import React from "react";
import { Link } from "react-router-dom";
import { FaArrowAltCircleLeft } from "react-icons/fa";

import ToolTip from "../../../../components/sharedComponents/ToolTip";

const CourseStudioHeader = ({ courseDetails, courseDetailsLoading }) => {
  return (
    <>
      <header className="row mx-0 course-studio-header">
        <div className="col-12 d-flex justify-content-between align-items-center">
          <Link to="/courses/list">
            <FaArrowAltCircleLeft />
          </Link>
          <h5 className="mb-0 ml-3">{courseDetails && courseDetails.name}</h5>
          <Link to={`/courses/preview/${courseDetails?._id}`}>
            <ToolTip title="Preview Course" placement="bottom">
              <button>Course Preview</button>
            </ToolTip>
          </Link>
        </div>
      </header>
      {/* <div className="align-items-center">
        {courseDetailsLoading && <CustomLoader />}
      </div> */}
    </>
  );
};
export default CourseStudioHeader;

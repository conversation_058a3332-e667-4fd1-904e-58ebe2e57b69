import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Combobox } from "react-widgets";

import AddBatchProblemDrawer from "./AddBatchProblemDrawer";
import AddBatchLessonDrawer from "./AddBatchLessonDrawer";
import AddBatchChallengeDrawer from "./AddBatchChallengeDrawer";
import AddSpeakerDrawer from "../../../../../components/Drawers/AddSpeaker/AddSpeakerDrawer";
import AddParticipantDrawer from "../../../../../components/Drawers/AddParticipantDrawer";
import AddBRoadmapDrawer from "./AddBRoadmapDrawer";
import CustomBreadcumb from "../../../../../components/sharedComponents/CustomBreadcum";
import CustomFilter from "../../../../../components/sharedComponents/CustomFilter";
import {
  getProblemTypeOptions,
  problemTypeOptions,
} from "../Problems/AddProblem/helper";
import {
  getBatchLessons,
  getBatchMentors,
  getBatchParticipants,
  getBatchProblems,
  getBatchQuiz,
  getCodeBatches,
} from "../../../actions";
import {
  batchContentRoleOptions,
  LimitOptions,
  StatusOptions,
} from "../helpers";
import { getOptions, getSort } from "../../../../../components/utils";

const BatchContentHeader = ({
  onChangeProblemType,
  setBatchContentStatus,
  batch,
  type,
  handleSelectedSpeaker,
  setRole,
  handleSelectedParticipant,
  tempParticipantDetail,
  setFilterQuery,
  setFilterLimit,
  setFilterStatus,
  filterLimit,
  filterQuery,
  filterStatus,
  mode,
  updateMode,
  showDrawer,
  onClose,
  open,
  tempRoadmap,
  batchId,
  setActiveTab,
  onChangeProblemOption,
  onChangeUserOption,
  setOpen,
  setOpenAddDay,
  openAddDay,
  selectedComponent,
  setSelectedComponent,
  setOpenContent,
  tempDay,
}) => {

  const dispatch = useDispatch();

  const { batchParticipantsList, batchProblemList } = useSelector(
    (state) => state.dayscode,
  );
  
  const [problemType, setProblemType] = useState(null);
  const [isOpen, setIsOpen] = useState(false);
  const [status, setStatus] = useState(null);
  const [mentorRole, setMentorRole] = useState();
  const [participantStatus, setParticipantStatus] = useState();
  const [participantLimit, setParticipantLimit] = useState();
  const [user, setUser] = useState();
  const [problem, setProblem] = useState();
  const [batchDetails, setBatchDetails] = useState([]);
  const [batches, setBatches] = useState();
  const [selectUser, setSelectUser] = useState(false);

  useEffect(() => {
    if (type === "Submissions") {
      dispatch(
        getBatchProblems({
          id: batchId,
        }),
      );
    }
    if (type === "Submission") {
      dispatch(getCodeBatches({ schedule: "all" })).then((res) => {
        setBatchDetails(res?.batches);
      });
    }
  }, [user, problem]);

  const items = [
    {
      title: "Batch",
      href: "/admin/days_code/batches",
    },
    {
      title: "Overview",
      onClick: () => {
        setActiveTab("overview");
      },
    },
  ];
  const handleOk = () => {
    if (type === "problem") {
      dispatch(
        getBatchProblems({
          id: batch,
          problem_type: problemType,
          status: status,
        }),
      );
    } else if (type === "lesson") {
      dispatch(
        getBatchLessons({
          id: batch,
          status: status,
        }),
      );
    } else if (type === "quiz") {
      dispatch(
        getBatchQuiz({
          id: batch,
          status: status,
        }),
      );
    } else if (type === "Mentor") {
      dispatch(
        getBatchMentors({
          batch,
          role: mentorRole,
        }),
      );
    } else if (type === "Participant") {
      setFilterLimit(participantLimit);
      setFilterStatus(participantStatus);
    } else if (type === "Submissions" || type === "Submission") {
      onChangeProblemOption(problem);
      onChangeUserOption(user);
    }
    setIsOpen(false);
  };
  const handleReset = () => {
    setProblemType();
    setStatus();
    setMentorRole();
    setParticipantLimit();
    setParticipantStatus();
    setBatches();
    setUser();
    setProblem();
    setSelectUser();
  };

  const getBatchOptions = (batches) => {
    const options = [];
    if (batches) {
      getSort(batches, "_id").map((item, i) => {
        return options.push({ batch: item.title, value: item._id });
      });
    }
    return options;
  };

  const getUsersOptions = (users) => {
    console.log(batchParticipantsList);
    const options = [];
    console.log(users, "userinn th bactch");
    if (users) {
      getSort(users, "firstName").map((item) => {
        return options.push({
          user: item.codeUser.user?.firstName,
          value: item._id,
        });
      });
    }
    return options;
  };

  const getProlemOptions = (problems) => {
    const options = [];
    if (problems) {
      getSort(problems, "day").map((item, i) => {
        return options.push({
          problem: `Day: ${i} | ${item.problemDetails?.title}`,
          value: item.problemDetails?._id,
        });
      });
    }
    return options;
  };

  const onChangeBatchOption = (e) => {
    setBatches(e);
    setSelectUser(true);
    dispatch(getBatchParticipants({ batch: e.value }));
    dispatch(getBatchProblems({ id: e.value }));
  };

  



  return (
    <>
      <div className='d-flex justify-content-between align-items-center batch-content-header'>
        <div className='problem-heading'>
          <h3>{type}</h3>
          <CustomBreadcumb items={items} />
        </div>
        <div className='d-flex '>
          <span className='mx-2'>
            {type === "problem" && (
              <CustomFilter
                isOpen={isOpen}
                setIsOpen={setIsOpen}
                handleOk={handleOk}
                handleReset={handleReset}
              >
                <div className='mb-2'>
                  <label className=' form-label'>Problem Type:</label>
                  <Combobox
                    data={getProblemTypeOptions(problemTypeOptions)}
                    dataKey={"value"}
                    textField='problem_type'
                    placeholder={"Select Problem Type"}
                    value={problemType}
                    onChange={(value) => {
                      setProblemType(value.value);
                    }}
                  />
                </div>
                <div className='mt-3 '>
                  <label className='form-label'>Status:</label>
                  <div className='d-flex'>
                    <div className='form-check'>
                      <input
                        className='form-check-input'
                        type='radio'
                        id='public'
                        value='public'
                        checked={status === "public"}
                        onChange={() => setStatus("public")}
                      />
                      <label className='form-check-label ' htmlFor='public'>
                        Public
                      </label>
                    </div>
                    <div className='form-check'>
                      <input
                        className='form-check-input'
                        type='radio'
                        id='draft'
                        value='draft'
                        checked={status === "draft"}
                        onChange={() => setStatus("draft")}
                      />
                      <label className='form-check-label' htmlFor='draft'>
                        Draft
                      </label>
                    </div>
                  </div>
                </div>
              </CustomFilter>
            )}
            {type === "lesson" && (
              <CustomFilter
                isOpen={isOpen}
                setIsOpen={setIsOpen}
                handleOk={handleOk}
                handleReset={handleReset}
              >
                <div className='mt-3 '>
                  <label className='form-label'>Status:</label>
                  <div className='d-flex'>
                    <div className='form-check'>
                      <input
                        className='form-check-input'
                        type='radio'
                        id='public'
                        value='public'
                        checked={status === "public"}
                        onChange={() => setStatus("public")}
                      />
                      <label className='form-check-label ' htmlFor='public'>
                        Public
                      </label>
                    </div>
                    <div className='form-check'>
                      <input
                        className='form-check-input'
                        type='radio'
                        id='draft'
                        value='draft'
                        checked={status === "draft"}
                        onChange={() => setStatus("draft")}
                      />
                      <label className='form-check-label' htmlFor='draft'>
                        Draft
                      </label>
                    </div>
                  </div>
                </div>
              </CustomFilter>
            )}
            {type === "quiz" && (
              <CustomFilter
                isOpen={isOpen}
                setIsOpen={setIsOpen}
                handleOk={handleOk}
                handleReset={handleReset}
              >
                <div className='mt-3 '>
                  <label className='form-label'>Status:</label>
                  <div className='d-flex'>
                    <div className='form-check'>
                      <input
                        className='form-check-input'
                        type='radio'
                        id='public'
                        value='public'
                        checked={status === "public"}
                        onChange={() => setStatus("public")}
                      />
                      <label className='form-check-label ' htmlFor='public'>
                        Public
                      </label>
                    </div>
                    <div className='form-check'>
                      <input
                        className='form-check-input'
                        type='radio'
                        id='draft'
                        value='draft'
                        checked={status === "draft"}
                        onChange={() => setStatus("draft")}
                      />
                      <label className='form-check-label' htmlFor='draft'>
                        Draft
                      </label>
                    </div>
                  </div>
                </div>
              </CustomFilter>
            )}
            {type === "Mentor" && (
              <CustomFilter
                isOpen={isOpen}
                setIsOpen={setIsOpen}
                handleOk={handleOk}
                handleReset={handleReset}
              >
                <div className='mb-2 '>
                  <label className='form-label'>Role:</label>
                  <Combobox
                    data={getOptions(batchContentRoleOptions, "role")}
                    dataKey={"value"}
                    textField='role'
                    placeholder={"role"}
                    value={mentorRole}
                    onChange={(value) => {
                      setMentorRole(value.value);
                    }}
                  />
                </div>
              </CustomFilter>
            )}
            {type === "Participant" && (
              <CustomFilter
                isOpen={isOpen}
                setIsOpen={setIsOpen}
                handleOk={handleOk}
                handleReset={handleReset}
              >
                <div className='mb-2 '>
                  <label className='form-label'>Status:</label>
                  <Combobox
                    data={getOptions(StatusOptions, "status")}
                    dataKey={"value"}
                    textField='status'
                    placeholder={"status"}
                    value={participantStatus}
                    onChange={(value) => {
                      if (value === "all") {
                        setParticipantStatus(null);
                      } else {
                        setParticipantStatus(value.value);
                      }
                    }}
                  />
                </div>
                <div className='mb-2 '>
                  <label className='mr-4 form-label'>Limit:</label>
                  <Combobox
                    data={getOptions(LimitOptions, "limit")}
                    dataKey={"value"}
                    textField='limit'
                    placeholder={"limit"}
                    value={participantLimit}
                    onChange={(value) => {
                      if (value === "all") {
                        setParticipantLimit(null);
                      } else {
                        setParticipantLimit(value.value);
                      }
                    }}
                  />
                </div>
                <div className=''>
                  <label className='form-label'>Search Name</label>
                  <input
                    className='form-control'
                    type='text'
                    name='search'
                    value={filterQuery}
                    onChange={(e) => setFilterQuery(e.target.value)}
                  />
                </div>
              </CustomFilter>
            )}
            {(type === "Submissions" || type === "Submission") && (
              <CustomFilter
                isOpen={isOpen}
                setIsOpen={setIsOpen}
                handleOk={handleOk}
                handleReset={handleReset}
              >
                {type === "Submission" ? (
                  <div className=''>
                    <label className='form-label'>Select Batch</label>
                    <Combobox
                      data={getBatchOptions(batchDetails)}
                      dataKey={"value"}
                      textField='batch'
                      placeholder={"Select Batch"}
                      value={batches}
                      onChange={(value) => onChangeBatchOption(value)}
                    />
                  </div>
                ) : (
                  ""
                )}
                {type === "Submissions" ||
                (type === "Submission" && selectUser) ? (
                  <>
                    <div className='mt-3'>
                      <label className='form-label'>Select User</label>
                      <Combobox
                        data={getUsersOptions(batchParticipantsList)}
                        dataKey={"value"}
                        textField='user'
                        placeholder={"Select User Name"}
                        value={user}
                        onChange={(value) => setUser(value.value)}
                        disabled={problem && true}
                      />
                    </div>
                    <div className='mt-3'>
                      <label className='form-label'>Select Problem</label>
                      <Combobox
                        data={getProlemOptions(batchProblemList)}
                        dataKey={"value"}
                        textField='problem'
                        placeholder={"Select Problem Name"}
                        value={problem}
                        onChange={(value) => setProblem(value.value)}
                        disabled={user && true}
                      />
                    </div>
                  </>
                ) : (
                  ""
                )}
              </CustomFilter>
            )}
          </span>
          <span className='mx-2'>
            {type === "problem" ? (
              <AddBatchProblemDrawer
                batch={batch}
                open={open}
                setOpen={setOpen}
                showDrawer={showDrawer}
                setOpenAddDay={setOpenAddDay}
                openAddDay={openAddDay}
                onClose={onClose}
              />
            ) : (
              ""
            )}
            {type === "lesson" ? (
              <AddBatchLessonDrawer
                batch={batch}
                open={open}
                setOpen={setOpen}
                showDrawer={showDrawer}
                setOpenAddDay={setOpenAddDay}
                openAddDay={openAddDay}
                onClose={onClose}
              />
            ) : (
              ""
            )}
            {type === "Participant" ? (
              <AddParticipantDrawer
                batch={batch}
                btnName='Add Participant'
                handleSelectedParticipant={handleSelectedParticipant}
                tempParticipantDetail={tempParticipantDetail}
              />
            ) : (
              ""
            )}
            {type === "quiz" ? (
              <AddBatchChallengeDrawer
                batch={batch}
                open={open}
                setOpen={setOpen}
                showDrawer={showDrawer}
                setOpenAddDay={setOpenAddDay}
                openAddDay={openAddDay}
                onClose={onClose}
              />
            ) : (
              ""
            )}
            {type === "Mentor" ? (
              <AddSpeakerDrawer
                btnName='Add Speaker'
                handleSelectedSpeaker={handleSelectedSpeaker}
                batch={batch}
              />
            ) : (
              ""
            )}
            {type === "RoadMap" ? (
              <AddBRoadmapDrawer
                mode={mode}
                updateMode={updateMode}
                showDrawer={showDrawer}
                onClose={onClose}
                open={open}
                tempRoadmap={tempRoadmap}
                batch={batch}
                selectedComponent={selectedComponent}
                setSelectedComponent={setSelectedComponent}
                setOpenContent={setOpenContent}
                tempDay={tempDay}
              />
            ) : (
              ""
            )}
          </span>
        </div>
      </div>
    </>
  );
};

export default BatchContentHeader;

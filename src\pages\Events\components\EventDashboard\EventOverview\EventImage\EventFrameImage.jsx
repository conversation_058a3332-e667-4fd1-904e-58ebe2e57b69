import React, { useEffect, useState } from "react";
import { useParams } from "react-router";
import { useDispatch, useSelector } from "react-redux";
import { Button } from "antd";

import { editEvent, getEventDetails } from "../../../../actions";
import noImage from "../../../../../../assets/images/bg/noImage.png";
import CustomLoader from "../../../../../../components/sharedComponents/CustomLoader";
import { uploadFrameImgToCloud } from "../../../../../../app/actions";

const EventFrameImage = () => {

  const dispatch = useDispatch();
  const { id } = useParams();
  
  const { editEventLoading } = useSelector(({ event }) => event) || {};

  const [frame, setFrame] = useState(noImage);
  const [isloading, setIsLoading] = useState(false);

  useEffect(() => {
    dispatch(getEventDetails(id)).then((res) => {
      if (res) {
        setFrame(res.data.eventBadgeImage);
      }
    });
  }, []);

  const handleImage = (e) => {
    const data = new FormData();
    data.append("file", e.target.files[0]);
    data.append("upload_preset", "user-profile-img");
    data.append("cloud_name", "datacode");
    dispatch(uploadFrameImgToCloud(data, setIsLoading)).then((res) => {
      if (res && res.success) {
        return setFrame(res.data);
      }
    });
  };

  const handleFrameUploadImage = () => {
    dispatch(editEvent({ _id: id, eventBadgeImage: frame }));
  };

  return (
    <div className='overview-event-images py-3'>
      <div className='event-images-header px-4'>
        <h1 className=''>You can add Event Images</h1>
        <span>Select images which you want to upload</span>
      </div>
      <div className='event-images border rounded-lg bg-white mx-3'>
        <div className='event-images-frame '>
          {isloading ? (
            <CustomLoader />
          ) : (
            <img src={frame} alt='Event Image' width={"100%"} height={400} />
          )}
        </div>
        <div className='event-images-array border'>
          <div className='d-flex flex-column'>
            <div className=''>
              <h2>Upload Image</h2>
              {/* <BannerImageUpload htmlFor="upload-btn-frame"/> */}
              <div className='row'>
                <div className='col-12 border-2 align-items-center'>
                  {isloading ? (
                    <CustomLoader />
                  ) : (
                    <>
                      <input
                        type='file'
                        id='upload-btn-frame'
                        onChange={(e) => handleImage(e)}
                        hidden
                      />
                      <label
                        className='btn upload-block my-3 align-items-center'
                        htmlFor='upload-btn-frame'
                      >
                        <i className='fas fa-upload mt-4' />
                      </label>
                    </>
                  )}
                  <Button
                    type='primary'
                    size='large'
                    className='upload-btn float-right'
                    onClick={handleFrameUploadImage}
                  >
                    {editEventLoading ? <CustomLoader /> : "Upload"}
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EventFrameImage;

import React from "react";
import { Link } from "react-router-dom";
import { FaTrash, FaEdit, <PERSON>a<PERSON>ye, FaUsers } from "react-icons/fa";

const CourseListCard = ({ course, handleDelete }) => {
  return (
    <>
      <div className="row mx-0">
        <div className="col-12 border rounded-lg p-3">
          <div className="row mx-0">
            <div className="col-10">
              <Link to={`/course/${course._id}/edit`}>
                <h6>{course.name}</h6>
              </Link>
            </div>
            <div className="col-2 d-flex justify-content-between">
              <Link to={`/course/${course._id}/users`}>
                <FaUsers className="mb-2 text-dark"/>
              </Link>
              <FaEdit />
              <FaTrash onClick={() => handleDelete(course._id)} />
              <FaEye />
            </div>
          </div>
        </div>
      </div>
    </>
  );
};
export default CourseListCard;

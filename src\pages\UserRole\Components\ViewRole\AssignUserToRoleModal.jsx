import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "reactstrap";
import { Combobox } from "react-widgets";

import { assignUserToRole, getUsers } from "../../actions/operations";
import { getSort } from "../../../../components/utils";
import CustomLoader from "../../../../components/sharedComponents/CustomLoader";
import SuccessConfirmationModal from "../../../../components/sharedComponents/SuccessConfirmationModal";


const AssignUserToRoleModal = ({ showModal, setShowModal, role, id }) => {
  
  const dispatch = useDispatch();
  const { viewRoleLoading,usersList,usersListLoading} = useSelector((state) => state.role)

  const [user, setUser] = useState();
  const [open, setOpen] = useState(false)
  const [search, setSearch] = useState("")

  useEffect(() => {
    const debounceTimeout = setTimeout(() => {
      dispatch(
        getUsers({
          search: search,
        }),
      )
    }, 800);
    return () => clearTimeout(debounceTimeout);
  }, [search , dispatch]);

 
  const getUsersOptions = (users) => {
    const options = [];
    if (users) {
      getSort(users, "firstName").map((item) => {
        return options.push({ user: item?.userName, value: item._id });
      });
    }
    return options;
  };

  const handleAssignUser = (role, userId) => {
    dispatch(assignUserToRole({ role, userId })).then((res)=>
  {
    setOpen(false);
    setShowModal(false);
  });
    
  }


  return (
    <div >
      <Modal centered isOpen={showModal} toggle={() => setShowModal(!showModal)}>
        <ModalHeader >
          <div className="modal1-header">Assign User To Role </div>
        </ModalHeader>
        <hr />
        <ModalBody>
          {viewRoleLoading ? (<><CustomLoader /></>) : (
            <>
              <ul>
                {role.users?.length ? (<>
                  <div className="mb-2">Already Assigned Users To This Role</div> <hr />
                  {role.users.map((item) => (
                    <div className="d-flex row ">
                      <li className="col-7 d-flex mx-1 justify-content-start">{item.firstName}</li>
                    </div>
                  ))}</>) : (<><div>There are no users .Add users from below</div></>)}
              </ul>
              <div className="d-flex justify-content-end align-items-stretch flex-wrap mt-3 mb-3">
                <input type="text" className=" form-control form-control-solid ps-15 " placeholder="Search Users" onChange={(e)=>setSearch(e.target.value)}/>
              </div>
              {!usersListLoading?
              <div className="mt-1">
                <Combobox
                  data={getUsersOptions(usersList)}
                  dataKey={"value"}
                  textField="user"
                  placeholder={"Select User"}
                  value={user}
                  onChange={(value) => setUser(value)}
                />
              </div> : (<CustomLoader/>)}
            </>
          )}
        </ModalBody>
        <ModalFooter className='d-flex justify-content-center align-items-center'>
          <Button
            color="success"
            onClick={() => setOpen(true)}
          >
            Assign User
          </Button>
        </ModalFooter>
      </Modal>
      <SuccessConfirmationModal
        open={open}
        toggle={() => setOpen(!open)}
        onSubmit={() => { handleAssignUser( id,user.value) }}
        message={"Are you sure want to assign selected user to this role"}
        title="Assign User"
        submitButtonName={"Yes"}
        submitButtonColor="success"
      />
    </div>
  );
}

export default AssignUserToRoleModal;
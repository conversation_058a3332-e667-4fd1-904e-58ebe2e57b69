import React, { useState } from "react";
import { useSelector } from "react-redux";
import { useParams } from "react-router";
import { Link } from "react-router-dom";
import moment from "moment";
import { FaCamera } from "react-icons/fa";
import { IoLocationOutline } from "react-icons/io5";

import { BackArrowCircleOutline } from "../../../../../assets/icons";
import ToolTip from "../../../../../components/sharedComponents/ToolTip";
import EventAttendees from "../EventAttendees";
import EventEmail from "../EventEmail";
import EventFeedbacks from "../EventFeedbacks";
import EventInsights from "../EventInsights";
import EventOverview from "../EventOverview";
import EventQuestions from "../EventQuestions";
import EventSideNav from "../EventSideNav";
import RegisterList from "../RegisterList";

const EventDashboard = () => {
  const { id } = useParams();

  const event = useSelector((state) => state?.event) || {};
  const { eventDetails } = event;

  const [activeTab, setActiveTab] = useState("overview");

  const renderActiveComponent = (activeTab) => {
    const componentDictionary = {
      overview: <EventOverview setActiveTab={setActiveTab} />,
      question: <EventQuestions setActiveTab={setActiveTab} />,
      registerList: <RegisterList setActiveTab={setActiveTab} />,
      feedback: <EventFeedbacks setActiveTab={setActiveTab} />,
      attendees: <EventAttendees setActiveTab={setActiveTab} />,
      email: <EventEmail setActiveTab={setActiveTab} />,
      insights: <EventInsights setActiveTab={setActiveTab} />,
    };

    return componentDictionary[activeTab];
  };

  return (
    <>
      <div className='row mx-0'>
        <div className='col-12 px-0 event-dashboard'>
          <div className='cover-image'>
            <div className='cover-img-util'>
              <FaCamera className='mr-2' />
              Add Cover
            </div>
            <div className='d-flex justify-content-center flex-column align-items-center cover-img-heading'>
              <div className='cover-img-logo '></div>
              <div> {eventDetails && eventDetails?.title}</div>
              <div className='date'>
                <span>
                  {moment(eventDetails?.start_date_time).format(
                    "DD MMMM YYYY HH:mm A",
                  )}
                  {" - "}
                  {moment(eventDetails?.end_date_time).format(
                    "DD MMMM YYYY HH:mm A",
                  )}
                </span>
                <span>
                  @ <IoLocationOutline />
                  Location
                </span>
              </div>
              <h6 className='heading-des'>
                This event is Hidden. Only Admins like you can see it.
              </h6>
            </div>
          </div>
          <div className='p-4 border-bottom d-flex flex-wrap justify-content-between'>
            <div className='d-flex text-wrap flex-wrap align-items-center'>
              <Link to='/event/dashboard'>
                <BackArrowCircleOutline className='back-btn' />
              </Link>
              <h1 className='ml-sm-3'>{eventDetails && eventDetails?.title}</h1>
            </div>
            <div className='text-right'>
              <a
                target={"_blank"}
                href={`https://www.datacode.in/event/${id}`}
                rel='noopener noreferrer'
              >
                <ToolTip title='Preview Event'>
                  <div className='btn btn-primary i'>Preview Event</div>
                </ToolTip>
              </a>
            </div>
          </div>
          <div className='row mx-0'>
            <div className='col-12 col-md-2 px-2 border-right'>
              <EventSideNav activeTab={activeTab} setActiveTab={setActiveTab} />
            </div>
            <div className='col-12 col-md-10'>
              {renderActiveComponent(activeTab)}
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default EventDashboard;

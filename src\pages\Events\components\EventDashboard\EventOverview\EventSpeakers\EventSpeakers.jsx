import React, { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { reduxForm } from "redux-form";
import { useParams } from "react-router-dom";
import {
  DropdownItem,
  DropdownMenu,
  DropdownToggle,
  UncontrolledDropdown,
} from "reactstrap";

import {
  addEventSpeaker,
  deleteEventSpeaker,
  getEventDetails,
  getEventSpeakers,
} from "../../../../actions";
import { setSpeakersList } from "../../../../../Speakers/actions";
import ImageFrame from "../../../../../../components/sharedComponents/ImageFrame";
import CustomLoader from "../../../../../../components/sharedComponents/CustomLoader";
import AddSpeakerDrawer from "../../../../../../components/Drawers/AddSpeaker/AddSpeakerDrawer";
import { RiDeleteBin6Line } from "react-icons/ri";
import ToolTip from "../../../../../../components/sharedComponents/ToolTip";

const EventSpeakers = () => {
  const dispatch = useDispatch();
  const { id } = useParams();

  const { eventSpeakersLoading, eventSpeakers, eventDetails } =
    useSelector((state) => state.event) || {};

  useEffect(() => {
    dispatch(setSpeakersList({ Speakers: [] }));
    dispatch(getEventSpeakers(id));
    dispatch(getEventDetails(id));
  }, [dispatch]);

  const handleSelectedSpeaker = (speakerId) => {
    dispatch(addEventSpeaker({ eventId: id, id: speakerId }));
  };

  return (
    <>
      <div className='row mx-0 p-2'>
        <div className='speaker-header'>
          <h3>Event Speakers List</h3>
          <AddSpeakerDrawer
            btnName={"Add New Speaker"}
            handleSelectedSpeaker={handleSelectedSpeaker}
          />
        </div>
        <div className='col-12 '>
          {eventSpeakersLoading ? (
            <CustomLoader />
          ) : (
            <>
              <div className='row mx-0 py-3'>
                {eventDetails?.speakers?.length ? (
                  eventDetails?.speakers?.map((speaker, i) => (
                    <div className='col-12 col-md-3 col-sm-4' key={i}>
                      <div className='speaker-card'>
                        <UncontrolledDropdown
                          setActiveFromChild
                          className='event-host-action-toggle'
                        >
                          <DropdownToggle tag='span' className='card-action'>
                            <ToolTip title='Remove Speaker' placement='bottom'>
                              <RiDeleteBin6Line className='dlt-card-btn' />
                            </ToolTip>
                          </DropdownToggle>
                          <DropdownMenu
                            className='dropdown-menu mt-3 card-shadow'
                            left='true'
                          >
                            <DropdownItem header>
                              <span>Actions</span>
                            </DropdownItem>
                            <DropdownItem
                              onClick={() =>
                                dispatch(
                                  deleteEventSpeaker({
                                    eventId: id,
                                    id: speaker._id,
                                  }),
                                )
                              }
                              className='remove-btn'
                            >
                              Remove Speaker
                            </DropdownItem>
                          </DropdownMenu>
                        </UncontrolledDropdown>
                        <ImageFrame imgUrl={speaker?.posterImage} />
                        <h5 className='mt-2'>{speaker?.speaker?.name}</h5>
                        <h5 className='mt-2'>{speaker?.talkTitle}</h5>
                        <p className='mb-0 text-truncate'>
                          {speaker.designation}
                        </p>
                      </div>
                    </div>
                  ))
                ) : (
                  <span className='col-12 no-speaker'>
                    No Speaker Added Into Event
                  </span>
                )}
              </div>
            </>
          )}
        </div>
      </div>
    </>
  );
};

export default reduxForm({
  form: "add-event-speaker",
})(EventSpeakers);

import React, { useEffect, useState } from "react";
import { useDispatch } from "react-redux";

import { blocksCardJson } from "../../../CourseStudioSideSection/BlocksSection/helper";
import ContentBlockLayout from "../ContentBlockLayout";
import { editContent } from "../../../../../actions";
import ToolTip from "../../../../../../../components/sharedComponents/ToolTip";

const EmbedBlock = ({ item, placeholder }) => {
  
  const dispatch = useDispatch();

  const [url, setUrl] = useState("");
  const data = blocksCardJson.find((i) => i.type === item.type);

  useEffect(() => {
    setUrl(item.payload);
  }, []);

  const handleSubmit = (e) => {
    e.preventDefault();
    const data = {
      id: item._id,
      payload: url,
    };
    dispatch(editContent(data));
  };

  const headerHtml = () => {
    return (
      <>
        <div className="embeds-block-header d-flex justify-content-between">
          <div>
            <div className="d-flex embeds-block-title">
              <i className={data.icon} />
              <p className="mb-0">{data.title}</p>
            </div>
            <p className="mb-0">{data.description}</p>
          </div>
        </div>
      </>
    );
  };

  return (
    <>
      <ContentBlockLayout
        item={item}
        blockHeader={headerHtml()}
        previewVisibility={false}
      >
        <div className="embeds-block-body px-4 py-3">
          <form onSubmit={handleSubmit}>
            <div className="form-group">
              <input
                type="text"
                className="form-control"
                placeholder={placeholder}
                value={url}
                onChange={(e) => setUrl(e.target.value)}
              />
            </div>
            <div className="d-flex justify-content-end">
              <ToolTip title="Embed link" placement="bottom">
                <button type="submit" className="btn btn-dark">
                  Embed Link
                </button>
              </ToolTip>
            </div>
          </form>
        </div>
      </ContentBlockLayout>
    </>
  );
};

export default EmbedBlock;

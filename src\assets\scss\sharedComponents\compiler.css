.language {
  display: inline !important;
  width: 150px;
}

.heading {
  font-size: 20px;
}

.source {
  width: 100% !important;
  background: rgba(204, 204, 204, 0.3215686275);
  font-size: 15px;
  resize: vertical;
}
.source textarea {
  min-height: 350px;
}

.input {
  background: rgba(204, 204, 204, 0.3215686275);
  width: 100%;
  min-height: 50px;
  resize: vertical;
  font-size: 17px;
}

.output {
  background: rgba(204, 204, 204, 0.3215686275);
  width: 100%;
  height: 100%;
  position: relative;
  top: 7.6%;
  font-size: 18px;
  right: 0;
  resize: none;
}
.output textarea {
  min-height: 150px;
}

.input-check .label {
  cursor: pointer;
}
.input-check .input {
  cursor: pointer;
}

.reset-compiler {
  cursor: pointer;
}

.code-editor-textarea textarea {
  max-height: none !important;
}

.code-editor .line {
  display: table-row;
}
.code-editor .editorLineNumber {
  display: table-cell;
  text-align: left;
  padding-right: 1em;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  opacity: 0.5;
}
.code-editor .editorText {
  display: table-cell;
}/*# sourceMappingURL=compiler.css.map */
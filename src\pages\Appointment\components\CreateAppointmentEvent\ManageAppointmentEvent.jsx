import React, { useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { useParams } from 'react-router'

import { getAppointmentEventDetails } from '../../actions'
import ManageMentorAvailability from './ManageMentorAvailability'
import CreateAppointmentEventForm from './CreateAppointmentEventForm'
import AppointmentEventNavBar from './AppointmentEventNavBar'
import AddAppointmentMentor from './AddAppointmentMentor'

const ManageAppointmentEvent = () => {

  const dispatch = useDispatch()
  const { id } = useParams()
  
  const { appointmentEventDetails, appointmentEventDetailsLoading } = useSelector(({ appointment }) => appointment) || {};
  
  const [activeTab, setActiveTab] = useState("settings");

  useEffect(() => {
    dispatch(getAppointmentEventDetails(id))
  }, [dispatch , id])

  const renderActiveComponent = (activeTab) => {
    const componentDictionary = {
      settings: (
        <>       
            <CreateAppointmentEventForm
              setActiveTab={setActiveTab}
              appointmentEventDetails={appointmentEventDetails}
              appointmentEventDetailsLoading={appointmentEventDetailsLoading}
            />
         
        </>
      ),
      availability: (
        <ManageMentorAvailability appointmentEventDetails={appointmentEventDetails} setActiveTab={setActiveTab} />
      ),
      addmentor: (
        <AddAppointmentMentor id={id} appointmentEventDetails={appointmentEventDetails} setActiveTab={setActiveTab} />
      )
    };

    return componentDictionary[activeTab];
  };

  return (
    <>
      <div className="row mx-0">
        <div className="col-12 px-0">
          <div className='row mx-0'>
            <div className="col-12 col-md-2 border-right">
              <AppointmentEventNavBar activeTab={activeTab} setActiveTab={setActiveTab} />
            </div>
            <div className="col-12 col-md-10">
              {renderActiveComponent(activeTab)}
            </div>
          </div>
        </div>
      </div>
    </>
  )
}

export default ManageAppointmentEvent
import React, { useEffect, useState } from "react";
import { useParams } from "react-router";
import { MdDelete<PERSON>orever } from "react-icons/md";
import { useDispatch, useSelector } from "react-redux";
import { Link } from "react-router-dom";
import Parser from "html-react-parser";
import { FaCameraRetro, FaEdit } from "react-icons/fa";

import {
  editEvent,
  getEventDetails,
  getEventRegistrations,
} from "../../../../actions";
import BannerImageUpload from "../../../../../../components/sharedComponents/BannerImageUpload";
import SpeakersCard from "./SpeakersCard";
import HostCard from "./HostCard";
import EventStats from "./EventStats";
import SponsorsCard from "./SponsorsCard";
import CustomLoader from "../../../../../../components/sharedComponents/CustomLoader";
import EventRegistrationChart from "./EventRegistrationChart";
import TextEditor from "../../../../../../components/sharedComponents/TextEditor";

const EventGeneral = ({ setActiveTab }) => {

  const dispatch = useDispatch();
  const { id } = useParams();

  const {
    eventDetailsLoading,
    eventDetails,
    eventCountDetails,
    // eventRegistrationsList,
  } = useSelector((state) => state.event) || {};
  
  const [descriptionText, getDescriptionText] = useState("<p></p>");
  const [uplodedImg, setUploadedImg] = useState("");
  const [preview, setIspreview] = useState(true);
  // const [eventUserData, setEventUserData] = useState();
  const [dayData, setDayData] = useState([]);


  useEffect(() => {
    dispatch(getEventDetails(id));
    dispatch(getEventRegistrations({ event: id, limit: "all" })).then((res) => {
      registrationDataByDay(res?.data);
    });
  }, [id, dispatch]);

  useEffect(() => {
    console.log(eventCountDetails);
    getDescriptionText(eventDetails?.description);
    if (eventDetails?.thumbnail) {
      setUploadedImg(eventDetails?.thumbnail);
    }
    console.log(eventDetails);
  }, [eventDetails, eventCountDetails]);

  const handleImageUpload = (image) => {
    setUploadedImg(image);
    if (eventDetails) {
      const updatedEvent = { ...eventDetails, thumbnail: image };
      dispatch(editEvent(updatedEvent));
    }
  };

  const handleDescriptionText = (text) => {
    getDescriptionText(text);
  };
  // useEffect(() => {
  //   if (Object.keys(eventDetails).length != 0) {
  //     console.log(eventDetails, "event");
  //     const event = { ...eventDetails };
  //     event["thumbnail"] = uplodedImg;
  //     dispatch(editEvent(event));
  //   }
  // }, []);

  const registrationDataByDay = (data) => {
    const registrationCounts = {};

    Array.isArray(data)&&data.forEach((item) => {
      const date = new Date(item.createdAt);
      const day = date.toISOString().split("T")[0];
      registrationCounts[day] = (registrationCounts[day] || 0) + 1;
    });
    const dayData = Object.entries(registrationCounts).map(([date, count]) => {
      return {
        date,
        count,
      };
    });
    setDayData(dayData);
    console.log("registrationDataByDay", dayData);
  };

  const onSave = () => {
    const event = { ...eventDetails };
    event["description"] = descriptionText;
    dispatch(editEvent(event)).then((res) => {
      if (res && res.success) {
        dispatch(getEventDetails(event._id));
      }
    });
    setIspreview(true);
  };
  return (
    <>
      <div className='row mx-0 event-general p-4'>
        {eventDetailsLoading ? (
         <div className="col-12 d-flex justify-content-center" >
    <CustomLoader />
  </div>
        ) : (
          <>
            <div className='col-12 d-flex flex-wrap align-items-center my-4'>
              <div className='stats mr-4'>Event States</div>
              <div className='d-flex flex-column ml-sm-4 sub-desc'>
                <Link href='https://www.datacode.in/datacod-event-24'>
                  {" "}
                  https://www.datacode.in/datacod-event-24
                </Link>
                <div className='text'>
                  Send your Event Participants directly to this Event Profile
                  URL.
                </div>
              </div>
            </div>
            <div className='col-12 px-0 overflow-auto'>
              <div className='row mx-0'>
                <div className='col-sm-3 m-1'>
                  <EventStats
                    heading={"Event Registrations"}
                    backgroundColor='#E5ECF6'
                    number={eventCountDetails?.totalRegistrations}
                  />
                </div>
                <div className='col-sm-3 m-1'>
                  <EventStats
                    heading={"Event Guests"}
                    backgroundColor='#E3F5FF'
                    number={eventCountDetails?.totalGuests}
                  />
                </div>
                <div className='col-sm-3 m-1'>
                  <EventStats
                    heading={"RSVP"}
                    backgroundColor='#E5ECF6'
                    number={eventCountDetails?.totalAttendies}
                  />
                </div>
              </div>
            </div>
            <div className=' w-100 bg-white border rounded-15  my-3 p-0 p-sm-4'>
              <h3 className='border-bottom text-wrap text-left text-sm-center pb-2'>
                Event Registration Chart
              </h3>
              <EventRegistrationChart dayData={dayData} />
            </div>
            <div className='col-12 p-0'>
              <div className='description p-4 rounded-15 '>
                {uplodedImg ? (
                  <div className='uploaded-img-preview border rounded-15   p-2 text-right'>
                    <span
                      className='cancel-img-btn'
                      onClick={() => {
                        setUploadedImg("");
                        dispatch(
                          editEvent({
                            ...eventDetails,
                            thumbnail: "",
                          }),
                        );
                      }}
                    >
                      <MdDeleteForever />
                    </span>
                    <img
                      src={uplodedImg}
                      alt='course-banner'
                      className='mx-auto img-fluid card-img'
                    />
                  </div>
                ) : (
                  <div className='my-3 img-section  rounded-15  px-3 py-3'>
                    <span className='edit p-2 rounded mb-4'>
                      <FaCameraRetro /> Edit Image/Video
                    </span>
                    <div>
                      <BannerImageUpload setUploadedImg={handleImageUpload} />
                    </div>
                  </div>
                )}
                <div>
                  <p className='my-4 event-desc'>Event Description</p>
                </div>
                {!preview ? (
                  <>
                    <div className='border my-3'>
                      {eventDetails ? (
                        <TextEditor
                          handleTextEditor={handleDescriptionText}
                          text={eventDetails.description}
                        />
                      ) : (
                        <TextEditor
                          handleTextEditor={handleDescriptionText}
                          text={descriptionText}
                        />
                      )}
                    </div>
                  </>
                ) : (
                  <>
                    <div className=' border-dashed rounded-15 p-3 my-3 mx-0 row'>
                      <div className='col-11'>
                        {Parser(String(descriptionText))}
                      </div>
                      <div className='col-1 d-flex justify-content-end'>
                        <FaEdit onClick={() => setIspreview(false)} />
                      </div>
                    </div>
                  </>
                )}
                <div className='d-flex justify-content-end pb-2'>
                  {!preview && (
                    <button
                      onClick={() => onSave()}
                      type='submit'
                      className='btn btn-primary mr-3'
                    >
                      Save
                    </button>
                  )}
                  {!preview && (
                    <button
                      onClick={() => setIspreview(true)}
                      type='submit'
                      className='btn btn-light'
                    >
                      Discard
                    </button>
                  )}
                </div>
              </div>
            </div>
            <div className='col-12 mt-4 p-0'>
              <HostCard eventDetails={eventDetails} />
            </div>
            <div className='col-12 mt-4 p-0'>
              <SpeakersCard
                setActiveTab={setActiveTab}
                eventDetails={eventDetails}
              />
            </div>
            <div className='col-12 mt-4 p-0'>
              <SponsorsCard
                eventDetails={eventDetails}
                setActiveTab={setActiveTab}
              />
            </div>
          </>
        )}
      </div>
    </>
  );
};

export default EventGeneral;

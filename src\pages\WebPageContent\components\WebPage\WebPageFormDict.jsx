import React from "react";

import { AboutUsBlockFormFields, FaqBlock<PERSON><PERSON>Fields, HeroSectionBlockFormFeilds } from "./WebPageBlockFormLib";

const WebPageFormDict = ({ block, formik }) => {
  const renderPageContentForm = (block) => {
    const formDict = {
      faq: <FaqBlockFormFields formik={formik} />,
      herosection : <HeroSectionBlockFormFeilds formik={formik} />,
      aboutus : <AboutUsBlockFormFields formik={formik} />
    };
    return formDict[block];
  };
  return renderPageContentForm(block);
};

export default WebPageFormDict;

import React, { useEffect, useState } from "react";
import { useDispatch,useSelector } from "react-redux";
import { useParams, useNavigate } from "react-router-dom";
import { Combobox } from "react-widgets";
import moment from "moment";
import { Field, getFormValues, reduxForm } from "redux-form";

import LayoutContainer from "../LayoutContainer";
import { getOptions } from "../../../../../components/utils";
import {
  batchCategoryOptions,
  batchLevelOptions,
  batchStatusOptions,
  batchTypeOptions,
  registrationStatusOptions,
} from "../helpers";
import {
  required,
  validateBatchDates,
} from "../../../../../components/utils/validators";
import {
  renderInputField,
  renderSelectField,
} from "../../../../../components/sharedComponents/ReduxFormFields";
import {
  createCodeBatch,
  editCodeBatch,
  getCodeBatchDetails,
} from "../../../actions";

const CreateCodeBatch = ({ reset, handleSubmit, submitting, initialize }) => {
  
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { id } = useParams();

  const [batchStatus, setBatchStatus] = useState();
  const [regStatus, setRegStatus] = useState();
  const [isEdit, setEdit] = useState(false);
  const [batchDetails, setBatchDetails] = useState();
  const [isPaid, setIsPaid] = useState(false);
  const formStates =
    useSelector((state) => getFormValues("create-batch")(state)) || {};


  useEffect(() => {
    if (id !== "new") {
      dispatch(getCodeBatchDetails(id)).then((res) => {
        if (res && res.success && res.batch) {
          const data = res.batch;
          data["status"] = res.batch && res.batch.status;
          data["reg_status"] = res.batch && res.batch.reg_status;
          initialize(data);
          setBatchStatus(res.batch.status);
          setRegStatus(res.batch.reg_status);
          setBatchDetails(res.batch);
          // getInputText(res.data.input)
          setEdit(true);
          data.type === "paid" ? setIsPaid(true) : setIsPaid(false);
        }
      });
    }
  }, [id, dispatch, initialize]);

  const onSubmit = (values) => {
    console.log(values);
    const data = {
      ...values,
      status: batchStatus?.value || "draft",
      reg_status: regStatus?.value || "close",
    };
    data["registration_end_date"] = data.reg_end_date;
    data["registration_start_date"] = data.reg_start_date;
    data["batch_start_date"] = data.start_date;
    data["batch_end_date"] = data.end_date;

    if (isEdit) {
      dispatch(editCodeBatch(data)).then((res) => {
        if (res.success) {
          navigate(`/admin/days_code/batch_dashboard/${res.batch._id}`);
        }
      });
    } else {
      dispatch(createCodeBatch(data));
    }
  };

  const calculateEndDate = (days, startDate) => {
    if (!startDate) {
      console.error("Invalid start date");
      return null;
    }

    // Calculate the end date
    const endDate = new Date(startDate);
    endDate.setDate(endDate.getDate() + Number(days));
    const formatted = moment(endDate).format("YYYY-MM-DD");
    // Return the end date in a readable format
    initialize({ ...formStates, end_date: formatted });
  };

  return (
    <>
      <LayoutContainer>
        <div className='row mx-0 solution-nav'>
          <div className='col-md-8 col-6 mx-0 d-flex p-2 align-items-center'>
            <h4 className='py-md-3 py-2 mb-0'>
              {batchDetails?.title || `Create New Batch`}
            </h4>
          </div>
          <div className='col-md-4 col-6 d-flex justify-content-end align-items-center'>
            <button
              onClick={() =>
                navigate("/admin/days_code/batches", { state: "batches" })
              }
              type='button'
              className='btn enroll-small-btn'
            >
              <small>Batch List</small>
            </button>
          </div>
        </div>
        <div className='row mx-0'>
          <div className='col-12'>
            <form
              className='py-3 px-md-5 px-1'
              onSubmit={handleSubmit(onSubmit)}
            >
              <small>
                Lorazepam, sold under the brand name Ativan among others, is a
                benzodiazepine medication. It is used to treat anxiety
                disorders, trouble sleeping, severe agitation, active seizures
                including status epilepticus, alcohol withdrawal, and
                chemotherapy-induced nausea and vomiting
              </small>
              <Field
                type='text'
                name='title'
                label='Batch Title'
                placeholder=''
                component={renderInputField}
                validate={[required]}
              />
              <h3 className='mt-3 mb-n2'>Genral Settings</h3>
              <div className='row m-0 border rounded-lg my-2 p-md-3 p-0'>
                <div className='col-md-6 col-12'>
                  <div className='row mx-0'>
                    <div className='col-md-6 col-12 pl-md-0 pr-md-2'>
                      <Field
                        name='level'
                        label='level'
                        options={getOptions(batchLevelOptions, "level")}
                        textField='level'
                        placeholder={"Batch Level"}
                        component={renderSelectField}
                        validate={required}
                      />
                    </div>
                    <div className='col-md-4 col-12 pl-md-2 pr-md-0'>
                      <Field
                        type='number'
                        name='days'
                        label='Batch Days'
                        placeholder=''
                        min={0}
                        component={renderInputField}
                        validate={[required]}
                        onBlur={() =>
                          calculateEndDate(
                            formStates.days,
                            formStates.start_date,
                          )
                        }
                      />
                    </div>
                    <div className='col-md-10 col-12 pl-md-0 pr-md-2'>
                      <Field
                        type='textarea'
                        name='description'
                        label='Description'
                        placeholder=''
                        component={renderInputField}
                        validate={[required]}
                      ></Field>
                    </div>
                  </div>
                </div>
                <div className='col-md-6 col-12 '>
                  <div className='row mb-4'>
                    <div className='col-6'>
                      <Field
                        type='date'
                        name='start_date'
                        label='Batch Start Date'
                        placeholder=''
                        component={renderInputField}
                        validate={[required]}
                        onBlur={(formStates) => {
                          calculateEndDate(
                            formStates.days,
                            formStates.start_date,
                          );
                        }}
                      />
                    </div>
                    <div className='col-6'>
                      <Field
                        type='date'
                        name='end_date'
                        label={`Batch End Date`}
                        placeholder=''
                        component={renderInputField}
                        validate={[required]}
                        isDisabled={formStates.start_date && true}
                      />
                    </div>
                  </div>
                  <div className='row mb-4'>
                    <div className='col-6 mt-3'>
                      <label className='mr-4 form-label'>Status:</label>
                      <div className=''>
                        <Combobox
                          data={getOptions(batchStatusOptions, "status")}
                          dataKey={"value"}
                          textField='status'
                          placeholder={"Batch Status"}
                          value={batchStatus}
                          onChange={(value) => setBatchStatus(value)}
                        />
                      </div>
                    </div>
                    <div className='col-6'>
                      <div className=''>
                        <Field
                          name='category'
                          label='Category'
                          placeholder='category'
                          options={getOptions(batchCategoryOptions, "category")}
                          textField={"category"}
                          component={renderSelectField}
                          validate={[required]}
                        />
                      </div>
                    </div>
                    <div className='col-6 '>
                      <div className=''>
                        <Field
                          name='type'
                          options={getOptions(batchTypeOptions, "type")}
                          label='Type'
                          textField='type'
                          placeholder={"Batch type"}
                          component={renderSelectField}
                          validate={[required]}
                          onChange={(value) => {
                            if (value === "paid") {
                              setIsPaid(true);
                            } else {
                              setIsPaid(false);
                            }
                          }}
                        />
                      </div>
                    </div>
                    {isPaid && (
                      <div className='col-6 '>
                        <Field
                          type='number'
                          name='fees'
                          label='Batch Fees'
                          placeholder='₹ INR'
                          min={1}
                          component={renderInputField}
                          validate={[required]}
                        />
                      </div>
                    )}
                  </div>
                </div>

                {/* <div className="my-3">
                  <label className="form-label text-left pt-5">Constrains {`(${constrainsText.length - 8})`}</label>
                  <div className="mb-3 border ">
                    {(isEdit && problemDetails && problemDetails.constrains) &&
                      <TextEditor handleTextEditor={handleConstrainsText} text={problemDetails?.constrains ? constrainsText : ''} />
                    }
                    {(!isEdit) &&
                      <TextEditor handleTextEditor={handleConstrainsText} text={problemDetails?.constrains ? constrainsText : ''} />
                    }
                  </div>
                </div> */}
              </div>
              {isEdit ? (
                <div className=''>
                  <h3 className='mt-4 '>Manage Setting</h3>
                  <div className='border rounded-lg row pb-4 mx-0'>
                    <div className='col-md-6 col-12  '>
                      <div className='mx-4 mt-4'>
                        <label className='mr-4 form-label'>
                          Registration Status:
                        </label>
                        <div className=''>
                          <Combobox
                            data={getOptions(
                              registrationStatusOptions,
                              "reg_status",
                            )}
                            dataKey={"value"}
                            textField='reg_status'
                            placeholder={"Registration Status"}
                            value={regStatus}
                            onChange={(value) => setRegStatus(value)}
                          />
                        </div>
                        <div className='row mb-4'>
                          <div className='col-6'>
                            <Field
                              type='date'
                              name='reg_start_date'
                              label='Registration Start Date'
                              placeholder=''
                              component={renderInputField}
                              validate={[required]}
                              onBlur={() =>
                                calculateEndDate(
                                  formStates.days,
                                  formStates.reg_start_date,
                                )
                              }
                            />
                          </div>
                          <div className='col-6'>
                            <Field
                              type='date'
                              name='reg_end_date'
                              label={`Registration End Date`}
                              placeholder=''
                              component={renderInputField}
                              validate={[required]}
                              // isDisabled={formStates.start_date && true}
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className='col-md-6 col-12 '>
                      <div className='row mx-0'>
                        <div className='col-md-6 col-12 pl-md-0 pr-md-2'>
                          <Field
                            type='number'
                            name='capacity'
                            label='No of Students Allows'
                            placeholder=''
                            component={renderInputField}
                            validate={[required]}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                ""
              )}

              <div className='row mb-4'>
                <div className='col-1  text-right'>
                  <button
                    type='submit'
                    className='btn custom-button'
                    disabled={submitting}
                  >
                    <span>{isEdit ? "Save" : "Create New Batch"}</span>
                  </button>
                </div>
              </div>
            </form>
          </div>
        </div>
      </LayoutContainer>
    </>
  );
};

export default reduxForm({
  form: "create-batch",
  fields: ["days"],
  validate: validateBatchDates,
})(CreateCodeBatch);

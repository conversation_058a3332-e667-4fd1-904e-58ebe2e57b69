import {
  SET_COURSES_LIST,
  SET_COURSE_DETAILS,
  SET_SECTIONS_LIST,
  SET_SECTION_DETAILS,
  SET_LESSONS_LIST,
  SET_LESSON_DETAILS,
  SET_LESSON_NAME,
  SET_CONTENTS_LIST,
  SET_CONTENT_DETAILS,
  SET_USER_COURSES_LIST,
  SET_COURSE_PROGRESS,
  SET_USER_PROGRESS,
  SET_COURSE_CURRICULUM,
  SET_COURSE_USER_ID,
  SET_COURSE_USERS_LIST,
  SET_USERS_BY_COURSE_LIST,
} from "../constants";

export const setCoursesList = (data) => ({
  type: SET_COURSES_LIST,
  payload: data,
});

export const setCourseDetails = (data) => ({
  type: SET_COURSE_DETAILS,
  payload: data,
})

export const setSectionsList = (data) => ({
  type: SET_SECTIONS_LIST,
  payload: data,
})

export const setSectionDetails = (data) => ({
  type: SET_SECTION_DETAILS,
  payload: data,
})

export const setLessonsList = (data) => ({
  type: SET_LESSONS_LIST,
  payload: data,
})

export const setLessonDetails = (data) => ({
  type: SET_LESSON_DETAILS,
  payload: data,
})

export const setLessonName = (data) => ({
  type: SET_LESSON_NAME,
  payload: data,
})

export const setContentsList = (data) => ({
  type: SET_CONTENTS_LIST,
  payload: data,
})

export const setContentDetails = (data) => ({
  type: SET_CONTENT_DETAILS,
  payload: data,
})

export const setUserCoursesList = (data) => ({
  type: SET_USER_COURSES_LIST,
  payload: data,
});
export const setCourseProgress = (data) => ({
  type: SET_COURSE_PROGRESS,
  payload: data,
});
export const setUserProgress = (data) => ({
  type: SET_USER_PROGRESS,
  payload: data,
});
export const setCourseCurriculum = (data) => ({
  type: SET_COURSE_CURRICULUM,
  payload: data,
});
export const setCourseUserId = (data) =>({
  type: SET_COURSE_USER_ID,
  payload: data,
});
 //----------------------COURSE STUDENTS-------------------
 export const setCourseUsersList = (data) => ({
  type: SET_COURSE_USERS_LIST,
  payload: data,
 })
export const setUsersByCourseList = (data) => ({
  type: SET_USERS_BY_COURSE_LIST,
  payload: data,
})

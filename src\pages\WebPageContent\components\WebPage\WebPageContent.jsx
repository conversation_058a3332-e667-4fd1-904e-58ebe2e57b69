import React, { useState } from "react";
import { useFormik } from "formik";

import WebPageFormDict from "./WebPageFormDict";

const WebPageContent = () => {
  
  const [category, setCategory] = useState("");
  const [block, setBlock] = useState("");

  const formik = useFormik({
    initialValues: {
      heading: "",
      description: "",
      category: "",
      block: "",
    },
    onSubmit: (values) => {
      alert(JSON.stringify(values, null, 2));
    },
  });
  return (
    <div className='webpagecontent padding-x padding-y'>
      <div className='webpage-filter'>
        <div className='row mx-0 justify-content-end'>
          <div className='col-lg-2 col-md-4 col-6'>
            <label htmlFor='category'>Category</label>
            <select
              name='category'
              id='category'
              className='form-control'
              onChange={(e) => {
                setCategory(e.target.value);
              }}
              value={category}
            >
              <option value='workshop page'>Workshop Page</option>
              <option value='community page'>Community Page</option>
              <option value='course page'>Course Page</option>
            </select>
          </div>
          <div className='col-lg-2 col-md-4 col-6'>
            <label htmlFor='block'>Block</label>
            <select
              className='form-control'
              name='block'
              id='block'
              onChange={(e) => {
                setBlock(e.target.value);
              }}
              value={block}
            >
              <option value='hero section'>hero section</option>
              <option value='home section'>home section</option>
              <option value='about section'>about section</option>
            </select>
          </div>
        </div>
      </div>

      <form className='webpage-form mt-3' onSubmit={formik.handleSubmit}>
        <WebPageFormDict formik={formik} block={"herosection"} />

        <div className='row mx-0 my-3'>
          <div className='col-12'>
            <button type='submit' className='btn-main mx-auto'>
              Save Changes
            </button>
          </div>
        </div>
      </form>
    </div>
  );
};

export default WebPageContent;

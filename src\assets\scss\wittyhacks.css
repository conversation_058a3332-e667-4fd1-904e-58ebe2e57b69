@import url("https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600&display=swap");
.padding-x {
  padding-left: 5%;
  padding-right: 5%;
}

.padding-y {
  padding-top: 7%;
  padding-bottom: 7%;
}

.wittyhacks .hero-section img {
  width: 100%;
}
.wittyhacks .details-panel {
  font-family: Poppins;
}
.wittyhacks .details-panel span {
  color: #535151;
  font-size: 32px;
  font-weight: 300;
  display: flex;
  justify-content: center;
  line-height: 48px;
}
.wittyhacks .details-panel h1 {
  font-size: 96px;
  font-weight: 600;
  color: #000000;
}
@media screen and (max-width: 700px) {
  .wittyhacks .details-panel h1 {
    font-size: 50px;
  }
}
.wittyhacks .details-panel .gradient-line {
  width: 135px;
  height: 9px;
  background-image: linear-gradient(to right, #4a2dff, #961dcf, #ec0ca0);
  border-radius: 30px;
}
.wittyhacks .details-panel p {
  font-weight: 300px;
  font-size: 24px;
  line-height: 45px;
  width: 90%;
  margin: auto;
}
@media screen and (max-width: 450px) {
  .wittyhacks .details-panel p {
    font-size: 18px;
  }
}
.wittyhacks .details-panel .logo-shadow {
  box-shadow: 2px 4px 10px 2px rgba(0, 0, 0, 0.2509803922) inset;
  margin: auto;
  border-radius: 26px;
  width: 660px;
  height: 250px;
}
@media screen and (max-width: 700px) {
  .wittyhacks .details-panel .logo-shadow {
    width: 450px;
    height: 100px;
  }
}
@media screen and (max-width: 480px) {
  .wittyhacks .details-panel .logo-shadow {
    width: 300px;
    height: 70px;
    border-radius: 10px;
  }
}
.wittyhacks .details-panel .logo-shadow img {
  width: 560px;
  height: 175px;
  top: 1552px;
}
@media screen and (max-width: 700px) {
  .wittyhacks .details-panel .logo-shadow img {
    width: 350px;
    height: 105px;
  }
}
@media screen and (max-width: 480px) {
  .wittyhacks .details-panel .logo-shadow img {
    width: 200px;
    height: 60px;
  }
}
.wittyhacks .details-panel .wittyhack-button {
  width: 350px;
  height: 70px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(91.49deg, #4a2dff -2.23%, #961dcf 51.19%, #ec0ca0 104.4%);
  color: #fff;
  font-family: Poppins;
  font-size: 22px;
  font-weight: 600;
  line-height: 45px;
  letter-spacing: 0.05em;
  text-align: center;
  transition: all 0.2s ease-in-out;
}
.wittyhacks .details-panel .wittyhack-button:hover {
  transform: scale(0.95);
}
@media screen and (max-width: 450px) {
  .wittyhacks .details-panel .wittyhack-button {
    font-size: 16px;
    height: 50px;
    width: 200px;
  }
}
.wittyhacks .frame-section {
  background-color: #f5f5f5;
  font-family: Poppins;
  margin: 0 auto;
}
@media screen and (max-width: 1100px) {
  .wittyhacks .frame-section {
    flex-direction: column;
    align-items: center;
  }
}
.wittyhacks .frame-section .frame-download {
  background-color: #000000;
  width: 600px;
  position: relative;
  height: 600px;
  border-radius: 10px;
}
@media screen and (max-width: 700px) {
  .wittyhacks .frame-section .frame-download {
    width: 400px;
    height: 400px;
  }
}
@media screen and (max-width: 500px) {
  .wittyhacks .frame-section .frame-download {
    width: 325px;
    height: 325px;
  }
}
.wittyhacks .frame-section .frame-download img {
  border-radius: 10px;
  width: 100%;
  position: absolute;
  height: 100%;
}
.wittyhacks .frame-section .frame-download .frame-img {
  z-index: 10;
}
.wittyhacks .frame-section .frame-download .user-frame-img {
  top: 0px;
}
.wittyhacks .frame-section label {
  border: 2px solid #7848f4;
  color: white;
  font-weight: 600;
  letter-spacing: 1px;
  width: 250px;
  border-radius: 10px;
  height: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #7968ce;
  cursor: pointer;
}
.wittyhacks .frame-section label:hover {
  background-color: #fff;
  border-color: #30006d;
  color: #30006d;
}
.wittyhacks .frame-section button {
  border: 2px solid #7968ce;
  background-color: #7968ce;
  color: white;
  font-weight: 600;
  width: 250px;
  border-radius: 10px;
  height: 50px;
}
.wittyhacks .frame-section button:hover {
  background-color: #fff;
  border-color: #30006d;
  color: #30006d;
}
.wittyhacks .frame-section input {
  display: none;
}
@media screen and (max-width: 1100px) {
  .wittyhacks .frame-section .form-elements {
    justify-content: center;
  }
}
@media screen and (max-width: 700px) {
  .wittyhacks .frame-section .form-elements {
    flex-direction: column;
    align-items: center;
  }
}
.wittyhacks .frame-section .digital-badge {
  width: calc(100% - 600px);
}
.wittyhacks .frame-section .digital-badge h1 {
  font-size: 62px;
  letter-spacing: 2px;
}
@media screen and (max-width: 500px) {
  .wittyhacks .frame-section .digital-badge h1 {
    font-size: 40px;
  }
}
.wittyhacks .frame-section .digital-badge p {
  letter-spacing: 1px;
}
@media screen and (max-width: 1100px) {
  .wittyhacks .frame-section .digital-badge {
    margin-top: 50px;
    flex-direction: column-reverse !important;
    width: 100%;
  }
}
@media screen and (max-width: 700px) {
  .wittyhacks .frame-section .digital-badge {
    margin-top: 50px;
    width: 70%;
  }
}
@media screen and (max-width: 500px) {
  .wittyhacks .frame-section .digital-badge {
    margin-top: 50px;
    padding: 0px !important;
    width: 100%;
  }
}
.wittyhacks .frame-section .digital-badge .hashtag {
  color: #5424e7;
}
.wittyhacks .frame-section .digital-badge .hashtag:hover {
  text-decoration: underline;
  cursor: pointer;
}
.wittyhacks .caption {
  font-family: Poppins;
  border-radius: 6px;
  letter-spacing: 1px;
  color: black;
  font-weight: 400;
  background: rgba(255, 255, 255, 0.35);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  border-radius: 10px;
  border: 1px solid rgba(255, 255, 255, 0.18);
}
.wittyhacks .caption .copy-clipboard {
  cursor: pointer;
}
.wittyhacks .caption .copy-clipboard:hover {
  font-size: large;
  color: #5424e7;
}
.wittyhacks .wallpaper-layout {
  background-color: #fff;
}
.wittyhacks .wallpaper-layout h1 {
  font-family: Poppins;
  font-weight: 600;
}
@media screen and (max-width: 500px) {
  .wittyhacks .wallpaper-layout h1 {
    font-size: 32px;
    justify-content: center;
  }
}
@media screen and (max-width: 700px) {
  .wittyhacks .wallpaper-layout h1 {
    font-size: 32px;
  }
}
@media screen and (max-width: 1000px) {
  .wittyhacks .wallpaper-layout .desktop-wall-layout {
    justify-content: center;
  }
}
@media screen and (max-width: 500px) {
  .wittyhacks .wallpaper-layout .desktop-wall-layout {
    justify-content: center;
  }
}
.wittyhacks .desktop-wallpaper {
  border-radius: 20px;
  font-family: Poppins;
  font-weight: 600;
}
.wittyhacks .desktop-wallpaper a:hover {
  opacity: 80%;
  cursor: pointer;
  transition: 0.3s ease-in-out;
}
.wittyhacks .desktop-wallpaper .desktop-image {
  width: 400px;
  height: 250px;
  border-radius: 20px;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
}
@media screen and (max-width: 1000px) {
  .wittyhacks .desktop-wallpaper .desktop-image {
    width: 600px;
    height: 350px;
  }
}
@media screen and (max-width: 700px) {
  .wittyhacks .desktop-wallpaper .desktop-image {
    width: 450px;
    height: 250px;
  }
}
@media screen and (max-width: 500px) {
  .wittyhacks .desktop-wallpaper .desktop-image {
    width: 350px;
    height: 200px;
    justify-content: center;
  }
}
.wittyhacks .desktop-wallpaper .desktop-image .download-wall {
  border-radius: 15px;
  font-size: 30px;
  width: 30px;
  background-color: #000000;
  color: #d4d2e4;
}
.wittyhacks .desktop-wallpaper .desktop-image .download-wall:hover {
  background-color: #5533ff;
}
.wittyhacks .mobile-image {
  width: 260px;
  height: 450px;
  border-radius: 10px;
  background-size: cover;
  background-repeat: no-repeat;
}
@media screen and (max-width: 1000px) {
  .wittyhacks .mobile-image {
    width: 250x;
    height: 480px;
  }
}
@media screen and (max-width: 700px) {
  .wittyhacks .mobile-image {
    width: 250px;
    height: 440px;
  }
}
@media screen and (max-width: 500px) {
  .wittyhacks .mobile-image {
    width: 250px;
    height: 440px;
    justify-content: center;
  }
}
.wittyhacks .mobile-image .download-wall {
  border-radius: 15px;
  font-size: 30px;
  width: 30px;
  background-color: #000000;
  color: #d4d2e4;
}
.wittyhacks .mobile-image .download-wall:hover {
  background-color: #5533ff;
}/*# sourceMappingURL=wittyhacks.css.map */
import { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useParams } from "react-router-dom";
import Parse from "html-react-parser";

import { EditQuizTest} from "../../../../actions/operations";
import TextEditor from "../../../../../../components/sharedComponents/TextEditor";

const FAQsSection = () => {
  
  const dispatch = useDispatch();
  const {id} = useParams();

  const {currentQuizTest} = useSelector(state => state.quizTest);

  const [isEdit ,setIsEdit] =useState(false);
  const [faQs,setFaqs] =useState(currentQuizTest?.faqs ||'<p></p>');

  const handleSaveClick = () => {
    setIsEdit(false);
    dispatch(EditQuizTest({ quizTest: id, faqs: faQs }));
  };

  return (
    <div className="row mx-0 px-0 mb-4 w-100">
      <div className="col-12 px-0 py-2 d-flex align-items-baseline">
        <span>
          <h5>FAQs</h5>
        </span>
        <span>
          {isEdit ? <a onClick={handleSaveClick} className="m-2">Save</a> : <a onClick={() =>setIsEdit(true)} className="m-2">Edit</a>}
        </span>
      </div>
      <div className="col-12 p-4 faq-section">
        {!isEdit ? (
          <div className="p-2">{currentQuizTest && currentQuizTest.faqs && Parse(currentQuizTest?.faqs)}</div>
        ) : (
          currentQuizTest &&
          <TextEditor
            text={faQs}
            handleTextEditor={setFaqs}
          />
        )}
      </div>
    </div>
  );
};

export default FAQsSection;

import React from "react";
import { FaRegQuestionCircle } from "react-icons/fa";
import { MdOutlineDashboard } from "react-icons/md";
import { LiaUsersSolid } from "react-icons/lia";
import { MdOutlineThumbsUpDown } from "react-icons/md";
import { HiOutlineLightBulb } from "react-icons/hi";

import ToolTip from "../../../../components/sharedComponents/ToolTip";

const EventSideNav = ({ activeTab, setActiveTab }) => {
  return (
    <>
      <div className='row m-0 mt-4'>
        <div className='col-12'>
          <div className='event-dashboard-side-nav w-100 px-0 table-responsive table'>
            <ul>
              <ToolTip title='Overview of the event' placement='right'>
                <li
                  className={`text-nowrap ${
                    activeTab === "overview" ? "active" : ""
                  }`}
                >
                  <span
                    onClick={() => setActiveTab("overview")}
                    className={`text-nowrap ${
                      activeTab === "overview" ? "active" : ""
                    }`}
                  >
                    <MdOutlineDashboard className='side-img' />
                    Overview
                  </span>
                </li>
              </ToolTip>
              <ToolTip title='Manage Attendees details' placement='right'>
                <li
                  className={`text-nowrap ${
                    activeTab === "attendees" ? "active" : ""
                  }`}
                >
                  <span
                    onClick={() => setActiveTab("attendees")}
                    className={`text-nowrap ${
                      activeTab === "attendees" ? "active" : ""
                    }`}
                  >
                    <LiaUsersSolid className='side-img' />
                    Attendees
                  </span>
                </li>
              </ToolTip>
              <ToolTip title='Questions' placement='right'>
                <li
                  className={`text-nowrap ${
                    activeTab === "question" ? "active" : ""
                  }`}
                >
                  <span
                    onClick={() => setActiveTab("question")}
                    className={`text-nowrap ${
                      activeTab === "question" ? "active" : ""
                    }`}
                  >
                    <FaRegQuestionCircle className='side-img' />
                    Questions
                  </span>
                </li>
              </ToolTip>
              <ToolTip title='Feedbacks' placement='right'>
                <li
                  className={`text-nowrap ${
                    activeTab === "feedback" ? "active" : ""
                  }`}
                >
                  <span
                    onClick={() => setActiveTab("feedback")}
                    className={`text-nowrap ${
                      activeTab === "feedback" ? "active" : ""
                    }`}
                  >
                    <MdOutlineThumbsUpDown className='side-img' />
                    Feedbacks
                  </span>
                </li>
              </ToolTip>
              <ToolTip title='Email' placement='right'>
                <li
                  className={`text-nowrap ${
                    activeTab === "email" ? "active" : ""
                  }`}
                >
                  <span
                    onClick={() => setActiveTab("email")}
                    className={`text-nowrap ${
                      activeTab === "email" ? "active" : ""
                    }`}
                  >
                    <MdOutlineDashboard className='side-img' />
                    Email
                  </span>
                </li>
              </ToolTip>
              <ToolTip title='Insights' placement='right'>
                <li
                  className={`text-nowrap ${
                    activeTab === "insights" ? "active" : ""
                  }`}
                >
                  <span
                    onClick={() => setActiveTab("insights")}
                    className={`text-nowrap ${
                      activeTab === "insights" ? "active" : ""
                    }`}
                  >
                    <HiOutlineLightBulb className='side-img' />
                    Insights
                  </span>
                </li>
              </ToolTip>
            </ul>
          </div>
        </div>
      </div>
    </>
  );
};

export default EventSideNav;

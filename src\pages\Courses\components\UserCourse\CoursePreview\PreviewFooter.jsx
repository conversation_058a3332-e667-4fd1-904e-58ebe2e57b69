import React from "react";
import { useSelector } from "react-redux";
import { Progress } from "reactstrap";

import { IoIosArrowBack, IoIosArrowForward } from "react-icons/io";
import { CiCircleAlert } from "react-icons/ci";

const PreviewFooter = ({nextLesson, previousLesson, courseProgress, nexrPrevbuttonDisabled}) => {

  const {lessonDetails} = useSelector((state) => state.course) || {};

  return (
    <div className="footer row mx-0">
      <div className="footer-progress col-12 col-md-5 col-lg-5 col-sm-12">
        <div className="footer-progress-header">  
          <small className="mb-2 ">
            Your Progress <CiCircleAlert />
          </small>
          <small className="mr-5">{Math.round(courseProgress?.progress)}%</small>   
        </div>
        <Progress
         style={{
            height: "6px",
            backgroundColor: "#c3c3c3",
            width: "250px",
          }}
          value={courseProgress?.progress}
        />
      </div>
      <div className="page-switch float-left  col-12 col-md-7 col-lg-7 col-sm-12">
        <button type="btn" id="pre-btn" className="btn btn-sm fs-6" disabled={() =>nexrPrevbuttonDisabled()} onClick={() => previousLesson(lessonDetails?._id)}>
          <IoIosArrowBack /> Prev
        </button>
        <button type="btn" className="btn btn-sm fs-6" onClick={() => nextLesson(lessonDetails?._id)}  >
          Next <IoIosArrowForward />
        </button>
      </div>
    </div>
  );
};

export default PreviewFooter;
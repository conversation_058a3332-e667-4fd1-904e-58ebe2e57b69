import React, { useEffect, useState } from "react";
import { Link } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { GiTeacher } from "react-icons/gi";
import { FaChalkboardTeacher, FaEdit } from "react-icons/fa";
import { FaPeopleGroup, FaRectangleList } from "react-icons/fa6";
import { IoCodeSlash } from "react-icons/io5";
import { HiDocumentText } from "react-icons/hi2";
import { ImUsers } from "react-icons/im";
import { MdQuiz } from "react-icons/md";
import { BiSolidDetail } from "react-icons/bi";
import BatchLeaderboard from "./BatchLeaderboard";

import { getBatchLeaderboard, getBatchRoadmap } from "../../../actions";
import usePermissions from "../../../../../hooks/userPermission";

const BatchOverview = ({ batch }) => {
  
  const dispatch = useDispatch();
  const { hasPermission } = usePermissions();

  const { codeBatchDetails, roadmapList} =
    useSelector((state) => state.dayscode) || {};
  const [viewDetails, setVeiwDetails] = useState(false);
  
  useEffect(() => {
    if(codeBatchDetails._id){
    dispatch(getBatchLeaderboard(codeBatchDetails._id));
    dispatch(getBatchRoadmap(codeBatchDetails._id))
    }
  }, [codeBatchDetails, dispatch]);

  return (
    <>
      <div className='overview-content'>
        <div
          className='py-3 px-4 d-flex justify-content-between
        align-items-center overview-header'
        >
          <div>
            <div className='row'>
              <h2 className='overview-batch-title'>
                <span className='mr-4'>
                  <GiTeacher />
                </span>
                {codeBatchDetails?.title}
              </h2>
              <div className='mx-2 my-2'>
                <span className='text-center mx-2 px-2 py-1 d-flex align-items-center status'>
                  Status:{" "}
                  {codeBatchDetails?.status?.charAt([0]).toUpperCase() +
                    codeBatchDetails?.status?.slice(1)}
                </span>
              </div>
              <div className=' my-2'>
                {hasPermission("batch", "edit") && <Link
                  to={`/admin/days_code/batch/${batch}`}
                  state={"batches"}
                  className='px-2 py-1 d-flex align-items-center status batch-edit'
                >
                  <FaEdit className='mr-1' /> Edit
                </Link>}
              </div>
            </div>
            <div className='d-flex flex-column'>
              <div className='d-flex'>
                <p className='ml-5 pr-2 mb-0 pb-0'>
                  <b>Registration Start Date: </b>
                  {codeBatchDetails.reg_start_date}
                </p>
                <span>||</span>
                <p className=' px-2 mb-0 pb-0'>
                  <b>Registration End Date: </b>
                  {codeBatchDetails.reg_end_date}
                </p>
              </div>
              <div className='d-flex'>
                <p className='ml-5 pr-2 mb-0 pb-0'>
                  <b>Start Date: </b>
                  {codeBatchDetails.start_date}
                </p>
                <span>||</span>
                <p className='px-2 mb-0 pb-0'>
                  <b>End Date: </b>
                  {codeBatchDetails.end_date}
                </p>
              </div>
            </div>
          </div>
          <div>
            <span className='header-days'>Day: {codeBatchDetails.days}/30</span>
          </div>
        </div>
        <div className='m-4 p-3 overview-user-content'>
          <div className='d-flex justify-content-between align-items-center w-100'>
            <h3 className=''>User Content</h3>
            <span>
              <span className='h4'>
                {/* View All <IoIosArrowDropdown /> */}
                <ImUsers className='mx-2 user-content-hover-icons' />{" "}
                <FaPeopleGroup className='mx-2 user-content-hover-icons' />
                <FaChalkboardTeacher className='mx-2 user-content-hover-icons' />
                <IoCodeSlash className='mx-2 user-content-hover-icons' />
                <HiDocumentText className='mx-2 user-content-hover-icons' />
                <MdQuiz className='mx-2 user-content-hover-icons' />
              </span>
            </span>
          </div>
          <hr className='mx-n3' />
          <div className='w-100 row mx-1'>
            <div className=' border my-3 d-flex align-items-center col-md-4 justify-content-center flex-column overview-details'>
              <div>
                <span className='overview-user-content-icons'>
                  <FaPeopleGroup />
                </span>
                <span className='mx-2 d-inline'>Participants</span>
              </div>
              <span className='h3'>
                {codeBatchDetails.participants?.length}+
              </span>
            </div>
            <div className=' border my-3 d-flex align-items-center col-md-4 justify-content-center flex-column overview-details'>
              <div>
                <span className='overview-user-content-icons'>
                  <FaChalkboardTeacher />
                </span>
                <span className='mx-2 d-inline'>Mentors</span>
              </div>
              <span className='h3'>
                {codeBatchDetails.mentorAssign?.length}+
              </span>
            </div>
            <div className=' border my-3 d-flex align-items-center col-md-4 justify-content-center flex-column overview-details'>
              <div>
                <span className='overview-user-content-icons'>
                  <ImUsers />
                </span>
                <span className='mx-2 d-inline'>Capacity</span>
              </div>
              <span className='h3'>{codeBatchDetails.capacity}</span>
            </div>
          </div>

          {viewDetails ? (
            <>
              <div className='w-100 row mx-1'>
                <div className=' border my-3 d-flex align-items-center col-md-4 justify-content-center flex-column overview-details'>
                  <div>
                    <span className='overview-user-content-icons'>
                      <IoCodeSlash />
                    </span>
                    <span className='mx-2 d-inline'>Problems</span>
                  </div>
                  <span className='h3'>
                    {codeBatchDetails.problems?.length}+
                  </span>
                </div>
                <div className=' border my-3 d-flex align-items-center col-md-4 justify-content-center flex-column overview-details'>
                  <div>
                    <span className='overview-user-content-icons'>
                      <HiDocumentText />
                    </span>
                    <span className='mx-2 d-inline'>Lessons</span>
                  </div>
                  <span className='h3'>
                    {codeBatchDetails.lessons?.length}+
                  </span>
                </div>
                <div className=' border my-3 d-flex align-items-center col-md-4 justify-content-center flex-column overview-details'>
                  <div>
                    <span className='overview-user-content-icons'>
                      <MdQuiz />
                    </span>
                    <span className='mx-2 d-inline'>Quizes</span>
                  </div>
                  <span className='h3'>{codeBatchDetails.quizes?.length}+</span>
                </div>
              </div>
            </>
          ) : (
            ""
          )}

          <hr className='mx-n3' />
          <div className='d-flex justify-content-center align-items-center '>
            <button
              className='rounded  px-5 py-2 overview-details-buttons'
              onClick={() => {
                setVeiwDetails(!viewDetails);
              }}
            >
              <span>
                <FaRectangleList />
              </span>
              Veiw {viewDetails ? "Less" : "Complete"} Details
            </button>
          </div>
        </div>

        <div className='row w-100 d-flex justify-content-center '>
          <div className='col-md-5 border mx-3 w-100 overview-graph '>
            <h2 className='mt-3 d-flex justify-content-center'>
              BATCH LEADERBOARD
            </h2>
            <span>
              <BatchLeaderboard />
            </span>
          </div>
          <div className='col-md-5 border mx-3 w-100 overview-roadmap'>
            <h2 className='mt-3 d-flex justify-content-center'>ROADMAP</h2>
            <hr />
            <table className="table">
        <thead>
          <tr>
            <th>Day</th>
            <th>Title</th>
            <th>Topic</th>
          </tr>
        </thead>
        <tbody>
          {roadmapList
            .sort((a, b) => a.day - b.day)
            .map((roadmap) => {
              return (
                <tr key={roadmap._id}>
                  <td>{roadmap.day}</td>
                  <td>{roadmap.title}</td>
                  <td>{roadmap.topic}</td>
                </tr>
              );
            })}
        </tbody>
      </table>
          </div>
        </div>

        <div className='batch-content-details m-4 p-3'>
          <h2>
            <BiSolidDetail className='mx-2' />
            Batch Content Details
          </h2>
          <hr />
          <div className='row px-3 justify-content-around'>
            <div className='col-md-3 d-flex flex-column justify-content-center border mx-3 mt-2'>
              <h3 className='text-center '>
                Problems List
                <IoCodeSlash className='mx-2' />
              </h3>
              <span className='text-center h5 mb-2'>
                <b>Titles</b>
              </span>
              <hr className='mt-n1' />
              <div className='d-flex flex-column align-items-center mx-3'>
                <ul>
                  {codeBatchDetails.problems?.map((item, key) => {
                    return <li key={key}>{item.problemDetails.title} Day-{item.day}</li>;
                  })}
                </ul>
              </div>
            </div>
            <div className='col-md-3 d-flex flex-column justify-content-center border mx-3 mt-2'>
              <h3 className='text-center '>
                Lessons List
                <HiDocumentText className='mx-2' />
              </h3>
              <span className='text-center h5 mb-2'>
                <b>Titles</b>
              </span>
              <hr className='mt-n1' />
              <div className='d-flex flex-column align-items-center mx-3'>
                <ul>
                  {codeBatchDetails.lessons?.map((item, key) => {
                    return <li key={key}>{item?.lessonDetails?.name} | Day-{item.day}</li>;
                  })}
                </ul>
              </div>
            </div>
            <div className='col-md-3 d-flex flex-column justify-content-center border mx-3 mt-2'>
              <h3 className='text-center '>
                Quizes List
                <MdQuiz className='mx-2' />
              </h3>
              <span className='text-center h5 mb-2'>
                <b>Titles</b>
              </span>
              <hr className='mt-n1' />
              <div className='d-flex flex-column align-items-center mx-3'>
                <ul>
                  {codeBatchDetails.quizes?.map((item, key) => {
                    return <li key={key}>{item.quizDetails._id} | Day-{item.day}</li>;
                  })}
                </ul>
              </div>
            </div>
          </div>
        </div>
        <br />
      </div>
    </>
  );
};

export default BatchOverview;

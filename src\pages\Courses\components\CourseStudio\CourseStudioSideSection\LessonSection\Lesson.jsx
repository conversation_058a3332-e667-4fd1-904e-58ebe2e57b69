import React, { useState } from "react";
import {
  UncontrolledDropdown,
  DropdownToggle,
  DropdownMenu,
  DropdownItem,
} from "reactstrap";

const Lesson = ({
  lesson,
  index,
  lessonsCount,
  handleLessonOrder,
  handleLessionDelete,
  handleLessonDetails,
  lessonName,
}) => {
  const [showAction, setShowAction] = useState(false);

  return (
    <>
      <div
        onMouseEnter={() => setShowAction(true)}
        onMouseLeave={() => setShowAction(false)}
        onClick={() => handleLessonDetails(lesson._id)}
        className='lesson-item py-2'
      >
        <div className='row d-flex align-items-center'>
          <div className='col-10'>
            <p className='mb-0 px-4'>
              {lesson.name === "" ? `Untitled Lesson` : lesson.name}
            </p>
          </div>
          <div className='col-2 text-center'>
            {showAction && (
              <UncontrolledDropdown setActiveFromChild>
                <DropdownToggle tag='a'>
                  <i className='far fa-ellipsis-v' />
                </DropdownToggle>
                <DropdownMenu>
                  {lessonsCount > 1 &&
                    (index > 0 && index < lessonsCount - 1 ? (
                      <>
                        <DropdownItem
                          onClick={() => handleLessonOrder(index, 1)}
                        >
                          <i className='fal fa-arrow-up mr-2' />
                          Move up
                        </DropdownItem>
                        <DropdownItem
                          onClick={() => handleLessonOrder(index, -1)}
                        >
                          <i className='fal fa-arrow-down mr-2' />
                          Move down
                        </DropdownItem>
                      </>
                    ) : index === 0 ? (
                      <DropdownItem
                        onClick={() => handleLessonOrder(index, -1)}
                      >
                        <i className='fal fa-arrow-down mr-2' />
                        Move down
                      </DropdownItem>
                    ) : (
                      <DropdownItem onClick={() => handleLessonOrder(index, 1)}>
                        <i className='fal fa-arrow-up mr-2' />
                        Move up
                      </DropdownItem>
                    ))}

                  <DropdownItem onClick={() => handleLessionDelete(lesson._id)}>
                    <i className='fal fa-trash mr-2' />
                    Delete
                  </DropdownItem>
                </DropdownMenu>
              </UncontrolledDropdown>
            )}
          </div>
        </div>
      </div>
    </>
  );
};

export default Lesson;

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from "antd";

import EventSpeakerFilter from "./EventSpeakerFilter";
import ToolTip from "../../sharedComponents/ToolTip";

const AddSpeakerDrawer = ({ handleSelectedSpeaker, btnName }) => {

  const [open, setOpen] = useState(false);
  const [placement, setPlacement] = useState("right");

  const showDrawer = () => {
    setOpen(true);
  };
  
  const onClose = () => {
    setOpen(false);
  };

  return (
    <>
      <Space>
        <ToolTip title="Add New Speaker" placement="bottom">
          <Button
            className="add-speaker-btn"
            type="primary"
            onClick={showDrawer}
          >
            {btnName}
          </Button>
        </ToolTip>
      </Space>
      <Drawer
        title="Select Datacode Speaker"
        placement={placement}
        closable={false}
        onClose={onClose}
        open={open}
        key={placement}
      >
        <EventSpeakerFilter handleSelectedSpeaker={handleSelectedSpeaker} />
      </Drawer>
    </>
  );
};
export default AddSpeakerDrawer;

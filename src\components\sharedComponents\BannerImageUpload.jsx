import React from "react";
import { useDispatch, useSelector } from "react-redux";
import PropTypes from "prop-types";

import { uploadImgToCloud } from "../../app/actions";
import CustomLoader from "./CustomLoader";

const BannerImageUpload = ({
  htmlFor = "upload-btn",
  setUploadedImg = () => {},
}) => {
  const dispatch = useDispatch();
  const { imgUploading } = useSelector((state) => state.app) || {};

  const handleImage = (e) => {
    const data = new FormData();
    data.append("file", e.target.files[0]);
    data.append("upload_preset", "user-profile-img");
    data.append("cloud_name", "datacode");
    dispatch(uploadImgToCloud(data)).then((res) => {
      if (res && res.success) {
        return setUploadedImg(res.data);
      }
    });
  };

  return (
    <>
      <div className='row'>
        <div className='col-12 border-2 align-items-center'>
          {imgUploading ? (
            <CustomLoader />
          ) : (
            <>
              <input
                type='file'
                id={htmlFor}
                onChange={(e) => handleImage(e)}
                hidden
              />
              <label
                className='btn upload-block my-3 align-items-center'
                htmlFor={htmlFor}
              >
                <i className='fas fa-upload mt-4' />
              </label>
            </>
          )}
        </div>
      </div>
    </>
  );
};

BannerImageUpload.propTypes = {
  setUploadedImg: PropTypes.func,
};

export default BannerImageUpload;

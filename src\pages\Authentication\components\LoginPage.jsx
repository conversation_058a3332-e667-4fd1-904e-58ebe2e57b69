import React, { useEffect } from "react";
// import authImg from "../../../assets/images/svg/login.svg";
import { useSelector, useDispatch  } from "react-redux";
import { initialize } from "redux-form";
import { useNavigate } from "react-router-dom";
import { useFormik } from "formik";

import { loginUser } from "../actions/operations";
import CustomLoader from "../../../components/sharedComponents/CustomLoader";
import ToolTip from "../../../components/sharedComponents/ToolTip";
import logo from "../../../assets/images/svg/datacode-logo.png";

// validation check
const validate = (values) => {
  const errors = {};

  if (!values.password) {
    errors.password = "Required";
  } else if (values.password.length > 20) {
    errors.password = "Must be 20 characters or less";
  }

  if (!values.email) {
    errors.email = "Required";
  } else if (!/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,4}$/i.test(values.email)) {
    errors.email = "Invalid email address";
  }

  return errors;
};

// main compoment
const LoginPage = () => {
  const dispatch = useDispatch();
  const { loginUserError, loginUserLoading } = useSelector(
    (state) => state.auth
  );
  const navigate = useNavigate();

  useEffect(() => {
    initialize(loginUserError);
    
    // when login already, move on home page direct..
    const localStorageItem = localStorage.getItem("currentUser")
      const sessionStorageItem = sessionStorage.getItem("currentUser")
      if ((localStorageItem !== null ) || (sessionStorageItem !== null) )
      navigate('/');
  }, [loginUserError]);

  const formik = useFormik({
    initialValues: {
      email: "",
      password: "",
      remember: false,
    },
    validate,
    onSubmit: (values) => {
      dispatch(
        loginUser({
          email: values.email,
          password: values.password,
          remember: values.remember,
        })
      ).then((res) => {
        if (res.success) {
          navigate("/");
        }
      });
    },
  });

  return (
    <section className="auth-page">
      {/* header */}
      <div className="auth-header px-3 py-1 shadow-sm">
        <img src={logo} alt="" />
      </div>
      <div className="row mx-0">
        <div className="col-md-7 px-0 col-12 d-none d-md-flex justify-content-center align-items-center auth-left">
          {/* <img src={authImg} alt="" /> */}
        </div>
        {/* login form side */}
        <div className="col-md-5 col-12 px-0 d-flex justify-content-center align-items-center auth-right">
          {/* main content */}
          <div className="auth-container">
            <div>
              <h1 className="text-center">Welcome Back</h1>
              <p className="text-center">Login to access your account</p>
            </div>
            {/* login form */}
            <form onSubmit={formik.handleSubmit}>
              {/* email */}
              <div className="form-group">
                <label htmlFor="email">Email address</label>
                <ToolTip title="enter your email" placement="top">
                  <input
                    type="email"
                    className="form-control"
                    id="email"
                    placeholder="Enter your Email"
                    onChange={formik.handleChange}
                    value={formik.values.email}
                  />
                </ToolTip>
                {formik.errors.email ? (
                  <div className="text-danger mt-1">{formik.errors.email}</div>
                ) : null}
              </div>
              {/* password */}
              <div className="form-group">
                <label htmlFor="password">Password</label>
                <ToolTip title="enter your password" position="top">
                  <input
                    type="password"
                    className="form-control"
                    id="password"
                    placeholder="Enter your Password"
                    onChange={formik.handleChange}
                    value={formik.values.password}
                  />
                </ToolTip>
                {formik.errors.password ? (
                  <div className="text-danger mt-1">
                    {formik.errors.password}
                  </div>
                ) : null}
                <div className="row mx-0">
                  {/* remember me */}
                  <div className="col-md-6 col-12 px-0">
                    <div className="form-check">
                    <ToolTip title="save login info" placement="bottom">
                      <input
                        className="form-check-input"
                        type="checkbox"
                        id="remember"
                        onChange={formik.handleChange}
                        value={formik.values.remember}
                      />
                      <label className="form-check-label" htmlFor="remember">
                        Remember me
                      </label>
                    </ToolTip>
                    </div>
                  </div>
                  {/* forgot password */}
                  <div className="col-md-6 col-12 px-0 text-md-right">
                  <ToolTip title="click to change password" placement="bottom">
                    <button className="btn btn-link">Forgot Password</button>
                    </ToolTip>
                  </div>
                </div>
              </div>
              {/* sign in button */}
              <div className="mt-4">
              <ToolTip title="login to access your account" placement="top">
                <button type="submit" className="btn-login">
                  {loginUserLoading ? <CustomLoader /> : <span>Sign In</span>}
                </button>
                </ToolTip>
                {loginUserError && (
                  <p className="mt-2 text-danger text-center">
                    {loginUserError}*
                  </p>
                )}
              </div>
            </form>
            <p className="text-center mt-4 d-flex align-items-center justify-content-center">
              Don't have an account ?{" "}
              <ToolTip title="create new account" placement="top">
              <button
                className="btn btn-link"
                onClick={() => window.open("https://datacode.in/#/signup")}
              >
                Sign Up
              </button>
              </ToolTip>

            </p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default LoginPage;

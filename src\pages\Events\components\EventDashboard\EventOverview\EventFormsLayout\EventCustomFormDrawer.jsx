import { useState } from "react";
import { <PERSON><PERSON>, <PERSON>er, Space } from "antd";

import ToolTip from "../../../../../../components/sharedComponents/ToolTip";
import EventRegFormLayout from "./EventRegFormLayout";

const EventCustomFormDrawer = ({
  tempEditField,
  isEdit,
  setEdit,
  formName,
  showDrawer,
  onClose,
  open
}) => {

  const [placement, setPlacement] = useState("right");

  // const onChange = (e) => {
  //   setPlacement(e.target.value);
  // };
  return (
    <>
      <Space>
        <ToolTip title="Add New Field" placement="bottom">
          <Button className="add-new-btn" type="primary" onClick={showDrawer}>
            Add New Field
          </Button>
        </ToolTip>
      </Space>
      <Drawer
        title="Add New Form Field"
        placement={placement}
        closable={false}
        onClose={onClose}
        open={open}
        key={placement}
      >
        <EventRegFormLayout
          onCloseDrawer={onClose}
          tempEditField={tempEditField}
          isEdit={isEdit}
          setEdit={setEdit}
          formName={formName}
          showDrawer={showDrawer}
        />
      </Drawer>
    </>
  );
};
export default EventCustomFormDrawer;

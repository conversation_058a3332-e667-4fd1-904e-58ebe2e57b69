import React, { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";

import { getQuizTestDetails } from "../../../actions";
import QuizTestQuestions from "./Question";
import QuizTestHeader from "./QuizTestHeader";
import QuizTestEmail from "./EmailTab/QuizTestEmail";
import QuizTestTaken from "./TestTaken/QuizTestTaken";
import QuizTestInvited from "./InvitedTab/QuizTestInvited";
import QuizReviewPending from "./ReviewPending/QuizReviewPending";
import CandidatesFeedback from "./CandidatesFeedback/CandidatesFeedback";
import QuizTestAnalytics from "./TestAnalytics/QuizTestAnalytics";
import TestQuestionAnalytics from "./QuestionAnalytics/TestQuestionAnalytics";
import QuizTestSideNavbar from "./QuizTestSideNavbar";
import QuizTestSideNavbarDrawer from "./Question/Drawers/QuizTestSideNavbarDrawer";
import QuizTestOverview from "./Overview";

const QuizTestDashboard = () => {

  const dispatch = useDispatch();
  const { id } = useParams();
  
  const { quizTestDetails } = useSelector(({ quizTest }) => quizTest) || {};

  const [activeTab, setActiveTab] = useState("overview");

  useEffect(() => {
    dispatch(getQuizTestDetails({ quizTest: id, mode: 'creator' }));
  }, [id, quizTestDetails])


  const renderActiveComponent = (activeTab) => {
    const componentDictionary = {
      overview: <QuizTestOverview setActiveTab={setActiveTab} activeTab={activeTab} />,
      questions: <QuizTestQuestions setActiveTab={setActiveTab} activeTab={activeTab} />,
      emailTab: <QuizTestEmail setActiveTab={setActiveTab} activeTab={activeTab} />,
      testTaken: <QuizTestTaken setActiveTab={setActiveTab} activeTab={activeTab} />,
      reviewPending: <QuizReviewPending setActiveTab={setActiveTab} activeTab={activeTab} />,
      invited: <QuizTestInvited setActiveTab={setActiveTab} activeTab={activeTab} />,
      CandidateFeedback: <CandidatesFeedback setActiveTab={setActiveTab} activeTab={activeTab} />,
      TestAnalytics: <QuizTestAnalytics setActiveTab={setActiveTab} activeTab={activeTab} />,
      QuestionAnalytics: <TestQuestionAnalytics setActiveTab={setActiveTab} activeTab={activeTab} />
    };

    return componentDictionary[activeTab];
  };

  return (
    <>
      <div className="row mx-0 ">
        <QuizTestHeader />
        <div className="col-lg-12 px-0">
          <div className="row mx-0 d-flex">
            <div className="col-lg-2 px-0 ">
              {/* <div className="quiztestsideNAvbar"><QuizTestSideNavbar setActiveTab={setActiveTab} activeTab={activeTab} /></div> */}
              <QuizTestSideNavbarDrawer setActiveTab={setActiveTab} activeTab={activeTab} />
            </div>
            <div className="col-lg-10 px-4 quiz-test-body">
              {renderActiveComponent(activeTab)}
            </div>
          </div>
        </div>

      </div>

    </>
  )
};

export default QuizTestDashboard;
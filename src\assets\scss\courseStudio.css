.course-studio-header {
  background-color: antiquewhite;
  height: 7vh;
}
.course-studio-header button {
  color: #fff;
  font-weight: 600;
  background-color: #673de6;
  padding: 5px 15px;
  border: 1px solid #673de6;
  border-radius: 5px;
}
.course-studio-header button:hover {
  background-color: transparent;
  color: #673de6;
}

.course-studio-side-section {
  width: 0% !important;
  left: 0%;
  transition: all 0.5s ease-in-out;
  display: block;
  padding: 0;
  margin: 0;
  background: #ffffff;
  position: relative;
  overflow-y: auto;
  overflow-x: hidden;
}

.course-studio-side-section.open {
  transition: all 0.5s ease-in-out;
  width: 30% !important;
  position: relative;
  left: 0%;
}

.course-studio-page-wrapper {
  height: 84.5vh;
  overflow: scroll;
  position: relative;
  right: 0;
  transition: all 0.5s ease-in-out;
  width: -webkit-fill-available;
}
.course-studio-page-wrapper::-webkit-scrollbar-track {
  border-radius: 10px;
  background-color: #f1efef;
}
.course-studio-page-wrapper::-webkit-scrollbar {
  width: 6px;
  height: 3px;
  background-color: #fff;
}
.course-studio-page-wrapper::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background-color: #c3c3c3;
}

.course-studio-side-section.open + .course-studio-page-wrapper {
  position: relative;
  left: 0%;
  right: 0%;
  width: 70% !important;
  transition: all 0.5s ease-in-out;
}

.toggle-icon {
  background: #000;
  border-radius: 50%;
  color: white;
  padding: 3px;
  position: relative;
  left: -1%;
  top: 10%;
  margin-left: 15px;
}

.course-studio-body-height {
  height: 84.5vh;
}

.course-studio-side-nav {
  text-align: center;
  border: 1px solid rgba(168, 166, 166, 0.5098039216);
  padding-left: 0;
  height: 84.5vh;
}
.course-studio-side-nav li {
  list-style: none;
  padding: 10px;
}
.course-studio-side-nav li i {
  font-size: 25px;
}
.course-studio-side-nav :hover {
  background-color: #dbeafe;
}
.course-studio-side-nav .active {
  background-color: #dbeafe;
}

.lesson-sections {
  overflow: hidden !important;
  height: 84.5vh !important;
  font-size: 16px;
}
.lesson-sections .lesson-header {
  padding: 10px;
  border-bottom: 1px solid rgba(168, 166, 166, 0.5098039216);
  height: 18vh;
}
.lesson-sections .lesson-header h3 {
  font-size: 20px;
}
.lesson-sections .section-list {
  overflow-y: scroll;
  overflow-x: hidden;
}
.lesson-sections .section-list::-webkit-scrollbar-track {
  border-radius: 10px;
  background-color: #f1efef;
}
.lesson-sections .section-list::-webkit-scrollbar {
  width: 5px;
  height: 5px;
  background-color: #fff;
}
.lesson-sections .section-list::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background-color: #c3c3c3;
}
.lesson-sections .lesson-list {
  height: 59.5vh;
}
.lesson-sections .lesson-list .section-list-item {
  font-size: 20px;
  padding: 10px 20px;
}
.lesson-sections .lesson-footer {
  position: relative;
  width: 100%;
  height: 7vh;
  display: flex;
  justify-content: flex-end;
  bottom: 0;
  background-color: ghostwhite;
  padding: 8px;
}

.section-title p {
  padding: 0 5px;
}
.section-title p:hover {
  background-color: #c3c3c3 !important;
  border-radius: 5px;
  color: blue;
  text-decoration: none;
}
.section-title input {
  padding: 0 10px;
  outline: none !important;
  background-color: aquamarine;
  border: none;
  border-radius: 5px;
}

.section-open p {
  background-color: #c3c3c3 !important;
  border-radius: 5px;
  color: blue;
  text-decoration: none;
}

.lesson-item:hover {
  background-color: #c3c3c3 !important;
  border-radius: 5px;
  color: blue;
  text-decoration: none;
}

.lesson-craft-header .lesson-heading {
  font-size: 32px;
}

.image-upload-preview img {
  width: 100%;
  height: 500px;
  -o-object-fit: cover;
     object-fit: cover;
}

.block-sections {
  overflow: hidden !important;
  height: 84.5vh !important;
  font-size: 16px;
}
.block-sections .block-header {
  padding: 10px;
  border-bottom: 1px solid rgba(168, 166, 166, 0.5098039216);
  height: 20vh;
}
.block-sections .block-header h3 {
  font-size: 20px;
}
.block-sections .block-list {
  overflow-y: scroll;
  overflow-x: hidden;
  height: 64.5vh;
}
.block-sections .block-list::-webkit-scrollbar-track {
  border-radius: 10px;
  background-color: #f1efef;
}
.block-sections .block-list::-webkit-scrollbar {
  width: 5px;
  height: 5px;
  background-color: #fff;
}
.block-sections .block-list::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background-color: #c3c3c3;
}

.block {
  border: 2px solid #e5e7eb;
  border-radius: 5px;
}
.block i {
  font-size: 25px;
  margin-right: 10px;
}
.block i:first-child {
  color: #e5e7eb;
}
.block:hover {
  border-color: #3b82f6 !important;
}
.block:hover i:first-child {
  color: #3b82f6;
}
.block .title {
  font-size: 15px;
}
.block .description {
  font-size: 10px;
  color: grey;
}

.block-drop-box {
  margin-top: 30px;
  background-color: #dbeafe;
  border: 1px dotted rgba(168, 166, 166, 0.5098039216);
  height: 60px;
  border-radius: 5px;
}

.setting-section {
  overflow: hidden !important;
  height: 84.5vh !important;
  font-size: 16px;
}
.setting-section .setting-header {
  padding: 10px;
  border-bottom: 1px solid rgba(168, 166, 166, 0.5098039216);
  height: 20vh;
}
.setting-section .setting-header h3 {
  font-size: 22px;
}
.setting-section .setting-list {
  overflow-y: scroll;
  overflow-x: hidden;
  height: 64.5vh;
}
.setting-section .setting-list::-webkit-scrollbar-track {
  border-radius: 10px;
  background-color: #f1efef;
}
.setting-section .setting-list::-webkit-scrollbar {
  width: 5px;
  height: 5px;
  background-color: #fff;
}
.setting-section .setting-list::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background-color: #c3c3c3;
}

.quiz-block {
  border-radius: 5px;
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
}
.quiz-block .quiz-header {
  padding: 10px 20px;
  background-color: #f9fafb;
}
.quiz-block .quiz-body {
  padding: 0px 20px 10px 20px;
}

.embeds-block-header {
  background-color: #f9fafb;
}
.embeds-block-header p {
  color: #6b7280;
}
.embeds-block-header .embeds-block-title i {
  color: red;
  font-size: 25px;
  margin-right: 5px;
}
.embeds-block-header .embeds-block-title p {
  font-size: 20px;
  font-weight: 600;
  color: black;
}

.video-responsive {
  overflow: hidden;
  padding-bottom: 56.25%;
  position: relative;
  height: 0;
  border-radius: 1.25rem;
}
.video-responsive iframe {
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  position: absolute;
}/*# sourceMappingURL=courseStudio.css.map */
import React from "react";
import moment from "moment";
import { Link } from "react-router-dom";
import Combobox from "react-widgets/Combobox";
import { statusOptions } from "../../../components/utils/selectOptions";

export const handlePermissionOption = (statusOptions, hasPermission) => {
  if (hasPermission('application-accept', 'update')) {
    return statusOptions
  }
  else {
    if (hasPermission('application-review', 'update')) {
      return statusOptions.filter((item) => (item.status == 'stage1' || item.status == 'stage2'))
    }
  }
}

const tableAction = (row, handleStatus, hasPermission) => {
  return (
    <div className='text-left d-flex'>
      {/* <i type="button" className="mr-2 fas fa-user-check"
        onClick={() => handleStatus(row._id, 'Approve')}
      />
      <i type="button" className="mr-2 fas fa-user-times"
        onClick={() => handleStatus(row._id, 'Reject')}
      /> */}
      <Combobox
        data={handlePermissionOption(statusOptions, hasPermission)}
        dataKey={"value"}
        textField='status'
        placeholder={"Select Status"}
        value={{ status: row.status }}
        onChange={(value) => handleStatus(value, row)}
      />
    </div>
  )
}
export const getColumns = (handleStatus, handleDeleteAction, hasPermission) => [
  {
    dataField: "_id",
    text: "",
    align: "center",
    verticalAlign: "center",
    headerAlign: "center",
    headerClasses: "table-header",
    formatter: (cell, row) => (
      <img
        className="rounded-circle border"
        height="30"
        width="30"
        loading="lazy"
        src={require(`../../../assets/images/svg/profile.jpg`)}
        alt="avatar"
      />
    )
  },
  {
    dataField: "",
    text: "Actions",
    sort: true,
    style: { color: "#757575" },
    align: "left",
    verticalAlign: "center",
    headerAlign: "left",
    headerClasses: "table-header action",
    formatter: (cell, row) => (
      <span>
        {tableAction(row, handleStatus, hasPermission)}
      </span>
    )
  },
  {
    dataField: "email",
    text: "Email",
    sort: true,
    style: { color: "#757575" },
    headerClasses: "table-header status",
    formatter: (cell, row) => (
      <span>
        <Link target="_blank" to={`/admin/club/application/${row._id}`}>{row.email}</Link>
      </span>
    ),
  },
  {
    dataField: "name",
    text: "Name",
    sort: true,
    headerClasses: "table-header name",
    style: { color: "#757575" },
  },
  {
    dataField: "gender",
    text: "Gender",
    sort: true,
    headerClasses: "table-header name",
    style: { color: "#757575" },
  },
  {
    dataField: "phone",
    text: "Phone",
    sort: true,
    style: { color: "#757575" },
    align: "left",
    headerAlign: "left",
    headerClasses: "table-header group-name",
  },
  {
    dataField: "referralName",
    text: "Referral",
    style: { color: "#757575" },
    align: "left",
    headerAlign: "left",
    headerClasses: "table-header group-name",
  },
  {
    dataField: "university",
    text: "University",
    sort: true,
    style: { color: "#757575" },
    align: "left",
    headerAlign: "left",
    headerClasses: "table-header group-name",
  },
  {
    dataField: "createdAt",
    text: "Application Date",
    sort: true,
    style: { color: "#757575" },
    align: "left",
    headerAlign: "left",
    headerClasses: "table-header group-name",
    formatter: (cell, row) => (
      <span>
        {row.createdAt === null
          ? "Invalid date"
          : moment(row.createdAt).format("LLL")}
      </span>
    ),
  },
  {
    dataField: "",
    text: "",
    sort: true,
    style: { color: "#757575" },
    align: "center",
    headerAlign: "center",
    headerClasses: "table-header",
    formatter: (cell, row) => (
      <span>
        <i type="button" className="mr-2 fas fa-trash"
          onClick={() => handleDeleteAction(row._id)}
        />
      </span>
    )
  },
];
{"header": {"reportVersion": 1, "event": "Allocation failed - JavaScript heap out of memory", "trigger": "FatalE<PERSON>r", "filename": "report.20240625.194655.19628.0.001.json", "dumpEventTime": "2024-06-25T19:46:55Z", "dumpEventTimeStamp": "1719325015369", "processId": 19628, "cwd": "/home/<USER>/projects/datacode-creator", "commandLine": ["/home/<USER>/.nvm/versions/node/v13.7.0/bin/node", "/home/<USER>/projects/datacode-creator/node_modules/react-scripts/scripts/build.js"], "nodejsVersion": "v13.7.0", "glibcVersionRuntime": "2.31", "glibcVersionCompiler": "2.17", "wordSize": 64, "arch": "x64", "platform": "linux", "componentVersions": {"node": "13.7.0", "v8": "7.9.317.25-node.28", "uv": "1.34.1", "zlib": "1.2.11", "brotli": "1.0.7", "ares": "1.15.0", "modules": "79", "nghttp2": "1.40.0", "napi": "5", "llhttp": "2.0.1", "openssl": "1.1.1d", "cldr": "36.0", "icu": "65.1", "tz": "2019c", "unicode": "12.1"}, "release": {"name": "node", "headersUrl": "https://nodejs.org/download/release/v13.7.0/node-v13.7.0-headers.tar.gz", "sourceUrl": "https://nodejs.org/download/release/v13.7.0/node-v13.7.0.tar.gz"}, "osName": "Linux", "osRelease": "5.15.0-91-generic", "osVersion": "#101~20.04.1-Ubuntu SMP Thu Nov 16 14:22:28 UTC 2023", "osMachine": "x86_64", "cpus": [{"model": "11th Gen Intel(R) Core(TM) i5-1135G7 @ 2.40GHz", "speed": 2475, "user": 5197100, "nice": 1700, "sys": 996800, "idle": 34174900, "irq": 0}, {"model": "11th Gen Intel(R) Core(TM) i5-1135G7 @ 2.40GHz", "speed": 1951, "user": 5453700, "nice": 700, "sys": 1028600, "idle": 33903100, "irq": 0}, {"model": "11th Gen Intel(R) Core(TM) i5-1135G7 @ 2.40GHz", "speed": 2483, "user": 5354000, "nice": 1000, "sys": 963200, "idle": 34070500, "irq": 0}, {"model": "11th Gen Intel(R) Core(TM) i5-1135G7 @ 2.40GHz", "speed": 2400, "user": 4788000, "nice": 3000, "sys": 1174000, "idle": 34121800, "irq": 0}, {"model": "11th Gen Intel(R) Core(TM) i5-1135G7 @ 2.40GHz", "speed": 2400, "user": 5161000, "nice": 500, "sys": 975200, "idle": 34243600, "irq": 0}, {"model": "11th Gen Intel(R) Core(TM) i5-1135G7 @ 2.40GHz", "speed": 2400, "user": 5322800, "nice": 1900, "sys": 967400, "idle": 33220100, "irq": 0}, {"model": "11th Gen Intel(R) Core(TM) i5-1135G7 @ 2.40GHz", "speed": 2400, "user": 5198000, "nice": 300, "sys": 922200, "idle": 34173500, "irq": 0}, {"model": "11th Gen Intel(R) Core(TM) i5-1135G7 @ 2.40GHz", "speed": 2400, "user": 5677900, "nice": 6400, "sys": 953400, "idle": 33795800, "irq": 0}], "networkInterfaces": [{"name": "lo", "internal": true, "mac": "00:00:00:00:00:00", "address": "127.0.0.1", "netmask": "*********", "family": "IPv4"}, {"name": "wlp0s20f3", "internal": false, "mac": "80:b6:55:fe:12:35", "address": "************", "netmask": "*************", "family": "IPv4"}, {"name": "br-0d6aa2576038", "internal": false, "mac": "02:42:d0:52:21:b4", "address": "**********", "netmask": "***********", "family": "IPv4"}, {"name": "lo", "internal": true, "mac": "00:00:00:00:00:00", "address": "::1", "netmask": "ffff:ffff:ffff:ffff:ffff:ffff:ffff:ffff", "family": "IPv6", "scopeid": 0}, {"name": "wlp0s20f3", "internal": false, "mac": "80:b6:55:fe:12:35", "address": "fe80::faf7:a86d:2232:2e76", "netmask": "ffff:ffff:ffff:ffff::", "family": "IPv6", "scopeid": 2}, {"name": "br-0d6aa2576038", "internal": false, "mac": "02:42:d0:52:21:b4", "address": "fe80::42:d0ff:fe52:21b4", "netmask": "ffff:ffff:ffff:ffff::", "family": "IPv6", "scopeid": 4}, {"name": "veth95cf02f", "internal": false, "mac": "6a:1a:ae:dc:76:07", "address": "fe80::681a:aeff:fedc:7607", "netmask": "ffff:ffff:ffff:ffff::", "family": "IPv6", "scopeid": 6}, {"name": "veth5757c2b", "internal": false, "mac": "b2:18:b5:4f:e1:d5", "address": "fe80::b018:b5ff:fe4f:e1d5", "netmask": "ffff:ffff:ffff:ffff::", "family": "IPv6", "scopeid": 8}, {"name": "veth6988268", "internal": false, "mac": "22:36:d5:76:7e:eb", "address": "fe80::2036:d5ff:fe76:7eeb", "netmask": "ffff:ffff:ffff:ffff::", "family": "IPv6", "scopeid": 10}, {"name": "vethfbe3197", "internal": false, "mac": "ae:e9:e9:fb:bc:ef", "address": "fe80::ace9:e9ff:fefb:bcef", "netmask": "ffff:ffff:ffff:ffff::", "family": "IPv6", "scopeid": 12}], "host": "developer-HP-Pavilion-Laptop-14-dv0xxx"}, "javascriptStack": {"message": "No stack.", "stack": ["Unavailable."]}, "nativeStack": [{"pc": "0x0000000000bd0365", "symbol": "report::TriggerNodeReport(v8::Isolate*, node::Environment*, char const*, char const*, std::string const&, v8::Local<v8::String>) [/home/<USER>/.nvm/versions/node/v13.7.0/bin/node]"}, {"pc": "0x0000000000a9f86c", "symbol": "node::OnFatalError(char const*, char const*) [/home/<USER>/.nvm/versions/node/v13.7.0/bin/node]"}, {"pc": "0x0000000000c0758e", "symbol": "v8::Utils::ReportOOMFailure(v8::internal::Isolate*, char const*, bool) [/home/<USER>/.nvm/versions/node/v13.7.0/bin/node]"}, {"pc": "0x0000000000c07909", "symbol": "v8::internal::V8::FatalProcessOutOfMemory(v8::internal::Isolate*, char const*, bool) [/home/<USER>/.nvm/versions/node/v13.7.0/bin/node]"}, {"pc": "0x0000000000db5e15", "symbol": " [/home/<USER>/.nvm/versions/node/v13.7.0/bin/node]"}, {"pc": "0x0000000000db64a6", "symbol": "v8::internal::Heap::RecomputeLimits(v8::internal::GarbageCollector) [/home/<USER>/.nvm/versions/node/v13.7.0/bin/node]"}, {"pc": "0x0000000000dc4d19", "symbol": "v8::internal::Heap::PerformGarbageCollection(v8::internal::GarbageCollector, v8::GCCallbackFlags) [/home/<USER>/.nvm/versions/node/v13.7.0/bin/node]"}, {"pc": "0x0000000000dc5b55", "symbol": "v8::internal::Heap::CollectGarbage(v8::internal::AllocationSpace, v8::internal::GarbageCollectionReason, v8::GCCallbackFlags) [/home/<USER>/.nvm/versions/node/v13.7.0/bin/node]"}, {"pc": "0x0000000000dc862c", "symbol": "v8::internal::Heap::AllocateRawWithRetryOrFailSlowPath(int, v8::internal::AllocationType, v8::internal::AllocationOrigin, v8::internal::AllocationAlignment) [/home/<USER>/.nvm/versions/node/v13.7.0/bin/node]"}, {"pc": "0x0000000000d8f204", "symbol": "v8::internal::Factory::NewFillerObject(int, bool, v8::internal::AllocationType, v8::internal::AllocationOrigin) [/home/<USER>/.nvm/versions/node/v13.7.0/bin/node]"}, {"pc": "0x00000000010dc68c", "symbol": "v8::internal::Runtime_AllocateInOldGeneration(int, unsigned long*, v8::internal::Isolate*) [/home/<USER>/.nvm/versions/node/v13.7.0/bin/node]"}, {"pc": "0x000000000145cc79", "symbol": " [/home/<USER>/.nvm/versions/node/v13.7.0/bin/node]"}], "javascriptHeap": {"totalMemory": 2168766464, "totalCommittedMemory": 2160743712, "usedMemory": 2141247744, "availableMemory": 38293800, "memoryLimit": 2197815296, "heapSpaces": {"read_only_space": {"memorySize": 262144, "committedMemory": 33328, "capacity": 33040, "used": 33040, "available": 0}, "new_space": {"memorySize": 8388608, "committedMemory": 1158216, "capacity": 4189696, "used": 146504, "available": 4043192}, "old_space": {"memorySize": 1686249472, "committedMemory": 1685961336, "capacity": 1672784472, "used": 1671772392, "available": 1012080}, "code_space": {"memorySize": 2789376, "committedMemory": 2515520, "capacity": 2291616, "used": 2291616, "available": 0}, "map_space": {"memorySize": 24645632, "committedMemory": 24644080, "capacity": 21802960, "used": 21802960, "available": 0}, "large_object_space": {"memorySize": 445808640, "committedMemory": 445808640, "capacity": 444656688, "used": 444656688, "available": 0}, "code_large_object_space": {"memorySize": 622592, "committedMemory": 622592, "capacity": 544544, "used": 544544, "available": 0}, "new_large_object_space": {"memorySize": 0, "committedMemory": 0, "capacity": 4189696, "used": 0, "available": 4189696}}}, "resourceUsage": {"userCpuSeconds": 342.438, "kernelCpuSeconds": 10.4351, "cpuConsumptionPercent": 299.045, "maxRss": 4141252608, "pageFaults": {"IORequired": 77, "IONotRequired": 2355415}, "fsActivity": {"reads": 898456, "writes": 768}}, "uvthreadResourceUsage": {"userCpuSeconds": 96.3892, "kernelCpuSeconds": 2.83451, "cpuConsumptionPercent": 84.0879, "fsActivity": {"reads": 117960, "writes": 728}}, "libuv": [], "environmentVariables": {"GJS_DEBUG_TOPICS": "JS ERROR;JS LOG", "LESSOPEN": "| /usr/bin/lesspipe %s", "npm_package_dependencies_prism_react_renderer": "^1.0.2", "npm_package_dependencies_react_draft_wysiwyg": "^1.15.0", "npm_config_cache_lock_stale": "60000", "npm_config_ham_it_up": "", "npm_config_legacy_bundling": "", "npm_config_sign_git_tag": "", "LANGUAGE": "en_IN:en", "USER": "developer", "npm_config_user_agent": "npm/6.13.6 node/v13.7.0 linux x64", "npm_config_always_auth": "", "npm_package_dependencies_formik": "^2.4.5", "npm_package_dependencies_react_device_detect": "^2.2.3", "npm_package_dependencies_react_simple_code_editor": "^0.13.1", "npm_config_bin_links": "true", "npm_config_key": "", "SSH_AGENT_PID": "1574", "XDG_SESSION_TYPE": "x11", "npm_package_dependencies_react_slick": "^0.30.1", "npm_config_allow_same_version": "", "npm_config_description": "true", "npm_config_fetch_retries": "2", "npm_config_heading": "npm", "npm_config_if_present": "", "npm_config_init_version": "1.0.0", "npm_config_user": "1000", "npm_node_execpath": "/home/<USER>/.nvm/versions/node/v13.7.0/bin/node", "SHLVL": "1", "npm_package_dependencies_draft_js": "^0.11.7", "npm_package_dependencies_export_from_json": "^1.6.0", "npm_config_prefer_online": "", "npm_config_noproxy": "", "HOME": "/home/<USER>", "npm_package_browserslist_production_0": ">0.2%", "npm_config_force": "", "DESKTOP_SESSION": "ubuntu", "NVM_BIN": "/home/<USER>/.nvm/versions/node/v13.7.0/bin", "npm_package_dependencies__akshayDatacode_datacode_ui": "^0.1.7", "npm_package_dependencies_react_multi_carousel": "^2.8.4", "npm_package_dependencies_react_syntax_highlighter": "^15.5.0", "npm_package_dependencies_reactstrap": "^9.1.3", "npm_package_browserslist_production_1": "not dead", "npm_config_only": "", "npm_config_read_only": "", "NVM_INC": "/home/<USER>/.nvm/versions/node/v13.7.0/include/node", "npm_package_dependencies_react_scroll": "^1.8.7", "npm_package_browserslist_production_2": "not op_mini all", "npm_config_cache_min": "10", "npm_config_init_license": "ISC", "GNOME_SHELL_SESSION_MODE": "ubuntu", "GTK_MODULES": "gail:atk-bridge", "npm_package_dependencies_redux_form": "^8.3.8", "npm_config_editor": "vi", "npm_config_rollback": "true", "npm_config_tag_version_prefix": "v", "MANAGERPID": "1348", "npm_package_dependencies_html2canvas": "^1.4.1", "npm_package_dependencies_react_icons": "^4.12.0", "npm_package_dependencies_redux_thunk": "^2.4.1", "npm_package_dependencies_webpack": "^5.74.0", "npm_config_cache_max": "Infinity", "npm_config_timing": "", "npm_config_userconfig": "/home/<USER>/.npmrc", "DBUS_SESSION_BUS_ADDRESS": "unix:path=/run/user/1000/bus", "npm_package_dependencies_moment": "^2.29.4", "npm_config_engine_strict": "", "npm_config_init_author_name": "", "npm_config_init_author_url": "", "npm_config_preid": "", "npm_config_tmp": "/tmp", "COLORTERM": "truecolor", "npm_package_dependencies__tinymce_tinymce_react": "^4.3.0", "npm_package_dependencies_antd": "^5.2.0", "npm_package_dependencies_react_router_dom": "^6.3.0", "npm_package_dependencies_react_scripts": "5.0.1", "npm_package_description": "This project was bootstrapped with [Create React App](https://github.com/facebook/create-react-app).", "npm_config_depth": "Infinity", "npm_config_package_lock_only": "", "npm_config_save_dev": "", "npm_config_usage": "", "NVM_DIR": "/home/<USER>/.nvm", "npm_package_homepage": "https://datacode-creator.azurewebsites.net", "npm_package_dependencies__testing_library_react": "^13.3.0", "npm_package_dependencies_jwt_decode": "^4.0.0", "npm_package_dependencies_react_color": "^2.19.3", "npm_package_readmeFilename": "README.md", "npm_config_metrics_registry": "https://registry.npmjs.org/", "npm_config_cafile": "", "npm_config_otp": "", "npm_config_package_lock": "true", "npm_config_progress": "true", "npm_config_https_proxy": "", "npm_config_save_prod": "", "IM_CONFIG_PHASE": "1", "npm_package_dependencies_prismjs": "^1.19.0", "npm_config_audit": "true", "npm_config_cidr": "", "npm_config_onload_script": "", "npm_config_sso_type": "o<PERSON>h", "LOGNAME": "developer", "npm_package_dependencies_react_tooltip": "^5.26.3", "npm_config_rebuild_bundle": "true", "npm_config_save_bundle": "", "npm_config_shell": "/bin/bash", "JOURNAL_STREAM": "8:46370", "_": "/home/<USER>/.nvm/versions/node/v13.7.0/bin/npm", "npm_package_private": "true", "npm_package_dependencies__emotion_react": "^11.11.4", "npm_config_dry_run": "", "npm_config_format_package_lock": "true", "npm_config_prefix": "/home/<USER>/.nvm/versions/node/v13.7.0", "XDG_SESSION_CLASS": "user", "npm_config_scope": "", "npm_config_registry": "https://registry.npmjs.org/", "npm_config_browser": "", "npm_config_cache_lock_wait": "10000", "npm_config_ignore_prepublish": "", "npm_config_save_optional": "", "npm_config_searchopts": "", "npm_config_versions": "", "USERNAME": "developer", "TERM": "xterm-256color", "npm_package_dependencies_react_bootstrap_table_next": "^1.1.0", "npm_package_devDependencies__babel_plugin_proposal_private_property_in_object": "^7.21.11", "npm_config_cache": "/home/<USER>/.npm", "npm_config_proxy": "", "npm_config_send_metrics": "", "GNOME_DESKTOP_SESSION_ID": "this-is-deprecated", "npm_package_scripts_start": "react-scripts start", "npm_config_global_style": "", "npm_config_ignore_scripts": "", "npm_config_version": "", "WINDOWPATH": "2", "npm_package_dependencies_axios": "^1.6.7", "npm_package_browserslist_development_0": "last 1 chrome version", "npm_config_local_address": "", "npm_config_viewer": "man", "npm_config_node_gyp": "/home/<USER>/.nvm/versions/node/v13.7.0/lib/node_modules/npm/node_modules/node-gyp/bin/node-gyp.js", "PATH": "/home/<USER>/.nvm/versions/node/v13.7.0/lib/node_modules/npm/node_modules/npm-lifecycle/node-gyp-bin:/home/<USER>/projects/datacode-creator/node_modules/.bin:/home/<USER>/.nvm/versions/node/v13.7.0/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/home/<USER>/Android/Sdk/emulator:/home/<USER>/Android/Sdk/platform-tools", "SESSION_MANAGER": "local/developer-HP-Pavilion-Laptop-14-dv0xxx:@/tmp/.ICE-unix/1617,unix/developer-HP-Pavilion-Laptop-14-dv0xxx:/tmp/.ICE-unix/1617", "INVOCATION_ID": "aa3752762cda430cb6b17d68edd2c0c9", "npm_package_name": "datacode-creator", "npm_package_dependencies__mui_material": "^5.15.19", "npm_package_browserslist_development_1": "last 1 firefox version", "npm_config_audit_level": "low", "npm_config_prefer_offline": "", "NODE": "/home/<USER>/.nvm/versions/node/v13.7.0/bin/node", "XDG_MENU_PREFIX": "gnome-", "GNOME_TERMINAL_SCREEN": "/org/gnome/Terminal/screen/84c2639e_c3bb_4ad0_b87e_fdd9b2a6975f", "XDG_RUNTIME_DIR": "/run/user/1000", "npm_package_dependencies_react_hot_toast": "^2.4.1", "npm_package_dependencies_react_redux": "^8.0.2", "npm_package_browserslist_development_2": "last 1 safari version", "npm_config_color": "true", "npm_config_sign_git_commit": "", "DISPLAY": ":0", "npm_package_dependencies_draftjs_to_html": "^0.9.1", "npm_package_dependencies_html_to_draftjs": "^1.5.0", "npm_package_scripts_eject": "react-scripts eject", "npm_config_fetch_retry_mintimeout": "10000", "npm_config_maxsockets": "50", "npm_config_offline": "", "npm_config_sso_poll_frequency": "500", "LANG": "en_IN", "XDG_CURRENT_DESKTOP": "ubuntu:GNOME", "npm_package_dependencies_react_dom": "^18.2.0", "npm_package_dependencies_redux": "^4.2.0", "npm_config_umask": "0002", "XMODIFIERS": "@im=ibus", "XDG_SESSION_DESKTOP": "ubuntu", "XAUTHORITY": "/run/user/1000/gdm/Xauthority", "LS_COLORS": "rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:", "GNOME_TERMINAL_SERVICE": ":1.191", "npm_package_eslintConfig_extends_0": "react-app", "npm_package_gitHead": "db197b94a69464fe44e698577b5d17c396dbbc88", "npm_config_fund": "true", "npm_config_fetch_retry_maxtimeout": "60000", "npm_config_loglevel": "notice", "npm_config_logs_max": "10", "npm_config_message": "%s", "npm_lifecycle_script": "react-scripts build", "SSH_AUTH_SOCK": "/run/user/1000/keyring/ssh", "npm_package_dependencies_react_gist": "^1.2.4", "npm_package_dependencies_react_router": "^6.3.0", "npm_package_scripts_test": "react-scripts test", "npm_package_eslintConfig_extends_1": "react-app/jest", "npm_config_ca": "", "npm_config_cert": "", "npm_config_global": "", "npm_config_link": "", "SHELL": "/bin/bash", "npm_package_version": "0.1.0", "npm_package_dependencies__testing_library_jest_dom": "^5.16.4", "npm_package_dependencies_react_loader_spinner": "^5.1.7-beta.1", "npm_config_access": "", "npm_config_also": "", "npm_config_save": "true", "npm_config_unicode": "", "npm_lifecycle_event": "build", "QT_ACCESSIBILITY": "1", "GDMSESSION": "ubuntu", "npm_package_dependencies__testing_library_user_event": "^13.5.0", "npm_package_dependencies_react_codepen_embed": "^1.1.0", "npm_package_scripts_build": "react-scripts build", "npm_config_argv": "{\"remain\":[],\"cooked\":[\"run\",\"build\"],\"original\":[\"run\",\"build\"]}", "npm_config_before": "", "npm_config_long": "", "npm_config_production": "", "npm_config_searchlimit": "20", "npm_config_unsafe_perm": "true", "npm_config_update_notifier": "true", "LESSCLOSE": "/usr/bin/lesspipe %s %s", "npm_package_dependencies_lodash": "^4.17.21", "npm_config_auth_type": "legacy", "npm_config_node_version": "13.7.0", "npm_config_tag": "latest", "npm_config_git_tag_version": "true", "npm_config_commit_hooks": "true", "npm_config_script_shell": "", "npm_config_shrinkwrap": "true", "GPG_AGENT_INFO": "/run/user/1000/gnupg/S.gpg-agent:0:1", "GJS_DEBUG_OUTPUT": "stderr", "npm_package_dependencies_html_react_parser": "^3.0.4", "npm_package_dependencies_sass": "^1.77.4", "npm_config_fetch_retry_factor": "10", "npm_config_save_exact": "", "npm_config_strict_ssl": "true", "QT_IM_MODULE": "ibus", "npm_package_dependencies__mui_icons_material": "^5.15.19", "npm_package_dependencies_react_simple_star_rating": "^5.1.7", "npm_config_dev": "", "npm_config_globalconfig": "/home/<USER>/.nvm/versions/node/v13.7.0/etc/npmrc", "npm_config_init_module": "/home/<USER>/.npm-init.js", "npm_config_parseable": "", "PWD": "/home/<USER>/projects/datacode-creator", "npm_package_dependencies__emotion_styled": "^11.11.5", "npm_package_dependencies_bootstrap": "^4.6.0", "npm_config_globalignorefile": "/home/<USER>/.nvm/versions/node/v13.7.0/etc/npmignore", "npm_execpath": "/home/<USER>/.nvm/versions/node/v13.7.0/lib/node_modules/npm/bin/npm-cli.js", "XDG_CONFIG_DIRS": "/etc/xdg/xdg-ubuntu:/etc/xdg", "ANDROID_HOME": "/home/<USER>/Android/Sdk", "NVM_CD_FLAGS": "", "XDG_DATA_DIRS": "/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop", "npm_package_dependencies_react_widgets": "^5.8.4", "npm_config_cache_lock_retries": "10", "npm_config_searchstaleness": "900", "npm_config_node_options": "", "npm_config_save_prefix": "^", "npm_config_scripts_prepend_node_path": "warn-only", "npm_config_group": "1000", "npm_config_init_author_email": "", "npm_config_searchexclude": "", "VTE_VERSION": "6003", "npm_package_dependencies_react_grid_gallery": "^1.0.1-alpha.0", "npm_package_dependencies_web_vitals": "^2.1.4", "npm_config_git": "git", "npm_config_optional": "true", "npm_package_dependencies_react": "^18.2.0", "npm_package_dependencies_react_paginate": "^8.1.3", "npm_config__akshayDatacode_registry": "https://npm.pkg.github.com/", "npm_config_json": "", "INIT_CWD": "/home/<USER>/projects/datacode-creator", "BABEL_ENV": "production", "NODE_ENV": "production", "SKIP_PREFLIGHT_CHECK": "true", "REACT_APP_BASE_URL": "https://datacode-api-v2.azurewebsites.net/api", "REACT_APP_ADMIN_EMAIL": "<EMAIL>", "REACT_APP_ADMIN_USERNAME": "akshay@25", "REACT_APP_ADMIN_AUTHOR_ID": "604ddb23c8cead1ad84b7174", "GITHUB_ACCESS_TOKEN_AKSHAY": "****************************************", "GITHUB_ACCESS_TOKEN_SHUBHAM": "****************************************", "NODE_PATH": ""}, "userLimits": {"core_file_size_blocks": {"soft": 0, "hard": "unlimited"}, "data_seg_size_kbytes": {"soft": "unlimited", "hard": "unlimited"}, "file_size_blocks": {"soft": "unlimited", "hard": "unlimited"}, "max_locked_memory_bytes": {"soft": 67108864, "hard": 67108864}, "max_memory_size_kbytes": {"soft": "unlimited", "hard": "unlimited"}, "open_files": {"soft": 1048576, "hard": 1048576}, "stack_size_bytes": {"soft": 8388608, "hard": "unlimited"}, "cpu_time_seconds": {"soft": "unlimited", "hard": "unlimited"}, "max_user_processes": {"soft": 62718, "hard": 62718}, "virtual_memory_kbytes": {"soft": "unlimited", "hard": "unlimited"}}, "sharedObjects": ["linux-vdso.so.1", "/lib/x86_64-linux-gnu/libdl.so.2", "/lib/x86_64-linux-gnu/libstdc++.so.6", "/lib/x86_64-linux-gnu/libm.so.6", "/lib/x86_64-linux-gnu/libgcc_s.so.1", "/lib/x86_64-linux-gnu/libpthread.so.0", "/lib/x86_64-linux-gnu/libc.so.6", "/lib64/ld-linux-x86-64.so.2"]}
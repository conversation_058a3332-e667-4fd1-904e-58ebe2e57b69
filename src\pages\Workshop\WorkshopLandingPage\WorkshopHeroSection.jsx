import React from 'react'

import Vector2 from "../../../assets/images/svg/Vector2.png"
import SoftStar from "../../../assets/images/svg/SoftStar.png"
import Group37 from "../../../assets/images/svg/Group37.png"
import Lightning from "../../../assets/images/svg/Lightning.png"

const WorkshopHeroSection = () => {
  return (
    <>
      <div className="row mx-0 mb-5 pb-5 d-flex justify-content-center workshop-hero-section">
        <div className="col-12 px-0 border">
          <div className='row x-0'>
            <div className='col-2 d-flex align-items-end'>
              <img
                className="img-fluid"
                loading="lazy"
                src={Group37}
                alt="star"
              />
              <img
                className="img-fluid"
                loading="lazy"
                src={SoftStar}
                alt=""
              />
            </div>
            <div className='col-4 px-0 herosection-heading'>
              <div className='d-flex justify-content-center px-0'>
                <h2 className="">Unlock Your
                  Potential with
                  Dynamic Workshops and Training Programs
                </h2>
                <img
                  className="lightning"
                  loading="lazy"
                  src={Lightning}
                  alt="star"
                  width={65}
                  height={115}
                />
              </div>
              <div>
                <p>Elevate Your Learning Environment: Join us to Connect, Learn, and Grow!!</p>
              </div>
            </div>
            <div className='col-6 pl-0'>
              <img
                className="img-fluid"
                loading="lazy"
                src={Vector2}
                alt="avatar"
              />
            </div>
            <div className=" col-12 workshop-search-bar">
              <input type="search" className="form-control rounded" placeholder="Search" aria-label="Search" aria-describedby="search-addon" />
              <button type="button" className="btn btn-outline-primary" data-mdb-ripple-init>search</button>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}

export default WorkshopHeroSection
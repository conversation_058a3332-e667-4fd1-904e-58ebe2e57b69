import React from "react";
import moment from "moment";
import { Link } from "react-router-dom";

const getStatus = (status) => {
  if (status) {
    return <i className="fas fa-user-check green" />
  } else {
    return <i className="fas fa-user-clock orange" />
  }
}
export const getColumns = (handleUserAction, handleUserDeleteAction) => [
  {
    dataField: "",
    text: "S.NO.",
    align: "center",
    headerAlign: "center",
    headerClasses: "table-header s-no",
    formatter: (cell, row, index) => <span>{++index}</span>,
  },
  {
    dataField: "isApproved",
    text: "Status",
    align: "center",
    headerAlign: "center",
    sort: true,
    headerClasses: "table-header s-no",
    formatter: (cell, row, index) => <span>{getStatus(row.isApproved)}</span>,
  },
  {
    dataField: "email",
    text: "Email",
    sort: true,
    style: { color: "#757575" },
    headerClasses: "table-header status",
    formatter: (cell, row) => (
      <span>
        <Link target="_blank" to={`/profile/${row.userName}`}>{row.email}</Link>
      </span>
    ),
  },
  {
    dataField: "name",
    text: "Name",
    sort: true,
    headerClasses: "table-header name",
    style: { color: "#757575" },
  },
  {
    dataField: "phone",
    text: "Phone",
    sort: true,
    style: { color: "#757575" },
    align: "left",
    headerAlign: "left",
    headerClasses: "table-header group-name",
  },
  {
    dataField: "technology",
    text: "Tech",
    sort: true,
    style: { color: "#757575" },
    align: "left",
    headerAlign: "left",
    headerClasses: "table-header group-name",
  },
  {
    dataField: "gender",
    text: "Gender",
    sort: true,
    style: { color: "#757575" },
    align: "left",
    headerAlign: "left",
    headerClasses: "table-header group-name",
  },
  {
    dataField: "question1",
    text: "Question1",
    sort: true,
    style: { color: "#757575" },
    align: "left",
    headerAlign: "left",
    headerClasses: "table-header status",
  },
  {
    dataField: "",
    text: "Actions",
    sort: true,
    style: { color: "#757575" },
    align: "left",
    headerAlign: "left",
    headerClasses: "table-header status",
    formatter: (cell, row) => (
      <div className='text-left d-flex'>
        <i type="button" className="mr-2 fas fa-user-check"
          onClick={() => handleUserAction(row.userName, 'Approve')}
        />
        <i type="button" className="mr-2 fas fa-user-times"
          onClick={() => handleUserAction(row.userName, 'Reject')}
        />
        <i type="button" className="mr-2 far fa-trash"
          onClick={() => handleUserDeleteAction(row.userName)}
        />
      </div>
    ),
  },
  {
    dataField: "createdAt",
    text: "Applied On",
    sort: true,
    style: { color: "#757575" },
    align: "left",
    headerAlign: "left",
    headerClasses: "table-header group-name",
    formatter: (cell, row) => (
      <span>
        {row.createdAt === null
          ? "Invalid date"
          : moment(row.createdAt).format("LLL")}
      </span>
    ),
  },
];


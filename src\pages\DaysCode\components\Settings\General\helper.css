

.a{
    box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1); 
}
.c{
    font-size: 30px;
    display: flex;
    /* display: inline-block; */
    justify-content: center;
}
.mainCardContainer{
    display: flex;
    padding: 20px;
    justify-content: space-evenly;
}
.cardContainer{
    height: 250px;
    width: 250px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    padding-left:5px;
    position: relative;
    overflow: hidden;
    margin: 10px;
}
.caredName{
    /* font-size: 20px; */
    font-weight: 700;
    color: white;
}
.cardValue{
    font-size: 20px;
    font-weight: 600;
}
.circleOne{
    height: 160px;
    width: 160px;
    background: rgba(255, 255, 255, 0.379);
    border-radius: 80px;
    position: absolute;
    right: -22px;
    bottom: -36px; 
}
.circleTwo{
    height: 160px;
    width: 160px;
    background: rgba(255, 255, 255, 0.379);
    border-radius: 80px;
    position: absolute;
    right: -59px;
    top: -7px;
}

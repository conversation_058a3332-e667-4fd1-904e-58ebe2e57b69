import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>dal<PERSON>ody, ModalFooter } from "reactstrap";
import Combobox from "react-widgets/Combobox";

const AsignCourseModal = ({
  model,
  openModal,
  closeModal,
  coursesList,
  courseUserId,
  handleEnrollCourseByAdmin,
}) => {

  const [courseId, setCourseId] = useState("");

  const getCourseOptions = (courses) => {
    const options = [];
    courses.forEach((course, i) => {
      options.push({ course: course.name, value: course._id });
    });
    return options;
  };
  
  return (
    <>
      <Modal centered isOpen={model} toggle={openModal} className='text-center'>
        <ModalHeader
          className='d-flex justify-content-between align-items-center'
          toggle={closeModal}
        ></ModalHeader>
        <ModalBody>
          <div>
            <label className='mr-4  form-label'>Course User Enrollment:</label>
            <div className='border mt-3 rounded-lg card-schadow p-3'>
              <div className='mt-1'>
                <Combobox
                  data={getCourseOptions(coursesList)}
                  dataKey={"value"}
                  textField='course'
                  placeholder={"Select Course Name"}
                  value={courseId}
                  onChange={(value) => setCourseId(value)}
                />
              </div>
            </div>
          </div>
        </ModalBody>
        <ModalFooter className='d-flex justify-content-center align-items-center'>
          <Button
            color='success'
            onClick={() =>
              handleEnrollCourseByAdmin(courseId?.value, courseUserId)
            }
          >
            Asign
          </Button>
        </ModalFooter>
      </Modal>
    </>
  );
};

export default AsignCourseModal;

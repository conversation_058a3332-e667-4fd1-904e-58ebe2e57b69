import React from "react";
import {
  EditOutlined,
  EllipsisOutlined,
  SettingOutlined,
} from "@ant-design/icons";
import { Avatar, Card, Dropdown } from "antd";
import { Link } from "react-router-dom";
import DeleteIcon from "@mui/icons-material/Delete";

import usePermissions from "../../../../../hooks/userPermission";
const { Meta } = Card;

const BatchCard = ({ batch, deleteBatch, handleAddClass }) => {
  const { hasPermission } = usePermissions();

  const items = [
    {
      label: <span>Add Mentor</span>,
      key: "1",
    },
    {
      label: <span>Participants</span>,
      key: "2",
    },
    {
      label: <span onClick={() => handleAddClass(batch._id)}>Add Class</span>,
      key: "3",
    },
    {
      type: "divider",
    },
    {
      label: (
        hasPermission("batch", "delete") &&
        <div onClick={() => deleteBatch(batch._id)}>
          <span className='text-danger d-flex align-items-center'>
            Delete
            <DeleteIcon className='ml-2' fontSize='small' />
          </span>
        </div>
      ),
      key: "0",
    },
  ];

  return (
    <>
      <div className='days-batch-card'>
        <Card
          style={{
            width: 300,
          }}
          cover={
            <img
              alt='example'
              src='https://gw.alipayobjects.com/zos/rmsportal/JiqGstEfoWAOHiTxclqi.png'
            />
          }
          actions={[
            <Link
              to={`/admin/days_code/batch_dashboard/${batch._id}`}
              state={"batches"}
            >
              <SettingOutlined key='setting' />
            </Link>,
            <Link to={`/admin/days_code/batch/${batch._id}`} state={"batches"}>
              <EditOutlined key='edit' />
            </Link>,
            <>
              <Dropdown menu={{ items }} trigger={["click"]}>
                <EllipsisOutlined
                  onClick={(e) => e.preventDefault()}
                  key='ellipsis'
                />
              </Dropdown>
            </>,
          ]}
        >
          <Meta
            avatar={
              <Avatar src='https://xsgames.co/randomusers/avatar.php?g=pixel' />
            }
            title={batch.title}
            description={"Batch Start From: " + batch.start_date}
          />
        </Card>
      </div>
    </>
  );
};

export default BatchCard;

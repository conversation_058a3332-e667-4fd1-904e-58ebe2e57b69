import React from "react";
import PropTypes from "prop-types";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>dal<PERSON>ody, ModalFooter } from "reactstrap";

const RoadmapColorInfoModal = ({
  open,
  toggle,
}) => {
  const missingCounts = [0,1,2,3];
  const colors = ["#368A9F", "#61DBFB", "#8CD9ED", "#F6F6F6",  ]
return(
  <Modal isOpen={open} toggle={toggle} >
    <div className="p-4 d-flex justify-content-between">
      <div className="delete-modal-header-title">
        <h3 className="mb-0">INFO </h3>
      </div>
    </div>
    <ModalBody className="modal-body">
    {missingCounts.map((count)=>(
          <div className="col-12 d-flex align-items-center" key={count}>
            <span className="d-block m-3 days-card" style={{backgroundColor:`${colors[count]}`}}></span>
            <span>{count===0 ? "All 3 i.e. Lesson, Quiz , Problem are present for particular day and roadmap can be created" 
            : count===1 ? "Any one from Lesson, Quiz, Problem is not created for particular day and roadmap cant be created" 
            : count===2 ? "Any two from Lesson, Quiz, Problem are not created for particular day and roadmap cant be created" 
            : "All three from Lesson, Quiz, Problem are not created for particular day and roadmap cant be created"}</span></div>
        ))
        }
    </ModalBody>
    <ModalFooter className="modal-footer px-4 py-3">
      <Button color="success" onClick={toggle}>
        Okay
      </Button>
    </ModalFooter>
  </Modal>
)};

RoadmapColorInfoModal.propTypes = {
  toggle: PropTypes.func.isRequired,
  open: PropTypes.bool.isRequired,
};

export default  RoadmapColorInfoModal ;

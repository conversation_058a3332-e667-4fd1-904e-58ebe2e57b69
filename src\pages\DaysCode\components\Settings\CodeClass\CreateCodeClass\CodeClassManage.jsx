import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { FaTrash } from "react-icons/fa";

import {
  addBatchInToClass,
  addMentorInToClass,
  getCodeBatches,
  getDaysCodeMentors,
  deleteBatchFromClass,
  deleteMentorFromClass,
  addAttendanceInToClass,
  getCodeClassParticipants,
  deleteAttendanceFromClass,
} from "../../../../actions";
import AssignBatchMenterModal from "../CodeClassesList/AssignBatchMenterModal";
import AttendanceModel from "../CodeClassesList/AttendanceModel";


const CodeClassManage = ({ codeClass, codeClassDetails }) => {
  const dispatch = useDispatch();
  const { codeBatches, mentorList, codeClassParticipants } =
    useSelector((state) => state.dayscode) || {};

  const [showModel, setShowModel] = useState(false);
  const [modelFor, setModelFor] = useState("");
  const [codeClassId, setCodeClassId] = useState(null);
  const [openAttendanceModal, setOpenAttendanceModal] = useState(false);


  useEffect(() => {
    dispatch(getCodeBatches({schedule:"all"}));
    dispatch(getDaysCodeMentors());
    dispatch(getCodeClassParticipants(codeClass));
  }, [codeClass, dispatch]);

  const openModal = (id, typestr) => {
    setModelFor(typestr);
    setCodeClassId(id); // Store the Class ID in state
    setShowModel(true); // Open the modal
  };
  // Function to close the modal
  const closeModal = () => {
    setShowModel(false);
    setCodeClassId(null); // Reset the Class ID
  };
  const handleAssignBatchesToClass = (codeClass, batch) => {
    dispatch(addBatchInToClass({ codeClass, batch })).then((res) => {
      if (res && res.success) {
        closeModal();
        window.location.reload();
      }
    });
  };
  const handleAssignMentorInToClass = (codeClass, mentor) => {
    dispatch(addMentorInToClass({ codeClass, mentor })).then((res) => {
      if (res && res.success) {
        closeModal();
        window.location.reload();
      }
    });
  };
  const onDeleteBatch = (batch) => {
    dispatch(deleteBatchFromClass({ batch, codeClass })).then((res) => {
      if (res && res.success) {
        window.location.reload();
      }
    });
  };

  const onDeleteMentor = (mentor) => {
    dispatch(deleteMentorFromClass({ mentor, codeClass })).then((res) => {
      if (res && res.success) {
        window.location.reload();
      }
    });
  };
  const toggleAttendanceModal = (codeClass) => {
    setCodeClassId(() => codeClass );
    setOpenAttendanceModal(!openAttendanceModal);
  };
  const handleAttendanceSubmission = (codeClass, presentStudents) => {
    alert(codeClass)
    dispatch(
      addAttendanceInToClass( codeClass, {participants: presentStudents })
    ).then((res) => {
      if (res && res.success) {
        setOpenAttendanceModal(!openAttendanceModal);
      }
    });
  };
  const onDeleteAttendance = (participant) => {
    alert(participant);
    dispatch(deleteAttendanceFromClass({codeClass, participant})).then((res) => {
        if (res && res.success) {
          window.location.reload();
        }
      });
   };

  return (
    <>
      <div className=" mb-3">
        <button
          className="btn btn-warning shadow-0 mr-2 "
          onClick={() => openModal(codeClass, "batch")}
        >
          <i className="me-1 fa fa-plus "></i> Add Batches
        </button>
        <button
          className="btn btn-primary shadow-0 mr-2 "
          onClick={() => openModal(codeClass, "mentor")}
        >
          <i className="me-1 fa fa-plus "></i> Add Mentors
        </button>
        <button className="btn btn-light border border-secondary py-2 icon-hover px-3" onClick={() => toggleAttendanceModal(codeClass)}>
          <i className="me-1 fas fa-heart fa-lg"></i> Take Attendance
        </button>
      </div>
      <div className="border-top mr-5">
        <div className="row mx-0">
          <div className="col-12 col-md-12 col-lg-12 col-sm-12 border rounded  p-2 my-2 mr-3">
            <h5>Batch List :</h5>
            {codeClassDetails.batches &&
              codeClassDetails.batches.map((batch, i) => (
                <div className="row mx-0" key={batch?._id}>
                  <dd className="col-6" >
                    {batch?.title}:
                  </dd>
                  <dd className="col-6">
                    <FaTrash
                      className="text-danger"
                      onClick={() => onDeleteBatch(batch?._id)}
                    />
                  </dd>
                </div>
              ))}
          </div>
        </div>
        <div className="row mx-0">
          <div className="col-12 col-md-12 col-lg-12 col-sm-12 border rounded  p-2 my-2 mr-3">
            <h5>Mentors List :</h5>
            {codeClassDetails.mentors &&
              codeClassDetails.mentors.map((mentor, i) => (
                <div className="row mx-0"  key={mentor.mentor?._id}>
                  <dd className="col-6">
                    {mentor.mentor?.name}:
                  </dd>
                  <dd className="col-6">
                    <FaTrash
                      className="text-danger"
                      onClick={() => onDeleteMentor(mentor?._id)}
                    />
                  </dd>
                </div>
              ))}
          </div>
        </div>
        <div className="row mx-0  border rounded  p-2 my-2 mr-3">
        <h5>Present Students  List :</h5>
        <div className="col-12 col-md-12">
                        {codeClassDetails.attendance && codeClassDetails.attendance.map((presentStudent, i) => (
                        <div className="row mx-0" key={presentStudent?._id}>
                        <dd className="col-6" >
                        {presentStudent?.user?.firstName}:
                        </dd>
                        <dd className="col-6">
                        <FaTrash
                            className="text-danger"
                            onClick={() => onDeleteAttendance(presentStudent?._id)}
                        />
                        </dd>
                    </div>
              ))}
                    </div>
        </div>
        <div className="row mx-0">
            <div className="col-md-12 col-sm-12 col-lg-12 col-12  border rounded  p-2 my-2 mr-3">
            <h5>Participants List :</h5>
            {codeClassParticipants &&
                codeClassParticipants.map((student) => (
                  <div className="row mx-0" key={student?.codeUser?._id}>
                    <div className="col-6 col-md-6">
                      <p className="user-list" >
                        {student?.codeUser?.name}{" "}
                      </p>
                    </div>
                  </div>
                ))}
            </div>
        </div>
        
      </div>
      <AssignBatchMenterModal
        type={modelFor}
        model={showModel}
        openModal={openModal}
        closeModal={closeModal}
        codeClassId={codeClassId}
        batchesList={codeBatches}
        mentorList={mentorList}
        codeClassBatches={codeClassDetails.batches}
        codeClassMentors={codeClassDetails.mentors}
        handleAssignBatchesToClass={handleAssignBatchesToClass}
        handleAssignMentorInToClass={handleAssignMentorInToClass}
      />
      <AttendanceModel
        model={openAttendanceModal}
        toggle={toggleAttendanceModal}
        handleAttendanceSubmission={handleAttendanceSubmission}
        codeClassId={codeClassId}
        codeClassPresentList={codeClassDetails.attendance}
      />
    </>
  );
};

export default CodeClassManage;

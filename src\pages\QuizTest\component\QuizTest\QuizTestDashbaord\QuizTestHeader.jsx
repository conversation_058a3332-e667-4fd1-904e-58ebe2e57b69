import { useState } from "react";
import { useSelector } from "react-redux";
import { Link, useParams } from "react-router-dom";

import QuizInviteModal from "./InvitedTab/QuizInviteModal";

const QuizTestHeader = () => {
  const { id } = useParams();
  const { currentQuizTest } = useSelector((state) => state.quizTest);
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [isopen, setIsopen] = useState(false);

  const toggle = () => setIsopen(!isopen);

  return (
    <>
      <div className="row mx-0 w-100 px-1 py-md-2 align-items-center border-bottom quiz-test-header">
        {/* Back Button */}
        <div
          className="col-2 py-1 px-0 d-flex justify-content-start"
          style={{ maxWidth: "240px" }}
        >
          <div className="col-12 my-0 text-start align-items-center ">
            <Link to="/quiz/tests" className="d-flex gap-1 align-items-center">
              <i className="bi bi-arrow-left"></i> Back
            </Link>
          </div>
        </div>

        <div className="col-lg-10 py-1 d-flex justify-content-between align-items-center  px-0">
          <div className="row mx-0 w-100 justify-content-between align-items-center  ">
            <div className="col-5 col-lg-4 px-0  ">
              <h5 className="m-0 px-1">{currentQuizTest?.quizTitle}</h5>
            </div>

            <div className="col-7 col-lg-8 px-0 d-flex justify-content-end align-items-center">
              <button className="btn-invite mx-2 px-3" onClick={toggle}>
                <i className="bi bi-person-plus"></i> Invite Candidate
              </button>

              <div className="d-md-none dropdown">
                <button
                  className="btn "
                  type="button"
                  id="dropdownMenuButton"
                  data-bs-toggle="dropdown"
                  aria-expanded={dropdownOpen}
                  onClick={() => setDropdownOpen(!dropdownOpen)}
                >
                  <i className="bi bi-three-dots"></i>
                </button>
                <ul
                  className="dropdown-menu dropdown-menu-end"
                  aria-labelledby="dropdownMenuButton"
                >
                  <li>
                    <a className="dropdown-item" href="#">
                      <i className="bi bi-copy"></i> Copy Link
                    </a>
                  </li>
                  <li>
                    <Link className="dropdown-item" to={`/${id}/playground`}>
                      <i className="bi bi-box-arrow-up-right"></i> Preview
                    </Link>
                  </li>
                </ul>
              </div>

              <a href="#" className="mx-3 d-none d-md-block">
                <i className="bi bi-copy"></i> Copy Link
              </a>
              <Link to={`/${id}/playground`} className="mx-3 d-none d-md-block">
                <i className="bi bi-box-arrow-up-right"></i> Preview
              </Link>
            </div>
          </div>
        </div>
      </div>
      <QuizInviteModal toggle={toggle} isopen={isopen}/>
    </>
  );
};

export default QuizTestHeader;

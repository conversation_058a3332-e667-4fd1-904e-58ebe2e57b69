import React from 'react'

import WorkshopCard from "./WorkshopCard"

const ExploreTrainingBootcampSection = () => {
  const data = [
    {
      img: "http://res.cloudinary.com/datacode/image/upload/v1691170388/natghz9cxos2uyp4osxf.png",
      days: '3x Days',
      title: 'Full Stack Web Development Workshop',
      category: 'Programming',
      speaker: '',
      students: '100',
      rating: '3',
    },
    {
      img: "http://res.cloudinary.com/datacode/image/upload/v1691170388/natghz9cxos2uyp4osxf.png",
      days: '10x Days',
      title: 'Full Stack Web Development Workshop',
      category: 'Web Development',
      speaker: '',
      students: '140',
      rating: '5',
    },
    {
      img: "http://res.cloudinary.com/datacode/image/upload/v1691170388/natghz9cxos2uyp4osxf.png",
      days: '4x Days',
      title: 'Full Stack Web Development Workshop',
      category: 'Design',
      speaker: '',
      students: '500',
      rating: '5',
    },
  ]

  return (
    <>
      <div className="row mx-0 d-flex justify-content-center explore-training-body">
        <div className="col-md-10 col-12">
          <div className='my-5'>
            <div className='row mx-0'>
              <div className='col-8 '>
                <h1 className='pl-4 pt-5 pb-3'>Explore Trainings and Bootcamps</h1>
              </div>
            </div>
            <div className="row mx-0 px-4 py-2">
              {data && data.map((item, i) => (
                <div className="col-md-4 col-12 d-flex p-4" key={i}>
                  <WorkshopCard item={item} />
                </div>
              ))}
            </div>
          </div>
          <div className="text-center">
            <button className="explore-button">Explore All...</button>
          </div>
        </div>
      </div>
    </>
  )
}

export default ExploreTrainingBootcampSection
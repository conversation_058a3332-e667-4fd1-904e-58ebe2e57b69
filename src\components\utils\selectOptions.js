export const technologyOptions = [
  { tech: "Machine Learning", value: "machine_learning" },
  { tech: "Artificial Intelligence", value: "artificial_intelligence" },
  { tech: "DataScience", value: "datascience" },
  { tech: "Hadoop", value: "hadoop" },
  { tech: "Apache Spark", value: "apache_spark" },
  { tech: "Tensorflow", value: "tensorflow" },
  { tech: "Javascript", value: "Javascript" },
  { tech: "Android", value: "Android" },
  { tech: "Datastructures", value: "Datastructures" },
  { tech: "C++", value: "cplusplus" },
  { tech: "Java", value: "Java" },
  { tech: "React", value: "React" },
  { tech: "Html", value: "Html" },
  { tech: "Angular", value: "Angular" },
  { tech: "SQL", value: "SQL" },
  { tech: "Python", value: "Python" },
  { tech: "C", value: "C" },
  { tech: "Node", value: "Node" },
  { tech: "PHP", value: "PHP" },
  { tech: "Django", value: "Django" },
  { tech: "Bootstrap", value: "Bootstrap" },
  { tech: "Typescript", value: "Typescript" },
  { tech: "React Native", value: "react_native" },
  { tech: "Git", value: "Git" },
  { tech: "Flutter", value: "Flutter" },
  { tech: "Swift", value: "Swift" },
  { tech: "Redux", value: "Redux" },
  { tech: "Vue", value: "Vue" },
  { tech: "Software Testing", value: "software_testing" },
  { tech: "Computer Science", value: "computer_science" },
  { tech: "Ruby", value: "Ruby" },
  { tech: "SEO", value: "SEO" },
  { tech: "CSharp", value: "CSharp" },
  { tech: "jQuery", value: "jQuery" },
  { tech: "Go", value: "Go" },
  { tech: "Css", value: "Css" },
  { tech: "Linux", value: "Linux" },
];

export const sponsorType = [
  {
    type: "Commmunity Partner",
    value: "community_partner",
  },
  {
    type: "Food Sponsor",
    value: "food_sponsor",
  },
  {
    type: "Food Partner",
    value: "food_partner",
  },
  {
    type: "Venue Partner",
    value: "venue_partner",
  },
  {
    type: "Education Partner",
    value: "education_partner",
  },
  {
    type: "IT Partner",
    value: "it_partner",
  },
];

export const sponsorSubType = [
  {
    type: "Food Sponsor",
    value: "food_sponsor",
  },
  {
    type: "Powered By",
    value: "powered_by",
  },
];

export const virtualEventPlatform = [
  {
    type: "Google Meet",
    value: "google",
  },
  {
    type: "Zoom Meet",
    value: "zoom",
  },
  {
    type: "Microsoft Teams",
    value: "microsoftTeams",
  },
];

export const languageOptions = [
  { tech: "C++", value: "cplusplus" },
  { tech: "C", value: "c" },
  // { tech: "Java", value: "java" },
  // { tech: "Python", value: "python" },
];

export const techCompaniesOptions = [
  {
    img: "7.png",
    name: "facebook",
  },
  {
    img: "4.png",
    name: "amazon",
  },
  {
    img: "14.png",
    name: "netflix",
  },
  {
    img: "5.png",
    name: "google",
  },
  {
    img: "3.png",
    name: "ibm",
  },
  {
    img: "8.png",
    name: "cognizant",
  },
  {
    img: "9.png",
    name: "Tcs NQT",
  },
  {
    img: "10.png",
    name: "Mind Tree",
  },
  {
    img: "11.png",
    name: "Capgemini",
  },
  {
    img: "12.png",
    name: "Wipro",
  },
  {
    img: "13.png",
    name: "amdocs",
  },
  {
    img: "6.png",
    name: "cisco",
  },
  {
    img: "15.png",
    name: "FlipKart",
  },
  {
    img: "16.png",
    name: "Infosys",
  },
  {
    img: "17.png",
    name: "Persistent",
  },
  {
    img: "18.png",
    name: "HCL",
  },
  {
    img: "19.png",
    name: "Deloitte",
  },
];

export const categoryOptions = [
  { name: "Tutorial", value: "tutorial" },
  { name: "Notes", value: "notes" },
  { name: "Article", value: "article" },
  { name: "Lesson", value: "lesson" },
  { name: "Post", value: "post" },
];

export const statusOptions = [
  { status: "pending", value: 0 },
  { status: "accept", value: 3 },
  { status: "waiting", value: 1 },
  { status: "onhold", value: 2 },
  { status: "reject", value: 4 },
  { status: "flag", value: 5 },
  { status: "stage1", value: 6 },
  { status: "stage2", value: 7 },
];

export const resourceOptions = [
  { type: "Github", value: "github" },
  { type: "Doc", value: "docx" },
  { type: "Drive", value: "drive" },
  { type: "Figma", value: "figma" },
  { type: "Tutorial", value: "tutorial" },
];

export const speakerTypes = [
  { type: "Mentor", value: "mentor" },
  { type: "Assistant", value: "assistant" },
  { type: "Guest", value: "guest" },
  { type: "Public", value: "public" },
];

import { Link } from "react-router-dom";

import datacodelogo from "../../../../assets/images/datacodelogo.png"

const Header = () => {
  return (
    <nav className="navbar navbar-expand-lg row mx-0">
      <div className="col-12 d-flex justify-content-between align-items-center">
      <div className="d-flex  ps-lg-3">
          <img src={datacodelogo} alt="datacodelogo" height={50} />
        </div>
        <button
          className="navbar-toggler"
          type="button"
          data-bs-toggle="collapse"
          data-bs-target="./navbarNav"
          aria-controls="navbarNav"
          aria-expanded="false"
          aria-label="Toggle navigation"
        >
          <span className="navbar-toggler-icon"></span>
        </button>
        <div className="collapse navbar-collapse ps-lg-5  justify-content-between" id="navbarNav">
          <ul className="navbar-nav ps-lg-5 ps-3 gap-2">
            <Link to="/quiz/tests">
              <li className="nav-item">
                <a className="nav-link active" aria-current="page" href="./">
                  Assesments
                </a>
              </li>
            </Link>
            <li className="nav-item">
              <a className="nav-link" href="./">
                UpSkill
              </a>
            </li>
            <li className="nav-item">
              <a className="nav-link" href="./">
                Profiles
              </a>
            </li>
            <li className="nav-item">
              <a className="nav-link" href="./">
                Library
              </a>
            </li>
            <li className="nav-item">
              <a className="nav-link" href="./">
                Hackathons
              </a>
            </li>
          </ul>
          <ul className="navbar-nav ps-lg-5 ps-3">
            <li className="nav-item">
              <a className="nav-link" href="./">
                <i className="bi bi-person-plus"></i>
              </a>
            </li>
            <li className="nav-item">
              <a className="nav-link" href="./">
                <i className="bi bi-question-circle"></i>
              </a>
            </li>
            <li className="nav-item">
              <a className="nav-link" href="./">
                <i className="bi bi-1-circle-fill"></i>
              </a>
            </li>
          </ul>
        </div>
      </div>
    </nav>
  )
};

export default Header;       
import { useEffect,useState } from "react";
import { useDispatch } from "react-redux";
import { useParams } from "react-router-dom";

import { deleteEventFormFields, getEventFormFields } from "../../../../actions";
import ToolTip from "../../../../../../components/sharedComponents/ToolTip";
import EventRegFormPreviewLayout from "./EventRegFormPreviewLayout";

export const EventRegFormPreview = ({
  formName,
  eventFormFields,
  handelFormFieldOrder,
  handleFieldEdit,
  showDrawer,
}) => {

  const dispatch = useDispatch();
  const { id } = useParams();
  
  const [previewState, setPreviewState] = useState({});

  useEffect(() => {
    setPreviewState({});
  }, [formName]);

  const generateMultiOptionField = (formField) => {
    const { type, options } = formField;

    let ele = (
      <div className=''>
        {options &&
          options.map((option, index) => {
            return (
              <div key={index} className='form-check d-flex align-items-center'>
                <input
                  type={type}
                  name={option.name}
                  value={option.label}
                  onChange={(e) => handleOnChange(e)}
                  className='form-check-input'
                />
                <label className='form-check-label'>{option.label}</label>
              </div>
            );
          })}
      </div>
    );
    return ele;
  };

  const generateLinearScaleField = (formField) => {
    var rows = [];
    for (let i = formField.start; i <= formField.end; i++) {
      rows.push(
        <div key={i} className='col-1 px-0 d-flex align-items-center'>
          <input
            id={i}
            type='radio'
            name={formField.name}
            value={i}
            onChange={(e) => handleOnChange(e)}
          />
          <label htmlFor={i} className='ml-sm-2 ml-1 mb-0 d-flex '>
            {i}
          </label>
        </div>,
      );
    }
    return (
      <>
        <div className='row mx-0'>
          {rows.map((row) => {
            return row;
          })}
        </div>
      </>
    );
  };

  const renderFormFields = (formField) => {
    console.log(formField);
    const { name } = formField;

    const inputFieldsDictionary = {
      text: (
        <div className='form-group'>
          <input
            type='text'
            className='form-control'
            name={name}
            value={previewState[name]}
            onChange={(e) => handleOnChange(e)}
          />
        </div>
      ),
      textarea: (
        <div className='form-group'>
          <input
            type='textarea'
            className='form-control'
            name={name}
            value={previewState[name]}
          />
        </div>
      ),
      radio: generateMultiOptionField(formField),
      checkbox: generateMultiOptionField(formField),
      linearScale: generateLinearScaleField(formField),
    };

    return inputFieldsDictionary[formField.type];
  };

  const handelDeleteFormField = (formField) => {
    dispatch(
      deleteEventFormFields({ id: formField.id, eventId: formField.eventId }),
    ).then((res) => {
      if (res) {
        dispatch(getEventFormFields({ eventId: id, formName }));
      }
    });
  };

  const handleOnChange = (e) => {
    const { name, value } = e.target;
    return setPreviewState({ ...previewState, [name]: value });
  };

  const handlePrevFormSubmit = (e) => {
    e.preventDefault();
  };

  return (
    <>
      <h3>Preview</h3>
      <div className='card-body bg-white rounded-lg shadow border'>
        <form>
          {eventFormFields &&
            eventFormFields.map((formField) => (
              <EventRegFormPreviewLayout
                key={formField.id}
                label={formField.label}
                eventFormFields={eventFormFields}
                formField={formField}
                handelFormFieldOrder={handelFormFieldOrder}
                handelDeleteFormField={handelDeleteFormField}
                handleFieldEdit={handleFieldEdit}
                showDrawer={showDrawer}
              >
                {renderFormFields(formField)}
                <hr />
              </EventRegFormPreviewLayout>
            ))}
        </form>
        <ToolTip title='Save' placement='bottom'>
          <div className='save-btn' onClick={(e) => handlePrevFormSubmit(e)}>
            Save
          </div>
        </ToolTip>
      </div>
    </>
  );
};
export default EventRegFormPreview;

import React from "react";
import { DownOutlined } from '@ant-design/icons';
import { Checkbox, Dropdown, Space } from "antd";
import moment from "moment";

export const getColumns = (items, handleApprove, selectedBLog) => [
  {
    dataField: "",
    text: "Select", // Add Checkbox header
    headerAlign: "center",
    sort: true,
    style: { color: "#757575" },
    headerFormatter: () => <Checkbox />,
    headerClasses: "table-header s-no",
    formatter: (cell, row, index) => (
      <div key={`select-${row._id}`}>
        <Checkbox />
      </div>
    ),
  },
  {
    dataField: "user",
    text: "Blogs",
    headerAlign: "left",
    sort: true,
    style: { color: "#757575" },
    headerClasses: "table-header s-no pl-4",
    formatter: (cell, row, index) => (
      <div className="d-flex align-items-center" key={`blogs-${row._id}`}>
        <div>
          <div>
            <img
              className="bl"
              src="https://miro.medium.com/v2/resize:fit:679/0*ccCr8F6SL9sdxsfp.png"
              width="100px"
              height="100px"
              alt="Blog"
            />
          </div>
        </div>
        <div className="d-flex">
          <div className="titles mb-1 ml-1">{row.name}</div>
        </div>
      </div>
    ),
  },
  {
    dataField: "author",
    text: "Author",
    headerAlign: "left",
    sort: true,
    style: { color: "#757575" },
    headerClasses: "table-header s-no pl-4",
    formatter: (cell, row, index) => (
      <div className="d-flex align-items-center" key={`author-${row._id}`}>
        <div className="symbol symbol-circle symbol-50px overflow-hidden m">
          <div className="symbol-label">
            <img
              src={row.author?.imgUrl || "https://miro.medium.com/v2/resize:fill:20:20/1*abnkL8PKTea5iO2Cm5H-Zg.png"}
              alt="Author"
              width="50px"
              height="50px"
            />
          </div>
        </div>
        <div className="d-flex ml-3">
          <div className="text-gray-800 mb-1 ml-1">
            {row.author ? `${row.author.firstName} ${row.author.lastName}` : "Parag Jain"}
          </div>
        </div>
      </div>
    ),
  },
  {
    dataField: "updatedAt",
    text: "Posted",
    headerAlign: "left",
    align: "left",
    sort: true,
    style: { color: "#757575" },
    headerClasses: "table-header status",
    formatter: (cell, row, index) => {
      const date = moment(row.updatedAt);
      return <span key={`date-${row._id}`}>{date.format("MMMM Do YYYY")}</span>;
    },
  },
  {
    dataField: "",
    text: "Status",
    headerAlign: "center",
    align: "center",
    sort: true,
    style: { color: "#757575" },
    headerClasses: "table-header status",
    formatter: (cell, row, index) => (
      <div onClick={() => handleApprove(row._id)} key={`status-${row._id}`}>
        {row.isApproved === false ? (
          <span className="Disapprove px-2">disapproved</span>
        ) : (
          <span className="Approve px-2">Approved</span>
        )}
      </div>
    ),
  },
  {
    dataField: "",
    text: "Actions",
    sort: true,
    style: { color: "#757575" },
    align: "left",
    headerAlign: "left",
    headerClasses: "table-header status",
    formatter: (cell, row, index) => (
      <Dropdown
        menu={{ items }}
        trigger={'click'}
        key={`actions-${row._id}`}
      >
        <div
          style={{ backgroundColor: "white" }}
          className="btn bg-light"
          onClick={(e) => {
            e.preventDefault();
            selectedBLog.current = row._id;
          }}
        >
          <Space>
            Actions
            <DownOutlined />
          </Space>
        </div>
      </Dropdown>
    ),
  },
];

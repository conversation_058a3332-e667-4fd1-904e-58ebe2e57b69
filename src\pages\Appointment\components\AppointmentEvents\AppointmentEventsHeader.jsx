import { Link } from "react-router-dom";
import { IoIosArrowDown } from "react-icons/io";

import ToolTip from "../../../../components/sharedComponents/ToolTip";

const AppointmentEventsHeader = ({ toggle, handleTab }) => {
  return (
    <>
      <div className="appointment-header container-fluid">
        <div className="row mx-0 px-md-5 px-3">
          <div className="col-12">
            <div className="row mx-0">
              <div className="col-12 d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center py-3 gap-3">
                <h4 className="mb-2 mb-md-0">
                  My Schedules <IoIosArrowDown />
                </h4>
                <ToolTip title="Create appointment" placement="bottom">
                  <Link to="/appointment/event/new">
                    <span className="header-create-button btn btn-primary">
                      + Create
                    </span>
                  </Link>
                </ToolTip>
              </div>
            </div>
            <div className="row mx-0 appointment-header-list">
              <div className="col-12">
                <ul className="appointment-nav pb-0 mb-0 d-flex flex-wrap justify-content-start">
                  <ToolTip title="View scheduled events" placement="bottom">
                    <li
                      className={toggle === "upcoming" ? "active" : ""}
                      onClick={() => handleTab("upcoming")}
                    >
                      Scheduled Events
                    </li>
                  </ToolTip>
                  <ToolTip title="Past events details" placement="bottom">
                    <li
                      className={toggle === "past" ? "active" : ""}
                      onClick={() => handleTab("past")}
                    >
                      Past Events
                    </li>
                  </ToolTip>
                  <ToolTip title="ongoing events details" placement="bottom">
                    <li
                      className={toggle === "ongoing" ? "active" : ""}
                      onClick={() => handleTab("ongoing")}
                    >
                      OnGoing Events
                    </li>
                  </ToolTip>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default AppointmentEventsHeader;

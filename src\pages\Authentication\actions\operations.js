import axios from "axios";
import * as actions from "./actionCreators";
import { SET_LOGIN_USER_LOADING, SET_REVIEW_LIST_LOADING, SET_USER_PROFILE_LOADING, SET_USERS_LIST_LOADING } from "../constants";
import { triggerNotifier } from "../../../components/utils/notifier";
import toast from "react-hot-toast";
import { generateQueryParams } from "../../../components/utils";

const baseURL = process.env.REACT_APP_BASE_URL;

export const loginUser = (user) => (dispatch) => {
    dispatch({ type: SET_LOGIN_USER_LOADING });
    return axios
      .post(`${baseURL}/user/login`, user)
      .then((res) => {
        if (res.data && res.data.success === true) {
          dispatch(actions.setLoginUser({...res.data.data, remember : user.remember, token:res.data.token}));
          dispatch(actions.setLoginUserStatus(res.data.success));
          triggerNotifier({
            message: "Login Successfull",
            type: "success",
            duration: 1000,
            icon: "✅",
          });
          return { success: true, data: res.data };
        } else {
          dispatch({ type: SET_LOGIN_USER_LOADING });
          dispatch(actions.setLoginUserError(res.data.message));
          dispatch(actions.setLoginUserStatus(res.data.success));
          setTimeout(() => {
            dispatch(actions.setLoginUserError(null));
          }, 5000);
          triggerNotifier({
            message: "Login Failed",
            type: "success",
            duration: 1000,
            icon: "❌",
          });
          return { success: false };
        }
      })
      .catch((error, res) => {
        dispatch({ type: SET_LOGIN_USER_LOADING });
        dispatch(actions.setLoginUserError(error));
        setTimeout(() => {
          dispatch(actions.setLoginUserError(null));
        }, 5000);
      });
  };

  export const getUsersListDetails = () => (dispatch) => {
    dispatch({ type: SET_USERS_LIST_LOADING });
    return axios
      .get(`${baseURL}/user/fetch-users`)
      .then(({ data }) => {
        if (data?.success) {
          console.log(data)
          dispatch({ type: SET_USERS_LIST_LOADING });
          dispatch(actions.setUsersProfileList(data.Users))
          return { success: true, data: data?.Users };
        }
      })
      .catch((error) => {
        dispatch({ type: SET_USER_PROFILE_LOADING });
        console.log("get users list error", error);
         triggerNotifier({
        message: "Mentor availabilties Error",
        type: 'error',
        duration: 1000,
        icon: "⚠️",
      });
      });
  };

  export const getUserProfile = (id) => (dispatch) => {
    dispatch({ type: SET_USER_PROFILE_LOADING });
    return axios
      .get(`${baseURL}/user/get-user/${id}`)
      .then(({ data }) => {
        if (data?.success) {
          console.log(data)
          dispatch({ type: SET_USER_PROFILE_LOADING });
          dispatch(actions.setUserProfile(data?.user))
          return { success: true, data: data?.user };
        }
      })
      .catch((error) => {
        dispatch({ type: SET_USER_PROFILE_LOADING });
        console.log("get user profile error", error);
         triggerNotifier({
        message: "Mentor availabilties Error",
        type: 'error',
        duration: 1000,
        icon: "⚠️",
      });
      });
  };

  export const addReview = (data) => (dispatch) => {
    return axios
      .post(`${baseURL}/user/review/create`, data)
      .then((res) => {
        if (res.status === 200) {
          dispatch(getReviewsList());
          toast.success("Review created", {
            duration: 2000,
          });
          return { success: true, review: res?.data };
        } else {
          return { success: false };
        }
      })
      .catch((error) => {
        console.log("create review Error", error);
        triggerNotifier({
          message: error?.response?.data?.message,
          type: "error",
          duration: 2000,
          icon: "⚠️",
        });
      });
  };
  
  export const getReviewsList = ({user}) =>
    (dispatch) => {
      dispatch({ type: SET_REVIEW_LIST_LOADING });
      return axios
        .get(
          `${baseURL}/user/review${generateQueryParams({
           user
          })}`,
        )
        .then((res) => {
          if (res.status === 200) {
            console.log(res)
            dispatch({ type: SET_REVIEW_LIST_LOADING });
            dispatch(actions.setReviewList(res.data.reviews));
            return { success: true, data: res && res.data.reviews };
          } else {
            return { success: false };
          }
        })
        .catch((error) => {
          console.log("batch lesson List Error", error);
          triggerNotifier({
            message: error?.response?.data?.message,
            type: "error",
            duration: 2000,
            icon: "⚠️",
          });
        });
    };
  
  export const editReview = (data) => (dispatch) => {
    debugger
    return axios
      .patch(`${baseURL}/user/review/${data._id}`, {
        data: data,
      })
      .then((res) => {
        if (res.status === 200) {
          dispatch(getReviewsList());
          toast.success("review edited", {
            duration: 2000,
          });
          return { success: true, review: res && res.data };
        } else {
          return { success: false };
        }
      })
      .catch((error) => {
        console.log("edit review Error", error);
        triggerNotifier({
          message: error?.response?.data?.message,
          type: "error",
          duration: 2000,
          icon: "⚠️",
        });
      });
  };
  
  export const deleteReview = (id) => (dispatch) => {
    return axios
      .delete(
        `${baseURL}/user/review/${id}`,
      )
      .then((res) => {
        if (res.status === 200) {
          dispatch(getReviewsList())
          toast.success("review deleted", {
            duration: 2000,
          });
          return { success: true, review: res && res.data };
        } else {
          return { success: false };
        }
      })
      .catch((error) => {
        console.log("delete review Error", error);
        triggerNotifier({
          message: error?.response?.data?.message,
          type: "error",
          duration: 2000,
          icon: "⚠️",
        });
      });
  };
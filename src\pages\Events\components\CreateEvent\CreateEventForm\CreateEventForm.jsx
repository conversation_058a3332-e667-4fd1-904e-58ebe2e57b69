import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Field, reduxForm, getFormValues } from "redux-form";
import { connect, useDispatch, useSelector } from "react-redux";
import moment from "moment";
import { MdDelete } from "react-icons/md";

import {
  renderInputField,
  renderCheckboxField,
  renderRadioField,
  renderSelectField,
  renderRadioButton,
} from "../../../../../components/sharedComponents/ReduxFormFields";
import {
  required,
  dateTimeDiff,
} from "../../../../../components/utils/validators";
import CustomLoader from "../../../../../components/sharedComponents/CustomLoader";
import TextEditor from "../../../../../components/sharedComponents/TextEditor";
import { createEvent, editEvent, getEventDetails } from "../../../actions";
import BannerImageUpload from "../../../../../components/sharedComponents/BannerImageUpload";
import ToolTip from "../../../../../components/sharedComponents/ToolTip";

const CreateEventForm = ({
  reset,
  handleSubmit,
  submitting,
  initialize,
  eventDetails,
  formValues,
}) => {

  const navigate = useNavigate();
  const dispatch = useDispatch();

  const {
    currentUser: { userName },
    eventRegistationLoading,
  } = useSelector(({ event }) => event) || {};

  const [isEdit, setEdit] = useState(false);
  const [tags, setTags] = useState([]);
  const [uplodedImg, setUploadedImg] = useState("");
  const [htmlText, getHtmlText] = useState("<p> </p>");

  const registrationStatusOption = [
    {
      type: "Open",
      value: "open",
    },
    {
      type: "Close",
      value: "close",
    },
  ];

  const eventCategoryOptions = [
    {
      type: "Workshop",
      value: "workshop",
    },
    {
      type: "Tech-Session",
      value: "techsession",
    },
    {
      type: "Meet-Up",
      value: "meetup",
    },
    {
      type: "Certificate",
      value: "certificate",
    },
    {
      type: "SLC",
      value: "slc",
    },
    {
      type: "Webinar",
      value: "webinar",
    },
    {
      type: "Project",
      value: "project",
    },
    {
      type: "Mentorship",
      value: "mentorship",
    },
  ];


  useEffect(() => {
    if (eventDetails) {
      initialize(eventDetails);
      setEdit(true);
      getHtmlText(eventDetails.description);
      setTags(eventDetails.tags);
      setUploadedImg(eventDetails.eventImg);
    }
  }, [eventDetails]);

  const onSubmit = (values) => {
    console.log(values);
    const event = { ...values };
    event["userName"] = userName && userName;
    event["host"] = process.env.REACT_APP_ADMIN_AUTHOR_ID;
    event["type"] = event.type && event.type.value;
    event["description"] = htmlText;
    event["eventImg"] = uplodedImg;
    event["tags"] = tags;
    event["start_date_time"] = moment(
      event.start_date + " " + event.start_time,
    ).format();
    event["end_date_time"] = moment(
      event.end_date + " " + event.end_time,
    ).format();

    if (isEdit) {
      dispatch(editEvent(event)).then((res) => {
        if (res && res.success) {
          dispatch(getEventDetails(event._id));
        }
      });
    } else {
      event["id"] = Math.random().toString(36).substr(4, 9);
      dispatch(createEvent(event)).then((res) => {
        if (res && res.success) {
          navigate(`/event/dashboard/${res.event && res.event._id}`);
          reset("create-event");
        }
      });
    }
  };
  
  const handleTextEditor = (text) => {
    getHtmlText(text);
  };

  const addTags = () => {
    setTags([...tags, formValues.tag]);
    initialize({ ...formValues, tag: "" });
  };
  const handleTagDelete = (e) => {
    const data = tags.filter((item) => item !== e);
    setTags(data);
  };
  console.log(isEdit);
  return (
    <>
      <div className='event-basic-details bg-default py-3'>
        <h1
          className={`${
            !isEdit ? "text-center pt-4" : "px-4 "
          } register-text mb-5 mb-md-3 event-basic-details-heading`}
        >
          {isEdit
            ? "You can edit the event details here"
            : "Create Your Own event"}
        </h1>
        {isEdit ? (
          <>
            <div className=' px-4 py-2 event-basic-details-short-heading'>
              <h4>Basic Details</h4>
              <span>
                This information will be displayed publicly so be careful what
                you share.
              </span>
            </div>
          </>
        ) : (
          <>
            <div className='text-center event-basic-details-short-heading py-2'>
              <h4>Let's Plan Your Next Event!</h4>
              <span>
                We're so excited to hear that you want to host your next event.
                <br />
                Let's kick things off with some essential details!
              </span>
            </div>
          </>
        )}
        {eventRegistationLoading && eventRegistationLoading ? (
          <CustomLoader />
        ) : (
          <form className='' onSubmit={handleSubmit(onSubmit)}>
            <div
              className={`row mx-0 bg-default ${
                !isEdit ? "mt-5  px-3 pt-5 rounded-lg " : " px-4 mt-3"
              }   event-reg`}
            >
              {!isEdit ? (
                <div className='col-12 col-md-4 px-0 pr-5 py-2 event-basic-details-short-heading'>
                  <h2>Basic Details</h2>
                  <span>
                    This information will be displayed publicly so be careful
                    what you share.{" "}
                  </span>
                </div>
              ) : (
                ""
              )}
              <div
                className={`${
                  !isEdit ? "col-12 col-md-8 px-0 rounded-lg bg-default" : ""
                }   form-shadow  w-100`}
                id='form'
              >
                <div className=' px-5 py-4 login-card '>
                  <div>
                    <span className='form-label'>Upload Event Image</span>
                    {uplodedImg ? (
                      <div className='uploaded-img-preview border rounded-lg p-2 text-right'>
                        <ToolTip title='delete' color='#EE3D3D' placement='top'>
                          <span
                            className='cancel-img-btn'
                            onClick={() => setUploadedImg("")}
                          >
                            <MdDelete className='' size={"30px"} />
                          </span>
                        </ToolTip>
                        <img
                          src={uplodedImg}
                          alt='course-banner'
                          className='mx-auto img-fluid card-img'
                        />
                      </div>
                    ) : (
                      <BannerImageUpload setUploadedImg={setUploadedImg} />
                    )}
                  </div>
                  <Field
                    type='text'
                    name='title'
                    label='Event Name'
                    placeholder=''
                    component={renderInputField}
                    validate={[required]}
                  />
                  <span className='text-secondary'>
                    <b>
                      <i>Important:</i>{" "}
                    </b>
                    Use Specific Name for the Event.
                  </span>
                  <Field
                    type='text'
                    name='sub_title'
                    label='Event Sub Heading'
                    placeholder=''
                    component={renderInputField}
                    validate={[required]}
                  />
                  <div className='mt-3'>
                    <label className='form-label text-left pt-5'>
                      Event Description {`(${htmlText && htmlText.length - 8})`}
                    </label>
                    <div className='mb-3 border rounded-lg overflow-auto'>
                      {isEdit && eventDetails.description && (
                        <TextEditor
                          handleTextEditor={handleTextEditor}
                          text={eventDetails ? htmlText : ""}
                        />
                      )}
                      {!isEdit && (
                        <TextEditor
                          handleTextEditor={handleTextEditor}
                          text={eventDetails ? htmlText : ""}
                        />
                      )}
                    </div>
                  </div>
                  <div className='row mx-0 my-2  border rounded-lg '>
                    <div className='col-12 pt-3 align-items-center overflow-auto'>
                      <label className='form-label'>Ticket Type</label>
                      <div className='type-tag d-flex flex-wrap'>
                        <div className='type-checkbox'>
                          <Field
                            name='ticketType'
                            component={renderRadioButton}
                            label='Free'
                            value='free'
                            type='radio'
                            // disabled={isEdit}
                            validate={[required]}
                          />
                        </div>
                        <div className='mx-3 type-checkbox'>
                          <Field
                            name='ticketType'
                            component={renderRadioButton}
                            label='Paid'
                            value='paid'
                            type='radio'
                            // disabled={isEdit}
                            validate={[required]}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className='row'>
                    <div className='col-sm-6'>
                      <Field
                        type='date'
                        name='start_date'
                        label='Event Start Date'
                        placeholder=''
                        component={renderInputField}
                        validate={[required]}
                      />
                    </div>
                    <div className='col-sm-6'>
                      <Field
                        type='time'
                        name='start_time'
                        label='Event Start Time'
                        placeholder=''
                        component={renderInputField}
                        validate={[required]}
                      />
                    </div>
                  </div>
                  <div className='row'>
                    <div className='col-sm-6'>
                      <Field
                        type='date'
                        name='end_date'
                        label='Event End Date'
                        placeholder='<EMAIL>'
                        component={renderInputField}
                        validate={[required]}
                        min={formValues?.start_date}
                      />
                    </div>
                    <div className='col-sm-6'>
                      <Field
                        type='time'
                        name='end_time'
                        label='Event End Time'
                        placeholder=''
                        component={renderInputField}
                        validate={[required]}
                      />
                    </div>
                  </div>

                  <div className='row'>
                    <div className='col-sm-6'>
                      <Field
                        name='registrationStatus'
                        label='Registration Status'
                        placeholder=''
                        options={registrationStatusOption}
                        textField={"type"}
                        component={renderSelectField}
                        validate={[required]}
                      />
                    </div>
                    <div className='col-sm-6'>
                      <Field
                        type='text'
                        name='slug'
                        label='Slug'
                        placeholder=''
                        component={renderInputField}
                        validate={[required]}
                      />
                    </div>
                  </div>
                  <div className='row my-5'>
                    <div className='col-sm-12'>
                      <h6>Events Tags</h6>
                      <div className='border rounded'>
                        <div className='border rounded-lg card-schadow p-3 overflow-auto'>
                          <div className='row mx-0'>
                            {tags &&
                              tags.map((item, i) => (
                                <div
                                  key={i}
                                  className='p-2  border d-flex flex-wrap gap-2 align-items-center justify-content-between'
                                >
                                  <h6 className='mb-0'>{item}</h6>
                                  <i
                                    className='fal fa-times'
                                    onClick={() => handleTagDelete(item)}
                                  />
                                </div>
                              ))}
                          </div>
                          <div className='mt-4'>
                            <Field
                              type='text'
                              name='tag'
                              label='Add Tags'
                              placeholder=''
                              component={renderInputField}
                            />
                            <div className='d-flex flex-wrap mt-3 justify-content-end'>
                              <button
                                className='btn btn-sm btn-primary'
                                type='button'
                                onClick={() => addTags()}
                                id='add-tags'
                              >
                                Add
                              </button>
                              <ToolTip
                                message='Add tags'
                                placement='bottom'
                                id='add-tags'
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div
              className={`row m-0 ${
                !isEdit ? "mt-3 px-3 pt-5" : " px-4 mt-4"
              }  event-reg`}
            >
              {isEdit ? (
                <div className='px-md-2 px-0  py-3 w-100 event-basic-details-short-heading '>
                  <h4>Event Details</h4>
                  <span>
                    Event settings can’t be changed once the event is created.
                    Learn more
                  </span>
                </div>
              ) : (
                <div className='col-12 col-md-4 pr-5 py-2 event-basic-details-short-heading'>
                  <h2>Settings</h2>
                  <span>
                    Event settings can’t be changed once the event is created.
                    Learn more
                  </span>
                </div>
              )}
              <div
                className={`${
                  !isEdit ? "col-12 col-md-8 px-0" : " w-100"
                }  border rounded-lg form-shadow`}
                id='form'
              >
                <div className='p-md-5 px-3 login-card '>
                  <div className='border rounded-lg p-3 mt-0 mt-sm-3'>
                    <label className='form-label'>Event Access</label>
                    <div className='type-tag d-flex flex-wrap'>
                      <div className='type-checkbox'>
                        <Field
                          name='event_access'
                          component={renderRadioField}
                          value='everyone'
                          label='Everyone'
                          type='radio'
                          validate={[required]}
                        />
                      </div>
                      <div className='mx-3 type-checkbox'>
                        <Field
                          name='event_access'
                          component={renderRadioField}
                          value='invite_only'
                          label='Invite Only'
                          type='radio'
                          validate={[required]}
                        />
                      </div>
                    </div>
                    {formValues?.event_access === "invite_only" && (
                      <small>
                        a join link will only be sent to manually approved
                        participants
                      </small>
                    )}
                  </div>
                  <div className='border rounded-lg p-3 mt-0 mt-sm-3'>
                    <label className='form-label'>This Event is:</label>
                    <div className='type-tag d-flex flex-wrap'>
                      <div className='type-checkbox'>
                        <Field
                          name='event_type'
                          component={renderRadioField}
                          value='public'
                          label='Public'
                          type='radio'
                          validate={[required]}
                        />
                      </div>
                      <div className='mx-3 type-checkbox'>
                        <Field
                          name='event_type'
                          component={renderRadioField}
                          value='private'
                          label='Private'
                          type='radio'
                          validate={[required]}
                        />
                      </div>
                    </div>
                  </div>
                  <div className='border rounded-lg p-3 mt-3'>
                    <label className='form-label'>This Event is:</label>
                    <div className='type-tag d-flex flex-wrap'>
                      <div className='type-checkbox'>
                        <Field
                          name='fees'
                          component={renderRadioField}
                          value='free'
                          label='Free'
                          type='radio'
                          validate={[required]}
                        />
                      </div>
                      <div className='mx-3 type-checkbox'>
                        <Field
                          name='fees'
                          component={renderRadioField}
                          value='paid'
                          label='Paid'
                          type='radio'
                          validate={[required]}
                        />
                      </div>
                    </div>
                  </div>
                  <div className='border rounded-lg p-3 mt-0 mt-sm-3'>
                    <label className='form-label'>Event Category</label>
                    <div className='type-checkbox mt-n3'>
                      <Field
                        name='category'
                        component={renderSelectField}
                        placeholder=''
                        textField={"type"}
                        options={eventCategoryOptions}
                        validate={[required]}
                      />
                    </div>
                  </div>
                  <div className='row mx-0 mt-3'>
                    <div className='col-12 border rounded-lg p-3'>
                      <div className=''>
                        <label className='form-label'>Event Mode:</label>
                        <div className='event-mode d-flex flex-wrap'>
                          <Field
                            name='event_mode'
                            component={renderRadioField}
                            value='virtual'
                            label='Virtual'
                            type='radio'
                            validate={[required]}
                          />
                          <div className='mx-3'>
                            <Field
                              name='event_mode'
                              component={renderRadioField}
                              value='inPerson'
                              label='In Person'
                              type='radio'
                              validate={[required]}
                            />
                          </div>
                        </div>
                      </div>
                      {formValues?.event_mode === "virtual" && (
                        <div className=''>
                          <div className='mt-2 px-0 text-left border rounded-lg p-3'>
                            <label className='form-label'>Event Platform</label>
                            <Field
                              name='event_platform'
                              component={renderRadioField}
                              value='google_meet'
                              label='Google Meet'
                              type='radio'
                              validate={[required]}
                            />
                            <Field
                              name='event_platform'
                              component={renderRadioField}
                              value='zoom'
                              label='Zoom'
                              type='radio'
                              validate={[required]}
                            />
                          </div>
                        </div>
                      )}
                      {formValues?.event_mode === "inPerson" && (
                        <div className='m-0 mt-5'>
                          <Field
                            type='textarea'
                            name='address'
                            label='Address'
                            placeholder=''
                            component={renderInputField}
                            validate={[required]}
                          />
                        </div>
                      )}
                      <div className=''>
                        <div className='d-flex flex-wrap align-items-center mt-md-4'>
                          <Field
                            type='text'
                            name='useRegForm'
                            label={`Use a Datacode registration form`}
                            placeholder=''
                            component={renderCheckboxField}
                            validate={[required]}
                          />
                          <small className='ml-2 mb-0'>
                            ( you can download attendee details )
                          </small>
                        </div>
                        {(formValues?.event_mode === "virtual" ||
                          !formValues?.useRegForm) && (
                          <Field
                            type='text'
                            name='registrationLink'
                            label={`Enter ${
                              formValues?.useRegForm &&
                              formValues?.event_mode === "virtual"
                                ? "Broadcast"
                                : "Registration"
                            } Link`}
                            placeholder=''
                            component={renderInputField}
                            validate={[required]}
                          />
                        )}
                        {formValues?.event_mode === "virtual" && (
                          <div className='mt-4'>
                            <Field
                              name='is_broadcast_link'
                              component={renderCheckboxField}
                              label='This is Broadcast Link'
                            />
                            <small className='mb-0'>
                              if registration link is same as broadcase link
                            </small>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className='row mx-1 my-4'>
              <div className='col-12 text-right'>
                <button
                  type='submit'
                  className='btn custom-button'
                  disabled={submitting}
                >
                  <span>{isEdit ? "Edit Event" : "Create Event"}</span>
                </button>
              </div>
            </div>
          </form>
        )}
      </div>
    </>
  );
};

export default connect((state) => ({
  formValues: getFormValues("create-event")(state),
}))(
  reduxForm({
    form: "create-event",
    fields: ["tag"],
    validate: dateTimeDiff,
  })(CreateEventForm),
);

import React, { useEffect, useState } from 'react'
import { reduxForm } from 'redux-form'

import TextEditor from '../../../../../../components/sharedComponents/TextEditor'

const ReferenceSection = ({
  reset, handleSubmit, submitting,
  id, lessonDetails, editLesson
}) => {

  const [referenceText, getReferenceText] = useState('<p> </p>')
  const [isEdit, setEdit] = useState(false)

  useEffect(() => {
    setEdit(true)
    getReferenceText(lessonDetails && lessonDetails.references)
  }, [])

  const handleReferenceText = (text) => {
    getReferenceText(text)
  }

  const onSubmit = (values) => {
    const lesson = { ...values }
    lesson['references'] = referenceText
    if (id) {
      lesson['id'] = id
      editLesson(lesson)
    }
    reset('edit-lesson')
  }

  return (
    <>
      <form className="p-3 px-1" onSubmit={handleSubmit(onSubmit)}>
        <small>
          Lorazepam, sold under the brand name <PERSON>ivan among others, is a benzodiazepine medication. It is used to treat anxiety disorders, trouble sleeping, severe agitation, active seizures including status epilepticus, alcohol withdrawal, and chemotherapy-induced nausea and vomiting
        </small>
        <div className="my-3 lesson-texteditor">
          <label className="form-label text-left pt-5">References {`(${referenceText && referenceText.length - 8})`}</label>
          <div className="mb-3 border ">
            {(isEdit && lessonDetails && lessonDetails.references) &&
              <TextEditor handleTextEditor={handleReferenceText} text={lessonDetails.references ? referenceText : ''} />
            }
            {(!isEdit) &&
              <TextEditor handleTextEditor={handleReferenceText} text={lessonDetails.references ? referenceText : ''} />
            }
          </div>
        </div>
        <div className="row my-4">
          <div className="col-12 text-right">
            <button type="submit" className="btn custom-border-capsule-button" disabled={submitting}>
              <span>Save</span>
            </button>
          </div>
        </div>
      </form>
    </>
  )
}

export default reduxForm({
  form: 'edit-lesson',
})(ReferenceSection);

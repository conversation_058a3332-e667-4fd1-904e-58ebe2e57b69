import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useParams } from "react-router";
import BootstrapTable from "react-bootstrap-table-next";

import { getColumns } from "./helpers";
import { getEventDetails } from "../../../actions";

const EventFeedbacks = () => {

  const dispatch = useDispatch();
  const {id} = useParams()
  
  const { eventDetails} = useSelector((state) => state.event) || {}

  const [feedbacksData, setFeedbacksData] = useState();
  let columns = getColumns();

  useEffect(() => {
    dispatch(getEventDetails(id))
  }, [dispatch , id]);

  
  useEffect(() => {
    if (eventDetails) {
      setFeedbacksData(eventDetails.feedbacks);
    }
  },[eventDetails]);

  return (
    <>
      <h1>Feedback Details</h1>
      {feedbacksData && feedbacksData.length && (
        <div className='table table-responsive'>
          <>
            <BootstrapTable
              keyField='id'
              bordered={true}
              data={feedbacksData}
              columns={columns}
              hover={true}
            />
          </>
        </div>
      )}
    </>
  );
};

export default EventFeedbacks;

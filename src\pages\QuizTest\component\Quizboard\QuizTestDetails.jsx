import { useState } from "react";
//import { useSelector } from "react-redux";
import { FaChevronDown, FaChevronUp } from "react-icons/fa6";
import { FaRegCalendarCheck } from "react-icons/fa";

import QuizTestCard from "../ui/QuizTestCard.jsx";

const QuizTestDetails = () => {

 // const { quizTestsList } = useSelector((state) => state.quizTest);
  
  const [onGoingTestToggle, setOnGoingTestToggle] = useState(false);
  const [previousTestToggle, setPreviousTestToggle] = useState(false);

  const data = [
    {
      quizTitle: "Mock Placement Test -Set-1 | DataCode",
      date: "20-12-2024",
      inviteType: "Public",
      duration: "1 hr",
      detail: "31 Candidates have taken test",
    },
    {
      quizTitle: "Shoot Py_2.Online Round",
      date: "22-06-2024",
      inviteType: "Public",
      duration: "40 min",
      detail: "85 Candidates have taken test",
    },
    {
      quizTitle: "Shoot Py_2.Online Round",
      date: "26-12-2020",
      inviteType: "Public",
      duration: "40 min",
      detail: "85 Candidates have taken test",
    },
  ];

  const currentDate = new Date();

  const parseCustomDate = (dateString) => {
    const [day, month, year] = dateString
      .split("-")
      .map((part) => parseInt(part, 10));
    return new Date(year, month - 1, day);
  };

  const ongoingTests = data.filter((test) => {
    const testDate = parseCustomDate(test.date);
    return testDate >= currentDate;
  });

  const previousTests = data.filter((test) => {
    const testDate = parseCustomDate(test.date);
    return testDate < currentDate;
  });

  const handleToogle = (toggle, setToggle) => () => {
    setToggle(!toggle);
  };

  return (
    <>
      <div className='quiz-test-details-container col-12 py-4 px-5'>
        <h4 className='section-title mb-4'>Quiz Test Details</h4>

        <div className='test-section'>
          <div
            className='test-header d-flex align-items-center justify-content-between py-3'
            onClick={handleToogle(onGoingTestToggle, setOnGoingTestToggle)}
          >
            <div className='d-flex align-items-center'>
              <span className='icon px-2'>
                {onGoingTestToggle ? <FaChevronUp /> : <FaChevronDown />}
              </span>
              <span className=' mx-2'>
                <FaRegCalendarCheck />
              </span>
              <h5 className='test-title'>
                OnGoing Tests ({ongoingTests.length})
              </h5>
            </div>
          </div>
          <div
            className={`toggle-container ${
              onGoingTestToggle ? "show" : "hide"
            }`}
          >
            {ongoingTests.length > 0 ? (
              ongoingTests.map((item, index) => (
                <div className='mt-3'>
                  <QuizTestCard key={index} item={item} index={index} />
                </div>
              ))
            ) : (
              <div className='empty-message text-center py-3'>
                No Ongoing Test
              </div>
            )}
          </div>
        </div>

        <div className='test-section mt-4'>
          <div
            className='test-header d-flex align-items-center justify-content-between py-3'
            onClick={handleToogle(previousTestToggle, setPreviousTestToggle)}
          >
            <div className='d-flex align-items-center'>
              <span className='icon px-2'>
                {previousTestToggle ? <FaChevronUp /> : <FaChevronDown />}
              </span>
              <span className=' mx-2'>
                <FaRegCalendarCheck />
              </span>
              <h5 className='test-title'>
                Previously Conducted Tests ({previousTests.length})
              </h5>
            </div>
          </div>
          <div
            className={`toggle-container ${
              previousTestToggle ? "show" : "hide"
            }`}
          >
            {previousTests.length > 0 ? (
              previousTests.map((item, index) => (
                <div className='mt-3'>
                  <QuizTestCard key={index} item={item} index={index} />
                </div>
              ))
            ) : (
              <div className='empty-message text-center py-3 border'>
                No Previously Conducted Test
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  );
};

export default QuizTestDetails;

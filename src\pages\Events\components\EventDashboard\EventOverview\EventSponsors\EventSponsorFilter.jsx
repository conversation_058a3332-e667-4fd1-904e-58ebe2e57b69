import { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Combobox } from "react-widgets";
import { useParams } from "react-router";
import { Link } from "react-router-dom";
import { FaPlus } from "react-icons/fa6";

import { addEventSponsor } from "../../../../actions";
import { getSponsors, setSponsorsList } from "../../../../../Sponsors/actions";
import ToolTip from "../../../../../../components/sharedComponents/ToolTip";
import AddEventSponsorModal from "./AddEventSponsorModal";
import { sponsorType } from "../../../../../../components/utils/selectOptions";

const EventSponsorFilter = () => {
  
  const dispatch = useDispatch();
  const { id } = useParams();

  const { sponsorsList } = useSelector(({ sponsor }) => sponsor) || {};

  const [filterQuery, setFilterQuery] = useState({
    search: "",
    type: "",
  });
  const [openModal, setOpenModal] = useState(false);
  const [tempSponsorData, setTempSponsorData] = useState();

  const toggleModal = () => {
    setOpenModal(!openModal);
  };
  const handleChangeFilterQuery = (event) => {
    const { name, value } = event.target;
    setFilterQuery({ ...filterQuery, [name]: value });

    if (filterQuery.search === " " || filterQuery.search === "") {
      dispatch(setSponsorsList({ Speakers: [] }));
    }
    dispatch(getSponsors({ ...filterQuery, [name]: value }));
  };

  const handleOpenModal = (sponsor) => {
    setOpenModal(true);
    setTempSponsorData(sponsor);
  };
  const handleAddEventSponsor = (data) => {
    dispatch(
      addEventSponsor({ eventId: id, sponsor: data._id, type: data.type })
    ).then((res) => {
      if (res) {
        setOpenModal(false);
        setTempSponsorData(null);
      }
    });
  };

  return (
    <>
      <div className="row mx-0">
        <div className="col-12 ">
          <div className="search-section">
            {/* <h5 className='mb-0 p-2 '>Search Sponsor</h5> */}

            <Link
              to={{
                pathname: "/sponsor/new",
                search: "?type=guest",
              }}
            >
              <ToolTip title="Add Sponser" placement="bottom">
                <button type="button" className="add-guest">
                  Add Guest Sponsor
                </button>
              </ToolTip>
            </Link>
          </div>
          <div className="row mx-0">
            <div className="col-12 px-0">
              <div className="row mx-0">
                <div className="col-12 px-0">
                  <label className="form-label">Search</label>
                  <input
                    className="form-control"
                    type="type"
                    name="search"
                    value={filterQuery.search}
                    onChange={(e) => handleChangeFilterQuery(e)}
                  />
                </div>
                <div className="col-12 px-0 my-2">
                  <div className="text-left form-set">
                    <label className="form-label">Technology</label>
                    <Combobox
                      data={sponsorType}
                      dataKey={"value"}
                      textField={"type"}
                      placeholder={"Select Type"}
                      value={filterQuery.type}
                      onChange={(e) =>
                        handleChangeFilterQuery({
                          target: { name: "type", value: e.value },
                        })
                      }
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
          {
            <div className="row mx-0 px-3 event-sponsor-search-table">
              {sponsorsList &&
                sponsorsList.map((sponsor) => (
                  <div className="col-12 row mx-0 sponsor-search-card  ">
                    <h6 className="col-2 mb-0">{sponsor.name}</h6>
                    <p className="col-3 text-truncate">{sponsor.bio}</p>
                    <div className="col-5 mx-2">
                      {sponsor.technologies &&
                        sponsor.technologies.map((item) => (
                          <span className="border badge bg-secondary text-white mx-2">
                            {item.tech}
                          </span>
                        ))}
                    </div>
                    <FaPlus
                      className="add"
                      onClick={() => handleOpenModal(sponsor)}
                    />
                  </div>
                ))}
            </div>
          }
        </div>
      </div>
      <AddEventSponsorModal
        open={openModal}
        toggle={toggleModal}
        onSubmit={handleAddEventSponsor}
        data={tempSponsorData}
        submitButtonName={"Add Sponsor"}
        message={"Are you sure want to delete course"}
      />
    </>
  );
};

export default EventSponsorFilter;

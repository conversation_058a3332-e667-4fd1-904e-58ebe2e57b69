import React, { useEffect, useState, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";

import {
  deleteCodeBatch,
  getCodeBatches,
  getCodeClasses,
  getMoreCodeBatches,
  resetCodeBatches,
} from "../../../actions";
import CustomLoader from "../../../../../components/sharedComponents/CustomLoader";
import LayoutContainer from "../LayoutContainer";
import BatchCard from "./BatchCard";
import DeleteModal from "../../../../../components/sharedComponents/DeleteModal";
import warning from "../../../../../assets/audio/quotwarningquot-175692.mp3";
import pointCloseClick from "../../../../../assets/audio/pointCloseClick.mp3";
import { getOptions } from "../../../../../components/utils";
import {
  CategoryOptions,
  LimitOptions,
  RegistrationOptions,
  TypeOptions,
  statusOptions,
} from "../helpers";
import { Combobox } from "react-widgets";
import { Button, Space } from "antd";
import { FaFilter } from "react-icons/fa";
import AddClassIntoBatchModal from "./AddClassIntoBatchModal";

const DaysCodeBatches = () => {
  
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const [schedule, setSchedule] = useState("all");
  const [registration, setRegistration] = useState(null);
  const [status, setStatus] = useState(null);
  const [limit, setLimit] = useState(4);
  const [type, setType] = useState(null);
  const [category, setCategory] = useState(null);
  const [filterQuery, setFilterQuery] = useState("");
  const [hasMore, setHasMore] = useState(true); // New state for checking if more data is available

  const warningAudio = new Audio(warning);
  const pointCloseClickAudio = new Audio(pointCloseClick);

  const {
    codeBatches,
    codeBatchesLoading,
    moreCodeBatchesLoading,
    codeClassess,
    codeBatchesCount,
  } = useSelector((state) => state.dayscode) || {};

  const [openModal, setOpenModal] = useState(false);
  const [tempDeleteId, setTempDeleteId] = useState();
  const [isOpen, setIsOpen] = useState();
  const pagesCounter = useRef(2);
  const [openClassModal, setOpenClassModal] = useState(false);
  const [tempBatchId, setTempBatchId] = useState(null);

  const debounce = (func, delay) => {
    let timeoutId;
    return (...args) => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
      timeoutId = setTimeout(() => {
        func(...args);
      }, delay);
    };
  };

  useEffect(() => {
    dispatch(getCodeClasses());
    return () => {
      dispatch(resetCodeBatches());
    };
  }, [dispatch]);

  useEffect(() => {
    const debounceTimeout = setTimeout(() => {
      dispatch(
        getCodeBatches({
          schedule: schedule,
          registrationStatus: registration,
          limit: limit,
          status: status,
          search: filterQuery,
          type: type,
          category: category,
          page: 0,
        }),
      ).then((res) => {
        setHasMore(res?.batches.length > 0); // Update hasMore based on the response
        pagesCounter.current = 1; // Reset the page counter
      });
    }, 800);
    return () => clearTimeout(debounceTimeout);
  }, [filterQuery, schedule]);

  useEffect(() => {
    if (isOpen) {
      document.addEventListener("click", handleOutsideClick);
    }
    return () => {
      document.removeEventListener("click", handleOutsideClick);
    };
  }, [isOpen]);

  useEffect(() => {
    const handleScroll = debounce(() => {
      if (
        window.innerHeight + window.scrollY >= document.body.offsetHeight &&
        hasMore
      ) {
       
        dispatch(
          getMoreCodeBatches({
            schedule: schedule,
            registrationStatus: registration,
            limit: limit,
            status: status,
            search: filterQuery,
            type: type,
            category: category,
            page: pagesCounter.current,
          }),
        ).then((res) => {
          setHasMore(res?.batches.length > 0);
        pagesCounter.current += 1;
        });
      }
    }, 100);

    window.addEventListener("scroll", handleScroll);

    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, [getMoreCodeBatches, hasMore]); // Include hasMore in the dependency array

  const toggleModal = () => {
    pointCloseClickAudio.play();
    setOpenModal(!openModal);
  };

  const toggleClassModal = () => {
    setOpenClassModal(!openClassModal);
  };

  const handleRemoveBatch = () => {
    dispatch(deleteCodeBatch(tempDeleteId)).then((res) => {
      if (res) {
        setOpenModal(!openModal);
      }
    });
  };

  const deleteBatch = (id) => {
    setTempDeleteId(id);
    warningAudio.play();
    setOpenModal(!openModal);
  };
  const addClass = (id) => {
    setTempBatchId(id);
    setOpenClassModal(true);
  };
  const handleReset = () => {
    setSchedule();
    setRegistration();
    setCategory();
    setType();
    setLimit();
    setSchedule();
  };
  const handleFilterChange = () => {
    dispatch(
      getCodeBatches({
        schedule: schedule,
        registrationStatus: registration,
        limit: limit,
        status: status,
        search: filterQuery,
        type: type,
        category: category,
        page: 0,
      }),
    );
    setIsOpen(false);
  };
  const handleOutsideClick = (event) => {
    if (!event.target.closest(".filter")) {
      setIsOpen(false);
    }
  };

  useEffect(() => {
     console.log(hasMore,"hasmore");
   }, [hasMore])
  return (
    <>
      <LayoutContainer>
        <div className='row mx-0 solution-nav mb-3'>
          <div className='col-md-8 col-6 d-flex align-items-center'>
            <h4 className='py-md-3 py-2 mb-0'>
              <i className='fas fa-file-alt mr-2' />
              Batches
            </h4>
          </div>
          <div className='col-md-4 col-6 d-flex justify-content-end align-items-center'>
            <button
              onClick={() =>
                navigate("/admin/days_code/batch/new", { state: "createbatch" })
              }
              type='button'
              className='btn enroll-small-btn'
            >
              <small>
                <i className='fas fa-plus-circle mr-2' />
                Create New Batch
              </small>
            </button>
          </div>
        </div>
        {/* {codeBatchesLoading ? (
          <div className='d-flex justify-content-center align-items-center'>
            <CustomLoader />
          </div>
        ) : ( */}
        <>
          <div className='row mx-0'>
            <div className='col-12 mb-4'>
              <div className='d-flex justify-content-between align-items-center batch-dashboard-header'>
                <ul className='nav nav-tabs'>
                  <li className='nav-item d-flex justify-content-center'>
                    <span
                      className={`nav-link ${
                        schedule === "live" ? "active" : ""
                      }`}
                      onClick={() => setSchedule("live")}
                    >
                      Live
                      <span className='badge'>{codeBatchesCount?.live}</span>
                    </span>
                  </li>
                  <li className='nav-item d-flex justify-content-center'>
                    <span
                      className={`nav-link ${
                        schedule === "upcoming" ? "active" : ""
                      }`}
                      onClick={() => setSchedule("upcoming")}
                    >
                      Upcoming
                      <span className='badge'>
                        {codeBatchesCount?.upcoming}
                      </span>
                    </span>
                  </li>
                  <li className='nav-item d-flex justify-content-center'>
                    <span
                      className={`nav-link ${
                        schedule === "past" ? "active" : ""
                      }`}
                      onClick={() => setSchedule("past")}
                    >
                      Past
                      <span className='badge'>{codeBatchesCount?.past}</span>
                    </span>
                  </li>
                  <li className='nav-item d-flex justify-content-center'>
                    <span
                      className={`nav-link ${
                        schedule === "all" ? "active" : ""
                      }`}
                      onClick={() => setSchedule("all")}
                    >
                      All{" "}
                      <span className='badge'>
                        {codeBatchesCount?.upcoming +
                          codeBatchesCount?.live +
                          codeBatchesCount?.past}
                        {/* {codeBatchesCount?.all} */}
                      </span>
                    </span>
                  </li>
                </ul>
                <div className='d-flex align-items-center '>
                  <div className=' '>
                    <div className=''>
                      {/* <label className='form-label'>Search Name</label> */}
                      <input
                        placeholder='&#x1F50E; Search'
                        className='form-control text-center'
                        type='type'
                        name='search'
                        value={filterQuery}
                        onChange={(e) => setFilterQuery(e.target.value)}
                      />
                    </div>
                  </div>
                  <div className='mx-5 filter-Modal-content'>
                    <Space>
                      <Button
                        className='filter-modal-button'
                        onClick={() => setIsOpen(!isOpen)}
                      >
                        <FaFilter className='mr-2' />
                        Filter
                      </Button>
                    </Space>
                    {isOpen ? (
                      <div className='filter'>
                        <div>
                          <h4>Filter Options</h4>
                        </div>
                        <div className='empty-line'></div>
                        <div className='row'>
                          <div className='my-1 col-12'>
                            <label className='mr-4 form-label'>
                              Registration:
                            </label>
                            <Combobox
                              data={getOptions(
                                RegistrationOptions,
                                "Registrations",
                              )}
                              dataKey={"value"}
                              textField='Registrations'
                              placeholder={"Registrations"}
                              value={registration}
                              onChange={(value) => {
                                if (value.value === "all") {
                                  setRegistration(null);
                                } else {
                                  setRegistration(value.value);
                                }
                              }}
                            />
                          </div>
                          <div className=' my-1 col-12'>
                            <label className='mr-4 form-label'>Status:</label>
                            <Combobox
                              data={getOptions(statusOptions, "status")}
                              dataKey={"value"}
                              textField='status'
                              placeholder={"status"}
                              value={status}
                              onChange={(value) => {
                                if (value.value === "all") {
                                  setStatus(null);
                                } else {
                                  setStatus(value.value);
                                }
                              }}
                            />
                          </div>
                          <div className=' my-1 col-6'>
                            <label className='mr-4 form-label'>Limit:</label>
                            <Combobox
                              data={getOptions(LimitOptions, "limit")}
                              dataKey={"value"}
                              textField='limit'
                              placeholder={"limit"}
                              value={limit}
                              onChange={(value) => {
                                if (value.value === "all") {
                                  setLimit(null);
                                } else {
                                  setLimit(value.value);
                                }
                              }}
                            />
                          </div>
                          <div className=' my-1 col-6'>
                            <label className='mr-4 form-label'>Type:</label>
                            <Combobox
                              data={getOptions(TypeOptions, "type")}
                              dataKey={"value"}
                              textField='type'
                              placeholder={"type"}
                              value={type}
                              onChange={(value) => {
                                if (value.value === "all") {
                                  setType(null);
                                } else {
                                  setType(value.value);
                                }
                              }}
                            />
                          </div>
                          <div className='my-1 col-12'>
                            <label className='mr-4 form-label'>Category:</label>
                            <Combobox
                              data={getOptions(CategoryOptions, "category")}
                              dataKey={"value"}
                              textField='category'
                              placeholder={"category"}
                              value={category}
                              onChange={(value) => {
                                if (value.value === "all") {
                                  setCategory(null);
                                } else {
                                  setCategory(value.value);
                                }
                              }}
                            />
                          </div>
                        </div>
                        <div className='mt-4 filter-buttons'>
                          <button
                            className='btn btn-secondary mx-2'
                            onClick={handleReset}
                          >
                            Reset
                          </button>
                          <button
                            className='btn btn-primary mx-2'
                            onClick={handleFilterChange}
                          >
                            Apply
                          </button>
                        </div>
                      </div>
                    ) : (
                      ""
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
          {/* <div className='row mx-2 mb-4'></div> */}
          {codeBatchesLoading ? (
            <div className='d-flex justify-content-center align-items-center'>
              <CustomLoader />
            </div>
          ) : (
            <div className='row mx-0 px-4'>
              {codeBatches?.map((item) => (
                <div
                  key={item._id}
                  className='d-flex gap-4 justify-content-space-around mb-4 mx-3'
                >
                  <div className='batch-card-container'>
                    <BatchCard
                      batch={item}
                      deleteBatch={deleteBatch}
                      handleAddClass={addClass}
                    />
                  </div>
                </div>
              ))}
              {moreCodeBatchesLoading && hasMore && <CustomLoader />}
              <br />
              {!hasMore && (
                <div className='d-flex justify-content-center '>
                  No more batches to load
                </div>
              )}
            </div>
          )}
        </>
      </LayoutContainer>
      <DeleteModal
        open={openModal}
        toggle={toggleModal}
        onSubmit={handleRemoveBatch}
        title={"Are you sure want to delete this batch"}
        submitButtonName={"Delete Batch"}
      />
      {openClassModal && (
        <AddClassIntoBatchModal
          id={tempBatchId}
          showModal={openClassModal}
          toggleModal={toggleClassModal}
          cc={codeClassess}
        />
      )}
    </>
  );
};

export default DaysCodeBatches;

import React from "react";
import { Link } from "react-router-dom";

export const getColumns = (
  editAction,
  participantDeleteAction,
  toggleMentorAssignModal,
) => [
  {
    dataField: "Serial No",
    text: "S.NO.",
    align: "center",
    headerAlign: "center",
    sort: true,
    headerClasses: "table-header s-no",
    formatter: (cell, row, index) => <span>{++index}</span>,
    /* filter: textFilter() */
  },
  {
    dataField: "status",
    text: "Status",
    align: "center",
    headerAlign: "center",
    sort: true,
    headerClasses: "table-header s-no",
    formatter: (cell, row, index) => <span>{row.status}</span>,
    /*  filter: textFilter() */
  },
  {
    dataField: "codeUser.name",
    text: "Name",
    sort: true,
    headerClasses: "table-header name",
    style: { color: "#757575" },
    formatter: (cell, row) => (
      <span>{row.codeUser?.user && row.codeUser?.user?.firstName}</span>
    ),
    /* filter: textFilter() */
  },
  {
    dataField: "codeUser.email",
    text: "Email",
    sort: true,
    style: { color: "#757575" },
    headerClasses: "table-header status",
    formatter: (cell, row) => (
      <span>
        <Link
          target='_blank'
          to={`/profile/${row.codeUser?.user && row.codeUser?.user?.email}`}
        >
          {" "}
          {row.codeUser && row.codeUser?.user?.email}
        </Link>
      </span>
    ),
    /* filter: textFilter() */
  },
  {
    dataField: "",
    text: "Actions",
    sort: true,
    style: { color: "#757575" },
    align: "left",
    headerAlign: "left",
    headerClasses: "table-header status",
    formatter: (cell, row) => (
      <div className='text-left d-flex'>
        <i
          className='fa-sharp fa-solid fa-user-plus mr-2'
          onClick={() => toggleMentorAssignModal(row._id, row)}
        />
        <i
          type='button'
          className='mr-2 fas fa-edit mr-2'
          onClick={() => editAction(row)}
        />
        <i
          type='button'
          className='mr-2 fas fa-trash'
          onClick={() => {
            participantDeleteAction(row._id);
          }}
        />
      </div>
    ),
  },
];

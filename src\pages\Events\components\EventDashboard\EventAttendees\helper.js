import React from "react";

import { Link } from "react-router-dom";
import { Dropdown, Space } from "antd";

import { FaRegClock } from "react-icons/fa";
import { SlClose } from "react-icons/sl";
import { GoIssueClosed } from "react-icons/go";
import { LiaUserCheckSolid } from "react-icons/lia";
import { FaUserClock } from "react-icons/fa6";
import { TbCertificate } from "react-icons/tb";
import { GrResources } from "react-icons/gr";
import { FaPeopleLine } from "react-icons/fa6";
import { TfiBlackboard } from "react-icons/tfi";
import { FaRegMoneyBill1 } from "react-icons/fa6";
import { RiGift2Line } from "react-icons/ri";

import ToolTip from "../../../../../components/sharedComponents/ToolTip";

const getStatusIcon = (status) => {
  switch (status) {
    case "pending":
      return <FaRegClock className='mr-1' />;
    case "reject":
      return <SlClose className='mr-1' />;
    case "approve":
      return <GoIssueClosed className='mr-1' />;
    case "attended":
      return <LiaUserCheckSolid className='mr-1' />;
    case "guest":
      return <FaUserClock className='mr-1' />;
    default:
      return null;
  }
};

const getTicketTypeIcon = (type) => {
  switch (type) {
    case "workshop":
      return <TfiBlackboard className='mr-1' />;
    case "free":
      return <FaRegMoneyBill1 className='mr-1' />;
    case "resource":
      return <GrResources className='mr-1' />;
    case "swags":
      return <RiGift2Line className='mr-1' />;
    case "meetup":
      return <FaPeopleLine className='mr-1' />;
    case "certificate":
      return <TbCertificate className='mr-1' />;
    default:
      return null;
  }
};

export const getColumns = ({
  handleDelete,
  id,
  statusItems,
  ticketTypeItems,
}) => [
  {
    dataField: "ticketType",
    text: "Ticket Type",
    align: "center",
    headerAlign: "center",
    sort: true,
    style: { color: "#757575" },
    headerClasses: "table-header s-no",
    formatter: (cell, row, index) => {
      return (
        <>
          <div>
            <Dropdown
              menu={{ items: ticketTypeItems(row) }}
              trigger={["click"]}
            >
              <div onClick={(e) => e.preventDefault()}>
                <Space>
                  <span
                    className={` btn p-0 
                      ${row?.ticket?.type === "workshop" && "text-orange"}
                      ${row?.ticket?.type === "free" && "text-green"}
                      ${row?.ticket?.type === "swags" && "text-danger"} 
                      ${row?.ticket?.type === "resource" && "text-info"}
                      ${row?.ticket?.type === "meetup" && "text-warning"}
                      ${row?.ticket?.type === "certificate" && "text-primary"}
                    }`}
                  >
                    {getTicketTypeIcon(row?.ticket?.type)}
                    {row?.ticket?.type ?? "-"}
                  </span>
                </Space>
              </div>
            </Dropdown>
          </div>
        </>
      );
    },
    /* filter: textFilter() */
  },
  {
    dataField: "status",
    text: "Status",
    align: "center",
    headerAlign: "center",
    sort: true,
    headerClasses: "table-header s-no",
    formatter: (cell, row, index) => {
      // const statusItem = getStatusItem(cell);
      return (
        <>
          <div>
            <Dropdown menu={{ items: statusItems(row) }} trigger={["click"]}>
              <div onClick={(e) => e.preventDefault()}>
                <Space>
                  <span
                    className={` btn p-0 
                      ${row.status === "pending" && "text-warning"}
                      ${row.status === "reject" && "text-danger"}
                      ${row.status === "waiting" && "text-primary"} 
                      ${row.status === "approve" && "text-success"}
                      ${row.status === "attended" && "text-primary"}
                      ${row.status === "guest" && "text-info"}
                    }`}
                  >
                    {getStatusIcon(row?.status)}
                    {row.status}
                  </span>
                </Space>
              </div>
            </Dropdown>
          </div>
        </>
      );
    },
    /*  filter: textFilter() */
  },
  {
    dataField: "eventUsers.name",
    text: "Phone",
    sort: true,
    headerClasses: "table-header name",
    style: { color: "#757575" },
    formatter: (cell, row) => <span>{row.phone ? row.phone : "-"}</span>,
    /* filter: textFilter() */
  },
  {
    dataField: "eventUsers.fullName",
    text: "Name",
    sort: true,
    headerClasses: "table-header s-no",
    style: { color: "#757575" },
    formatter: (cell, row) => (
      <span>{row?.fullName !== " " ? row.fullName : "-"}</span>
    ),
    /* filter: textFilter() */
  },
  {
    dataField: "eventUsers.profession",
    text: "Profession",
    sort: true,
    headerClasses: "table-header s-no",
    style: { color: "#757575" },
    formatter: (cell, row) => (
      <span>
        {row?.profession !== " "
          ? row.profession
            ? row.profession
            : "-"
          : "-"}
      </span>
    ),
    /* filter: textFilter() */
  },
  {
    dataField: "eventUsers.gender",
    text: "Gender",
    sort: true,
    headerClasses: "table-header s-no",
    style: { color: "#757575" },
    formatter: (cell, row) => <span>{row?.gender ? row.gender : "N/A"}</span>,
    /* filter: textFilter() */
  },
  {
    dataField: "eventUsers.email",
    text: "Email",
    sort: true,
    style: { color: "#757575" },
    headerClasses: "table-header status",
    formatter: (cell, row) => (
      <span>
        <Link
        //   target='_blank'
        //   to={`/profile/${row.eventUsers?.email && row.codeUser?.user?.email}`}
        >
          {row?.email ? row.email : "N/A"}
        </Link>
      </span>
    ),
    /* filter: textFilter() */
  },
  {
    dataField: "eventUsers.createdAt",
    text: "Date",
    sort: true,
    sortFunc: (a, b, order, dataField, rowA, rowB) => {
      const dateA = new Date(rowA.createdAt);
      const dateB = new Date(rowB.createdAt);
      if (order === "asc") {
        return dateA - dateB;
      }
      return dateB - dateA;
    },
    headerClasses: "table-header header-date ",
    style: { color: "#757575" },
    formatter: (cell, row) => {
      const date = new Date(row.createdAt);
      return (
        <>
          <span>{row?.createdAt ? date.toLocaleDateString() : "-"}</span>
        </>
      );
      /* filter: textFilter() */
    },
  },
  {
    dataField: "actions",
    text: "Actions",
    sort: true,
    style: { color: "#757575" },
    headerClasses: "table-header status",
    formatter: (cell, row) => (
      <span className=''>
        <ToolTip title='Edit' placement='bottom'>
          <span>
            <i type='button' className='mx-2 fas fa-edit' />
          </span>
        </ToolTip>
        <ToolTip title='Delete' placement='bottom'>
          <span>
            <i
              type='button'
              className='mx-2 fas fa-trash'
              onClick={() => {
                handleDelete({ event: id, userId: row._id });
              }}
            />
          </span>
        </ToolTip>
      </span>
    ),
  },
];

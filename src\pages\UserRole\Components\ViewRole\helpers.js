import React from "react";
import { DownOutlined } from '@ant-design/icons';
import {  Dropdown, Space } from "antd";

export const getColumns = ({ items, userIdRef }) => [
  {
    dataField: "",
    text: "S.NO.",
    headerAlign: "center",
    sort: true,
    style: { color: "#757575" },
    headerClasses: "table-header s-no",
    formatter: (cell, row, index) => <span key={`sno-${row._id}-${index}`}>{index + 1}</span>,
  },
  {
    dataField: "firstName",
    text: "Users",
    headerAlign: "left",
    sort: true,
    style: { color: "#757575" },
    headerClasses: "table-header s-no pl-4",
    formatter: (cell, row, index) => (
      <div className="d-flex align-items-center" key={`user-${row._id}-${index}`}>
        <div>
          <img
            src={row?.imgUrl || "https://miro.medium.com/v2/resize:fill:20:20/1*abnkL8PKTea5iO2Cm5H-Zg.png"}
            alt=""
            width="60px"
            height="60px"
            className="im"
          />
        </div>
        <div className="d-flex">
          <div className="text-gray-800 mb-1 ml-3">{row?.firstName}</div>
        </div>
      </div>
    ),
  },
  {
    dataField: "email",
    text: "Email",
    align: "left",
    headerAlign: "left",
    sort: true,
    style: { color: "#757575" },
    headerClasses: "table-header s-no pl-4",
    formatter: (cell, row, index) => (
      <div className="text-gray-800 mb-1 ml-3" key={`email-${row._id}-${index}`}>{row?.email}</div>
    ),
  },
  {
    dataField: "joinedDate",
    text: "Joined date",
    headerAlign: "left",
    align: "left",
    sort: true,
    style: { color: "#757575" },
    headerClasses: "table-header status",
    formatter: (cell, row, index) => (
      <div className="text-gray-800 mb-1 ml-3" key={`joined-${row._id}-${index}`}>{row?.joinedDate || "20 Mar"}</div>
    ),
  },
  {
    dataField: "",
    text: "Actions",
    sort: true,
    style: { color: "#757575" },
    align: "left",
    headerAlign: "left",
    headerClasses: "table-header status",
    formatter: (cell, row, index) => (
      <Dropdown menu={{ items }} trigger={['click']} key={`actions-${row._id}-${index}`}>
        <div
          style={{ backgroundColor: "white" }}
          className="btn bg-light"
          onClick={(e) => {
            e.preventDefault();
            userIdRef.current = row._id;
          }}
        >
          <Space>
            Actions
            <DownOutlined />
          </Space>
        </div>
      </Dropdown>
    ),
  },
];

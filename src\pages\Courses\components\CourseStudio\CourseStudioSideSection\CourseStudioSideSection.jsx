import React, { useEffect } from "react";
import { useState } from "react";

import BlocksListSection from "./BlocksSection/BlocksListSection";
import CourseStudioSideNav from "./CourseStudioSideNav";
import LessonsListSection from "./LessonSection/LessonsListSection";
import SettingsSection from "./SettingsSection/SettingsSection";
import BlogSettingSection from "../../../../Blogs/components/CreateBlog/BlogSettingSection";

const CourseStudioSideSection = ({ type }) => {
  
  const [activeTab, setActiveTab] = useState("lessons");

  useEffect(() => {
    // We are using the course UI same as Blog, becasue blogs and lessons are same
    if (type === 'course') {
      setActiveTab('lessons')
    } else {
      setActiveTab('blocks')
    }
  }, [])

  const renderActiveComponent = activeTab => {
    const componentDictionary = {
      lessons: <LessonsListSection setActiveTab={setActiveTab} />,
      blocks: <BlocksListSection setActiveTab={setActiveTab} />,
      settings: type === 'blog' ? <BlogSettingSection /> : <SettingsSection setActiveTab={setActiveTab} />
    };

    return componentDictionary[activeTab];
  };

  return (
    <>
      <div className='row mx-0'>
        <div className='col-12 col-md-2 col-lg-2 col-sm-12 px-0 tab-section'>
          <CourseStudioSideNav
            setActiveTab={setActiveTab}
            activeTab={activeTab}
            type={type}
          />
        </div>
        <div className={`col-12 col-md-10 col-lg-10 col-sm-12 px-0 block-section`}>
          {renderActiveComponent(activeTab)}
        </div>
      </div>
    </>
  );
};

export default CourseStudioSideSection;

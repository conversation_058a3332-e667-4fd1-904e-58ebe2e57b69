* {
  margin: 0;
  padding: 0;
}

.course-page-wrapper .course-header {
  padding-right: 20px;
  background-color: antiquewhite;
  height: 50px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.course-page-wrapper .course-header .toggle-button {
  cursor: pointer;
  width: 25px;
  height: 25px;
  margin-left: 20px;
}
.course-page-wrapper .course-list {
  padding: 40px;
}

.course-side-nav {
  width: 4% !important;
  left: 0;
  transition: all 0.5s ease-in-out;
  display: block;
  padding: 0;
  margin-top: 106px;
  height: 100%;
  background: #ffffff;
  box-shadow: 0px 5px 6px 1px rgba(59, 73, 94, 0.2);
  position: fixed;
  top: 0;
  left: 0;
  overflow-y: auto;
  overflow-x: hidden;
}
.course-side-nav .side-nav-header {
  height: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-bottom: 1px solid rgba(168, 166, 166, 0.5098039216);
  padding: 15px;
  text-decoration: none;
}
.course-side-nav .side-nav-header i {
  font-size: 22px;
}
.course-side-nav .side-nav-header p {
  display: none;
}
.course-side-nav span {
  display: none;
}
.course-side-nav .side-nav-menu ul {
  padding: 10px;
}
.course-side-nav .side-nav-menu ul li {
  display: flex;
  justify-content: center;
  align-items: center;
  list-style: none;
  margin: 12px 0 12px 0;
  padding: 10px;
  color: #000;
}
.course-side-nav .side-nav-menu ul li:hover {
  background-color: #c3c3c3;
  border-radius: 5px;
  color: #000;
  text-decoration: none;
}
.course-side-nav .side-nav-menu ul .active-tab {
  color: #673de6;
  background-color: #c3c3c3;
}
.course-side-nav .side-nav-menu ul .active-tab span {
  color: #673de6;
  font-weight: 700;
}
.course-side-nav .side-nav-menu ul .active-tab:hover {
  color: #673de6;
}
.course-side-nav ul::-webkit-scrollbar-track {
  border-radius: 10px;
  background-color: #f1efef;
}
.course-side-nav ul::-webkit-scrollbar {
  width: 3px;
  height: 5px;
  background-color: #fff;
}
.course-side-nav ul::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background-color: #c3c3c3;
}

.course-side-nav.open {
  transition: all 0.5s ease-in-out;
  width: 20% !important;
}
.course-side-nav.open .side-nav-header {
  width: 100%;
  display: flex;
  justify-content: left;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid rgba(168, 166, 166, 0.5098039216);
  font-size: 18px;
  text-decoration: none;
}
.course-side-nav.open .side-nav-header span {
  width: 100% !important;
  white-space: nowrap;
  display: block;
}
.course-side-nav.open .side-nav-menu ul {
  width: 100%;
  padding: 10px;
  list-style-type: none;
  overflow-x: hidden;
  overflow-y: scroll;
  text-decoration: none;
}
.course-side-nav.open .side-nav-menu ul li {
  width: 100%;
  color: #000;
  align-items: left;
  justify-content: left;
  padding: 7px;
  margin: 12px 0 12px 0;
  text-decoration: none;
  display: flex;
  text-align: left;
  align-items: center;
}
.course-side-nav.open .side-nav-menu ul li:hover {
  background-color: #c3c3c3;
  color: #000;
  border-radius: 5px;
}
.course-side-nav.open .side-nav-menu ul li span {
  display: block;
  text-align: left;
  color: rgba(22, 22, 23, 0.946);
  font-size: 16px;
  text-decoration: none;
}
.course-side-nav.open .side-nav-menu ul .active-tab {
  color: #673de6;
  background-color: #ebebeb;
}
.course-side-nav.open .side-nav-menu ul .active-tab span {
  color: #673de6;
  font-weight: 700;
}
.course-side-nav.open .side-nav-menu ul .active-tab:hover {
  color: #673de6;
}

.course-page-wrapper {
  width: 96% !important;
  position: relative;
  left: 4%;
  right: 0;
  transition: all 0.5s ease-in-out;
}

.course-side-nav.open + .course-page-wrapper {
  position: relative;
  left: 20%;
  right: 0;
  width: 80% !important;
  transition: all 0.5s ease-in-out;
}

.course-list .course-list-header {
  display: flex;
  justify-content: space-between;
  padding: 20px;
}
.course-list .course-list-header button {
  color: #fff;
  background-color: #673de6;
  padding: 5px 15px;
  border: none;
  border-radius: 5px;
}

.course-preview {
  display: flex;
  height: 95vh;
}
.course-preview .course-content {
  border: 1px solid #e9e9e9;
  padding: 20px 0px;
  background-color: #f8f9fa;
  height: 95vh;
}
.course-preview .course-content .crossBtn {
  border: 1px solid #e9e9e9;
  border-radius: 5px;
  background-color: #fff;
  margin: 20px;
}
.course-preview .course-content .crossBtn .cross {
  width: 2em !important;
}
.course-preview .course-content .content-header {
  border-bottom: 1px solid #e9e9e9;
  height: 24vh;
}
.course-preview .course-content .content-header p {
  color: #808080;
}
.course-preview .course-content .content-list {
  height: 71vh;
  overflow-y: scroll;
}
.course-preview .course-content .content-list::-webkit-scrollbar-track {
  border-radius: 10px;
  background-color: #f1efef;
}
.course-preview .course-content .content-list::-webkit-scrollbar {
  width: 6px;
  height: 3px;
  background-color: #fff;
}
.course-preview .course-content .content-list::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background-color: #c3c3c3;
}
.course-preview .course-content .content-list .guidlines .guidlines-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  border-bottom: 1px solid #e9e9e9;
}
.course-preview .course-content .content-list .guidlines .guidlines-header h4 {
  font-weight: 700;
  margin: 0;
}
.course-preview .course-content .content-list .guidlines .guidlines-header p {
  font-size: 25px;
  margin: 0 20px 0 10px;
}
.course-preview .course-content .content-list .guidlines .guidlines-header Button {
  background-color: transparent;
  border: none;
  outline: none;
}
.course-preview .course-content .content-list .guidlines .guidlines-header .up-arrow {
  cursor: pointer;
  font-size: 24px;
  color: #808080;
}
.course-preview .course-content .content-list .guidlines .guidlines-header .down-arrow {
  cursor: pointer;
  font-size: 24px;
  color: #808080;
}
.course-preview .course-content .content-list .guidlines .guidlines-list ul {
  list-style: none;
}
.course-preview .course-content .content-list .guidlines .guidlines-list ul div .tickMark {
  width: 30px;
  height: 36px;
  color: #02da48;
  padding: 0 5px;
}
.course-preview .course-content .content-list .guidlines .guidlines-list ul div .lock {
  margin-top: 9px;
  width: 30px;
  height: 20px;
}
.course-preview .course-content .content-list .guidlines .guidlines-list ul div li {
  gap: 10px;
}
.course-preview .course-content .content-list .guidlines .guidlines-list ul div p {
  margin-left: 40px;
  display: flex;
  align-items: center;
  gap: 6px;
}
.course-preview .course-content .content-list .guidlines .guidlines-list ul div span {
  color: #808080;
}
.course-preview .course-content .content-list .blocks .blocks-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 15px 0;
  padding: 10px;
  border-bottom: 1px solid #e9e9e9;
}
.course-preview .course-content .content-list .blocks .blocks-header h4 {
  font-weight: 700;
  margin: 0;
}
.course-preview .course-content .content-list .blocks .blocks-header p {
  font-size: 25px;
  margin: 0 20px 0 10px;
}
.course-preview .course-content .content-list .blocks .blocks-header Button {
  background-color: transparent;
  border: none;
  outline: none;
}
.course-preview .course-content .content-list .blocks .blocks-header Button:hover {
  background-color: transparent;
  border: none;
  outline: none;
}
.course-preview .course-content .content-list .blocks .blocks-header .up-arrow {
  font-size: 24px;
  color: #808080;
}
.course-preview .course-content .content-list .blocks .blocks-header .down-arrow {
  font-size: 24px;
  color: #808080;
}
.course-preview .course-content .content-list .blocks .blocks-list {
  margin-left: -13px;
}
.course-preview .course-content .content-list .blocks .blocks-list ul {
  list-style: none;
}
.course-preview .course-content .content-list .blocks .blocks-list ul div {
  display: flex;
  flex-direction: column;
  gap: 10px;
}
.course-preview .course-content .content-list .blocks .blocks-list ul div span {
  display: flex;
  align-items: center;
  gap: 20px;
}
.course-preview .course-content .content-list .blocks .blocks-list ul div span .tickMark {
  width: 30px;
  height: 30px;
  color: #02da48;
  padding: 0 5px;
}
.course-preview .course-content .content-list .blocks .blocks-list ul div span li {
  font-size: 20px;
}
.course-preview .course-content .content-list .blocks .blocks-list ul div span li span {
  margin-left: 10px;
}
.course-preview .course-content .content-list .blocks .blocks-list ul div p {
  margin-left: 40px;
  display: flex;
  align-items: center;
  gap: 10px;
}
.course-preview .preview-content {
  height: 95vh;
  display: flex;
  flex-direction: column;
  padding: 1px;
  transition: text-align 0.5s ease-in-out;
}
.course-preview .preview-content .horizontal-bar .progress-container {
  height: 5px;
  width: 100%;
  background-color: transparent;
  position: sticky;
  left: 0;
  right: 0;
}
.course-preview .preview-content .horizontal-bar .progress-container .progress-fill {
  height: 100%;
  width: 50px;
  background-color: #673de6;
}
.course-preview .preview-content .horizontal-bar .progressMainStyle {
  height: 15px;
  background-color: #673de6;
  width: 15%;
}
.course-preview .preview-content .main-preview {
  overflow-y: scroll;
  height: 80vh;
}
.course-preview .preview-content .main-preview::-webkit-scrollbar-track {
  border-radius: 10px;
  background-color: #f1efef;
}
.course-preview .preview-content .main-preview::-webkit-scrollbar {
  width: 7px;
  height: 5px;
  background-color: #fff;
}
.course-preview .preview-content .main-preview::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background-color: #c3c3c3;
}
.course-preview .preview-content .main-preview .preview-detail p {
  text-align: justify;
  color: #585858;
}
.course-preview .preview-content .main-preview .preview-detail ul li {
  margin-left: 40px;
  color: #585858;
}
.course-preview .preview-content .preview-footer {
  background-color: #fefefe;
  padding: 0 100px;
  height: 15vh;
  border-top: 1px solid #f1d7ff;
  border-bottom: 1px solid #f1d7ff;
}
.course-preview .preview-content .preview-footer .footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.course-preview .preview-content .preview-footer .footer .footer-progress {
  padding: 30px 0;
  display: flex;
  flex-direction: column;
}
.course-preview .preview-content .preview-footer .footer .footer-progress .footer-progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 40px;
}
.course-preview .preview-content .preview-footer .footer .footer-progress .footer-progress-header p {
  font-size: 18px;
}
.course-preview .preview-content .preview-footer .footer .page-switch {
  display: flex;
  justify-content: flex-end;
  gap: 20px;
}
.course-preview .preview-content .preview-footer .footer .page-switch button {
  font-size: 18px;
  padding: 5px 15px;
  background-color: #fff;
  color: #000;
  border: none;
  border-radius: 10px;
  border: 1px solid #f1d7ff;
  display: flex;
  gap: 20px;
  justify-content: center;
  align-items: center;
}
.course-preview .preview-content .preview-footer .footer .page-switch button:hover {
  background-color: transparent;
  color: #673de6;
}

.new-text-color {
  color: gray;
}

.user-progress p {
  margin: 0;
  padding: 0;
}
.user-progress .progress-detail {
  background-color: #f3efff;
  border-radius: 10px;
}
.user-progress .progress-detail .progress-badge {
  display: flex;
  align-items: center;
  gap: 30px;
  background-color: #fff;
  padding: 10px;
  border-radius: 10px;
}
.user-progress .progress-detail .progress-badge .badge-icon {
  width: 40px;
  height: 40px;
  border-radius: 5px;
}
.user-progress .progress-detail .progress-badge .badge-detail {
  display: flex;
  flex-direction: column;
  gap: 10px;
}
.user-progress .progress-detail .progress-badge .badge-detail .bg-bronze {
  background-color: #DE6B02;
}
.user-progress .progress-detail .progress-badge .badge-detail .bg-silver {
  background-color: #A8A8A8;
}
.user-progress .progress-detail .progress-badge .badge-detail .bg-gold {
  background-color: #C98E27;
}
.user-progress .progress-detail .progress-badge .badge-detail .bg-platinum {
  background-color: #B2B2B2;
}
.user-progress .total-time {
  width: 100%;
  height: 100%;
  display: flex;
  gap: 30px;
  flex-direction: column;
  justify-content: center;
  padding: 10px;
  background-color: #fff;
  border-radius: 10px;
  margin: 0px;
}
.user-progress .total-time .clock {
  background-color: #ffe6b9;
  padding: 6px;
  border-radius: 5px;
  width: 40px;
  height: 40px;
  color: orange;
}
.user-progress .total-score {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 30px;
  padding: 10px;
  background-color: #fff;
  border-radius: 10px;
  margin: 0px;
}
.user-progress .total-score .checkbox {
  width: 40px;
  height: 40px;
  color: #7f7fff;
  background-color: #cfe2ff;
  padding: 6px;
  border-radius: 5px;
}
.user-progress .learning {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  background-color: #f3efff;
  border-radius: 10px;
}
.user-progress .learning .learning-container {
  width: 100%;
}
.user-progress .learning .learning-container h5 {
  margin-bottom: 25px;
}
.user-progress .learning .learning-container h5 .learning-progress-icon {
  color: #c284ee;
}
.user-progress .learning .learn-progress {
  width: 100%;
  height: 10px;
  position: relative;
  background-color: #c9b8ff;
  border-radius: 10px;
}
.user-progress .learning .learn-progress .learn-progress-bar {
  height: 15px;
  position: absolute;
  top: -3px;
  left: 0;
  background-color: #673de6;
  border-radius: 10px;
}

.user-course-list {
  margin-top: 30px;
}
.user-course-list .line {
  margin-top: 20px;
  width: 100%;
  height: 2px;
  background-color: rgb(180, 180, 180);
}
.user-course-list .line .line-double {
  width: 200px;
  height: 3px;
  background-color: #9129F1;
}
.user-course-list .course-list {
  margin-top: 50px;
  display: flex;
  flex-wrap: wrap;
}
.user-course-list .course-list .list {
  margin: 0 40px 40px 0;
  width: 100%;
  border: 1px solid #acacac;
  border-radius: 20px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.user-course-list .course-list .list:hover {
  box-shadow: 0 0 5px #acacac;
}
.user-course-list .course-list .list .icon {
  width: 100%;
  padding: 20px;
  background: linear-gradient(to right, #F5DDFD 0%, #FDDEE9 100%);
  border-top-left-radius: 20px;
  border-top-right-radius: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.user-course-list .course-list .list .icon .goal-icon {
  width: 60px;
  height: 60px;
}
.user-course-list .course-list .list .detail {
  height: 100%;
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.user-course-list .course-list .list .detail h5 {
  -webkit-line-clamp: 2;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  color: #585858;
}
.user-course-list .course-list .list .detail p {
  color: #777777;
}
.user-course-list .course-list .list .button {
  padding: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 10px 20px;
  border: 1px solid #d4c6ff;
  border-radius: 7px;
  cursor: pointer;
}
.user-course-list .course-list .list .button button {
  background-color: transparent;
  border: none;
}
.user-course-list .course-list .list .button button h6 {
  margin: 0;
  width: -moz-max-content;
  width: max-content;
}
.user-course-list .course-list .list .button button h6 .tickMark {
  width: 35px;
  height: 35px;
  color: #02da48;
  padding: 0 6px;
}

.course-detail-info {
  display: flex;
  flex-direction: column;
  gap: 20px;
}
.course-detail-info .course-details-header {
  gap: 40px;
}
.course-detail-info .course-details-header .header-icon {
  background-color: #f5dcff;
  padding: 50px 50px;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
}
.course-detail-info .course-details-header .header-icon .icon {
  width: 40px;
  height: 40px;
}
.course-detail-info .course-details-header .header-info {
  display: flex;
  flex-direction: column;
  gap: 10px;
}
.course-detail-info .course-details-header .header-info p {
  color: gray;
}
.course-detail-info .course-details-header .header-info h3 {
  margin-top: -20px;
}
.course-detail-info .course-details-header .header-info div {
  display: flex;
  gap: 20px;
}
.course-detail-info .course-details-header .header-info div .clock {
  display: flex;
  gap: 10px;
  justify-content: center;
  align-items: center;
}
.course-detail-info .course-details-header .header-info div .clock p {
  margin: 0;
}
.course-detail-info .course-details-header .header-info div .lesson {
  display: flex;
  gap: 10px;
  justify-content: center;
  align-items: center;
}
.course-detail-info .course-details-header .header-info div .lesson p {
  margin: 0;
}
.course-detail-info .course-desciption {
  display: flex;
  flex-direction: column;
  gap: 20px;
}
.course-detail-info .course-desciption h3 {
  font-weight: bold;
}
.course-detail-info .course-desciption p {
  font-size: 16px;
}
.course-detail-info .course-desciption p span {
  font-weight: bold;
}
.course-detail-info .course-desciption .read-more {
  color: #673de6;
  font-weight: bold;
}
.course-detail-info .teacher-info {
  gap: 20px;
}
.course-detail-info .teacher-info img {
  max-width: 150px;
  border-radius: 50%;
}
.course-detail-info .teacher-info p {
  margin: 0;
  color: #808080;
}
.course-detail-info .teacher-info h3 {
  font-weight: bold;
}

.course-details-curriculum {
  margin-top: 40px;
}
.course-details-curriculum .course-details-progress p {
  color: gray;
}
.course-details-curriculum .course-details-progress button {
  border: none;
  background-color: #01f96f;
  width: -moz-max-content;
  width: max-content;
  border-radius: 8px;
}
.course-details-curriculum .curriculum {
  margin: 20px;
  border: 1px solid #c3c3c3;
  border-radius: 20px;
}
.course-details-curriculum .curriculum h4 {
  background-color: #f8f9fa;
  padding: 20px 40px;
  border-top-left-radius: 20px;
  border-top-right-radius: 20px;
  font-weight: bold;
}
.course-details-curriculum .curriculum ol {
  max-height: 550px;
  overflow-y: scroll;
}
.course-details-curriculum .curriculum ol::-webkit-scrollbar-track {
  border-radius: 10px;
  background-color: #f1efef;
}
.course-details-curriculum .curriculum ol .tickMark {
  width: 30px;
  height: 36px;
  color: #02da48;
  padding: 0 5px;
}
.course-details-curriculum .curriculum ol::-webkit-scrollbar {
  width: 6px;
  height: 5px;
  background-color: #fff;
}
.course-details-curriculum .curriculum ol::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background-color: #c3c3c3;
}
.course-details-curriculum .curriculum ol div {
  display: flex;
  flex-direction: column;
  gap: 10px;
}
.course-details-curriculum .curriculum ol div span {
  display: flex;
  align-items: center;
  gap: 40px;
}
.course-details-curriculum .curriculum ol div span .clock {
  font-size: 24px;
  color: #585858;
}
.course-details-curriculum .curriculum ol div span li {
  font-size: 16px;
  font-weight: bold;
}
.course-details-curriculum .curriculum ol div p {
  margin-left: 63px;
  display: flex;
  align-items: center;
  gap: 10px;
  color: #585858;
  font-size: 16px;
}

.custom-progress-bar {
  display: block;
  border: 1px solid #02DB4A;
  height: 51% !important;
  width: 10%;
  color: #000 !important;
  background-color: #4caf50 !important; /* Set the color of your progress bar */
}

.progress-bar {
  background-color: #02DB4A !important;
}

.btn-courses {
  background-color: #652fec;
  padding: 10px 30px;
  border-radius: 5px;
  outline: none;
  border: none;
  color: white;
  font-weight: 500;
  float: right;
  width: 25%;
  font-size: 18px;
  transition: all 0.3s ease;
}

.complete-course-window .tickMark {
  width: 30px;
  height: 36px;
  color: #02da48;
  padding: 0 5px;
}
.complete-course-window .btn-courses {
  margin-right: 150px;
}

.user_list .course_asign_button {
  display: inline-block;
  padding: 7px 20px;
  font-size: 20px;
  cursor: pointer;
  text-align: center;
  text-decoration: none;
  outline: none;
  color: #fff;
  background-color: #04AA6D;
  border: none;
  border-radius: 10px;
}
.user_list .course_asign_button:hover {
  background-color: #3e8e41;
}
.user_list .course_asign_button:active {
  background-color: #3e8e41;
  box-shadow: 0 5px #666;
  transform: translateY(4px);
}

.course_denie_button {
  display: inline-block;
  padding: 7px 20px;
  font-size: 20px;
  cursor: pointer;
  text-align: center;
  text-decoration: none;
  outline: none;
  color: #fff;
  border: none;
  border-radius: 10px;
}
.course_denie_button:hover {
  background-color: #c3c3c3;
  border-radius: 5px;
  color: #000;
  text-decoration: none;
}

@media (max-width: 767px) {
  .nav-cross {
    display: none;
  }
}
@media (max-width: 329px) {
  .course-side-nav ul li {
    position: relative;
    left: 28px;
  }
  .nav-cross {
    display: none;
  }
}
@media (max-width: 600px) {
  .user-progress {
    margin-top: 20px;
    width: 100%;
    background-color: #f3efff;
    border-radius: 10px;
  }
  .list {
    width: 100% !important;
    margin: 0 !important;
    margin-bottom: 20px !important;
  }
  .list .icon {
    padding: 10px !important;
  }
  .list .detail {
    padding: 10px !important;
  }
  .list .button {
    margin: 5px 8px !important;
    padding: 5px !important;
  }
  .course-detail-info {
    display: block !important;
  }
  .course-detail-info .course-details-header {
    display: block !important;
  }
  .preview-footer {
    padding: 0 33px !important;
    height: 35vh !important;
  }
}/*# sourceMappingURL=course.css.map */
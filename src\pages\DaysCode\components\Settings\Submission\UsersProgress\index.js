import { connect } from "react-redux";
import {
  getAllUsersProgressByMentor,
} from "../../../../actions";

import UsersProgress from "./UsersProgress";

const mapStateToProps = ({ dayscode, auth }) => ({
  currentUser: auth.currentUser ? auth.currentUser : {},
  daysUsers: dayscode.daysUsers,
  getUserSubmissionsLoading: dayscode.getUserSubmissionsLoading,
  getAllUsersProgressByMentorLoading: dayscode.getAllUsersProgressByMentorLoading,
});

const mapDispatchToProps = {
  getAllUsersProgressByMentor,
};

export default connect(mapStateToProps, mapDispatchToProps)(UsersProgress);

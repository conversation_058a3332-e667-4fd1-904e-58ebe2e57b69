import React, { useEffect, useRef, useState } from 'react'
import { useNavigate } from 'react-router'
import { useDispatch, useSelector } from 'react-redux'
import BootstrapTable from 'react-bootstrap-table-next'

import { getColumns } from './helpers'
import NoDataBlock from '../../../../../components/sharedComponents/NoDataBlock'
import { deleteReview, getReviewsList } from '../../../actions/operations'
import CustomLoader from '../../../../../components/sharedComponents/CustomLoader'
import { setEditReview } from '../../../actions/actionCreators'

const UsersReviewList = () => {
  const [search, setSearch] = useState('')
  const dispatch = useDispatch()
  const { reviewsList, reviewsLoading } = useSelector((state) => state.auth)
  const selectedReview = useRef()
  const navigate = useNavigate()
  useEffect(() => {
    dispatch(getReviewsList())
  }, [])
  useEffect(() => {
    console.log(selectedReview.current, 're')
  }, [selectedReview.current])

  const handleEdit = () => {
    navigate(`/users/review/${selectedReview.current.user}`)
    dispatch(setEditReview(selectedReview.current))
  }
  const handledelete = () => {
    console.log(selectedReview.current)
    dispatch(deleteReview(selectedReview.current._id))
  }
  const items = [
    {
      key: '1',
      label: (
        <div onClick={(e) => handleEdit(e)}> Edit </div>
      ),
    },
    {
      key: '2',
      label: (
        <div onClick={(e) => handledelete()}>Delete</div>
      ),
    },
  ];
  let columns = getColumns(selectedReview, items)
  return (
    <>
      <div className='event-attendees '>
        <div className='d-flex justify-content-between align-items-center px-3 py-3 border-bottom'>
          <div className='event-attendees-title'>
            Portal Users <span>Reviews</span>
          </div>
        </div>
        <div className='row d-flex justify-content-center'>
          <div className='table table-responsive mt-3 col-10'>
            <div
              className='w-100 border-top border-left border-right rounded d-flex justify-content-between align-items-center'
              style={{ backgroundColor: "#f0eeee" }}
            >
              <div className='table-options-search ml-4'>
                <input
                  type="text"
                  className="search"
                  value={search}
                  placeholder="Username"
                  onChange={(e) => setSearch(e.target.value)}
                />
              </div>
            </div>

            {reviewsLoading ? (
              <CustomLoader />
            ) : reviewsList.length === 0 ? (
              <NoDataBlock />
            ) : (
              <>
                <BootstrapTable
                  keyField='_id'
                  data={reviewsList}
                  columns={columns}
                  bordered={true}
                  hover
                  headerClasses='header-class'
                />
              </>
            )}
          </div>
        </div>
      </div>
    </>
  )
}

export default UsersReviewList
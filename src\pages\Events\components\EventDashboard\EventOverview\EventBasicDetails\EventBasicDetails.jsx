import React, { useEffect } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { useParams } from 'react-router'

import { getEventDetails } from '../../../../actions'
import CreateEvent from '../../../CreateEvent/CreateEventForm'
import CustomLoader from '../../../../../../components/sharedComponents/CustomLoader'

const EventBasicDetails = () => {

  const dispatch = useDispatch();
  const {id} = useParams()
  
  const { eventDetailsLoading ,eventDetails} = useSelector((state) => state.event) || {}

  useEffect(() => {
    dispatch(getEventDetails(id))
  }, [dispatch , id]);

  useEffect(() => {
    window.scroll(0, 0)
  }, [])

  return (
    <>{eventDetailsLoading ? <div className='d-flex justify-content-center'> <CustomLoader/></div> : <>
      <CreateEvent eventDetails={eventDetails} />
      </>}
    </>
  )
}

export default EventBasicDetails
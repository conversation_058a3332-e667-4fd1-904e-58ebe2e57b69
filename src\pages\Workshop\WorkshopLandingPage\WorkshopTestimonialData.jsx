import React from 'react'

import CustomStarRating from '../../../components/sharedComponents/CustomStarRating'

const WorkshopTestimonialData = ({img,name,bio,title,rating,content}) => {
  return (
    <>
      <div className='speaker-section mx-3'>
        <div className='testimonial-border pt-3 pb-4 px-3'>
          <div className='d-flex justify-content-between'>
            <div className='d-flex py-3 justify-content-start'>
              <img
                className="rounded-circle border mr-2"
                height="52"
                width="52"
                loading="lazy"
                src={img}
                alt="avatar"
              />
              <div className="">
              </div>
              <div>
                <h6 className="mb-0 item-speaker-name">{name}</h6>
                <p className="mb-0 item-speaker-bio">{bio}</p>
              </div>
            </div>
            <div className="rating">
              <CustomStarRating initialValue={rating} readonly={true}/>
            </div>
          </div>
          <div className='text-center workshop-title'>{title}</div>
          <div className='text-center item-content '>{content}</div>
        </div>
      </div>
    </>
  )
}

export default WorkshopTestimonialData;

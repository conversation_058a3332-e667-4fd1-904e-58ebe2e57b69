import { useEffect, useState } from "react";

import { useDispatch, useSelector } from "react-redux";
import { useParams } from "react-router-dom";
import { Button } from "reactstrap";

import { getLessonDetails } from "../../../../actions";

import { BlocksFramPreviewDictionary } from "../../LessonContentCraftSection/ContentBlocksFrams/BlocksFramPreviewDictionry";
import CustomLoader from "../../../../../../components/sharedComponents/CustomLoader";
import moment from "moment";

const LessonPreview = ({ component, lessonId, handleLessionComplete, nextIncompleteLesson }) => {
  const [formatDate, setFormatDate] = useState()
  const { id } = useParams();
  const dispatch = useDispatch();

  const { lessonDetailsLoading, lessonDetails } =
    useSelector((state) => state.course) || {};

  useEffect(() => {
    if (!component) {
      dispatch(getLessonDetails(id || lessonId));
    }
  }, []);

  useEffect(() => {
    const date = moment(lessonDetails?.updatedAt);
    setFormatDate(date?.format("MMMM Do YYYY"))
  }, [lessonDetails])

  const completedByuser = lessonDetails && lessonDetails?.completedBy;
  const currentUser = JSON.parse(
    sessionStorage.getItem("currentUser") || localStorage.getItem("currentUser")
  );
  let courseUserId = currentUser?.courseUser;
  const isLessonCompleted = completedByuser && completedByuser?.includes(courseUserId);

  return (
    <>
      <div className="row">
        <div className="container-fluid col-9">
          <div className="row mt-5 d-flex justify-content-center">
            <div className="col-12 col-md-12 ">
              <h2 className="text-center ">{lessonDetails?.name}</h2>
            </div>

          </div>
          <div className="mt-3 d-flex justify-content-center"><img src="https://miro.medium.com/v2/resize:fit:679/0*ccCr8F6SL9sdxsfp.png" height="300px" width={"300px"}></img></div>

          <div className=" lesson-craft">
            <div className=" p-2">
              {lessonDetailsLoading ? (
                <CustomLoader />
              ) : (
                <>
                  {lessonDetails?.contents && (
                    <>
                      {lessonDetails.contents.map((item) => (
                        <div key={item._id} className="mt-5">
                          {BlocksFramPreviewDictionary(item)}
                        </div>
                      ))}
                      <div className="d-flex align-items-center ">
                        <div className="symbol symbol-circle symbol-50px overflow-hidden ">
                          <div className="symbol-label">
                            <img src={lessonDetails.author?.imgUrl || "https://miro.medium.com/v2/resize:fill:20:20/1*abnkL8PKTea5iO2Cm5H-Zg.png"} alt="Author" width="50px" height="50px" />
                          </div>
                        </div>
                        <div className="d-flex ml-3">
                          <div className="text-gray-800  mb-1 ml-1">{lessonDetails.author ? ((lessonDetails.author?.firstName) + " " + (lessonDetails.author?.lastName)) : "Parag Jain"}</div>
                        </div>
                      </div>
                      <div className="mt-3">Published On ~ {formatDate}</div>
                      {!isLessonCompleted ? (
                        <div
                          className="d-flex justify-content-center mx-3"
                          onClick={() => handleLessionComplete(lessonDetails._id)}
                        >
                          <Button color="success my-3">Mark as Completed</Button>
                        </div>
                      ) : (
                        <div
                          className="d-flex justify-content-center mx-3"
                          onClick={() => nextIncompleteLesson(lessonDetails._id)}
                        >
                          <Button color="primary my-3 ">Lesson Completed</Button>
                        </div>
                      )}
                    </>
                  )}
                </>
              )}
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default LessonPreview;

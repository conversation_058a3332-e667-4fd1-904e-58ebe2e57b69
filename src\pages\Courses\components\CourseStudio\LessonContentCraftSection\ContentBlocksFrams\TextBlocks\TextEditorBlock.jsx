import React, {  useState } from 'react'
import { useDispatch } from 'react-redux'

import TextEditor from '../../../../../../../components/sharedComponents/TextEditor'
import { editContent } from '../../../../../actions'
import ContentBlockLayout from '../ContentBlockLayout'

const TextEditorBlock = ({ item }) => {
  
  const dispatch = useDispatch()

  const [descriptionText, getDescriptionText] = useState('<p></p>')

  const handleDescriptionText = (text) => {
    getDescriptionText(text)
  }

  const handleSubmit = () => {
    const data = {
      id: item._id,
      payload: descriptionText
    }
    dispatch(editContent(data))
  }

  const headerHtml = () => {
    return (
      <>
        <div className='embeds-block-header d-flex justify-content-between'>
          <div>
            <div className='d-flex embeds-block-title'>
              <i className="fa-regular fa-pen-to-square" />
              <p className='mb-0'>Text Editor</p>
            </div>
          </div>
        </div>
      </>
    )
  }

  return (
    <>
      <ContentBlockLayout item={item} blockHeader={headerHtml()} previewVisibility={true}>
        <div className="row mx-0 border m-2">
          <div className="col-12">
            {
              item.payload ?
                <TextEditor handleTextEditor={handleDescriptionText} text={item.payload} />
                :
                <TextEditor handleTextEditor={handleDescriptionText} text={descriptionText} />
            }
            <div className='d-flex justify-content-end pb-2'>
              <button onClick={() => handleSubmit()} type="submit" className="btn btn-primary">Save</button>
            </div>
          </div>
        </div>
      </ContentBlockLayout>
    </>
  )
}
export default TextEditorBlock
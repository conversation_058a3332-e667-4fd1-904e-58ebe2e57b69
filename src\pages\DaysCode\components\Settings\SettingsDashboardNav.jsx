import React, { useState } from "react";
import { connect } from "react-redux";
import { Link, useLocation } from "react-router-dom";
import ExpandMoreRoundedIcon from "@mui/icons-material/ExpandMoreRounded";
import ExpandLessRoundedIcon from "@mui/icons-material/ExpandLessRounded";
import { GiHamburgerMenu } from "react-icons/gi";
import { MdOutlineKeyboardArrowLeft } from "react-icons/md";
import { LuLayoutDashboard } from "react-icons/lu";
import { GoStack } from "react-icons/go";
import { FaUserTie } from "react-icons/fa";
import { LuFileQuestion } from "react-icons/lu";
import { MdOutlinePlayLesson } from "react-icons/md";
import { MdOutlineQuiz } from "react-icons/md";
import { TbBookUpload } from "react-icons/tb";
import { HiOutlineUsers } from "react-icons/hi2";
import { VscFeedback } from "react-icons/vsc";
import { MdOutlineNotificationAdd } from "react-icons/md";
import { MdOutlineClass } from "react-icons/md";

import { setSettingActiveTab } from "../../actions";

const SettingsDashboardNav = ({
  setSettingActiveTab,
  activeTab,
  isOpen,
  setIsOpen,
}) => {
  const authAdminGuard = () => {
    var localUser = JSON.parse(localStorage.getItem("currentUser"));
    return localUser &&
      localUser.userName === process.env.REACT_APP_ADMIN_USERNAME &&
      localUser.email === process.env.REACT_APP_ADMIN_EMAIL
      ? true
      : false;
  };

  const [batchOption, setBatchOption] = useState(false);
  const [mentorOption, setMentorOption] = useState(false);
  const [problemOption, setProblemOption] = useState(false);
  const [lessonOption, setLessonOption] = useState(false);
  const [quizOption, setQuizOption] = useState(false);
  const [userOption, setUserOption] = useState(false);

  const location = useLocation();
  const state = location.state || "genral";

  return (
    <>
      <div className='row m-0 w-100'>
        <div className='col-12 px-0' style={{ height: "100vh" }}>
          <div
            className={`d-flex ${
              !isOpen ? "justify-content-end" : "justify-content-center"
            }`}
          >
            {isOpen ? (
              <GiHamburgerMenu
                className='h4 mt-3 '
                onClick={() => {
                  setIsOpen(!isOpen);
                }}
              />
            ) : (
              <MdOutlineKeyboardArrowLeft
                className='h3 mx-4 mt-3  d-flex justify-content-end'
                onClick={() => {
                  setIsOpen(!isOpen);
                }}
              />
            )}
          </div>
          <div className='code-setting-side-nav'>
            <ul>
              <li
                className={`side-nav-option ${
                  state === "genral" ? "active " : ""
                }`}
              >
                <Link to={"/admin/days_code/general"} state={"genral"}>
                  {isOpen ? (
                    <span
                      className={`close-drawer-icon ${
                        state === "genral" ? "active" : ""
                      }`}
                    >
                      <LuLayoutDashboard />
                    </span>
                  ) : (
                    <span className={state === "genral" ? "active " : ""}>
                      <LuLayoutDashboard className='mr-1 open-drawer-item' />
                      DashBoard
                    </span>
                  )}
                </Link>
              </li>
              <li>
                <>
                  <div
                    className={`${batchOption ? "no-hover " : ""} ${
                      state === "batches" || state === "createbatch"
                        ? "active-drop"
                        : ""
                    } side-nav-option`}
                    onClick={() => {
                      if (isOpen) {
                        setIsOpen(false);
                        setBatchOption(true);
                      }
                    }}
                  >
                    {isOpen ? (
                      <span
                        className={`close-drawer-icon ${
                          state === "batches" || state === "createbatch"
                            ? "active-drop"
                            : ""
                        } `}
                      >
                        <GoStack />
                      </span>
                    ) : (
                      <span>
                        <span
                          onClick={() => {
                            setBatchOption(!batchOption);
                          }}
                          className={`d-flex justify-content-between align-items-center open-drawer-item ${
                            state === "batches" || state === "createbatch"
                              ? "active-drop"
                              : ""
                          }`}
                        >
                          <span
                            className={
                              state === "batches" || state === "createbatch"
                                ? "active-drop"
                                : ""
                            }
                          >
                            <GoStack className='mr-1' />
                            Batches
                          </span>
                          <span
                            className={
                              state === "batches" || state === "createbatch"
                                ? "active-drop"
                                : ""
                            }
                          >
                            {batchOption ? (
                              <ExpandLessRoundedIcon />
                            ) : (
                              <ExpandMoreRoundedIcon />
                            )}
                          </span>
                        </span>
                      </span>
                    )}
                  </div>
                  {isOpen ? (
                    ""
                  ) : batchOption ? (
                    <div>
                      <ul className='option'>
                        <Link to={"/admin/days_code/batches"} state={"batches"}>
                          <li
                            className={
                              state === "batches" ? "active-option" : ""
                            }
                          >
                            Batches
                          </li>
                        </Link>
                        <Link
                          to='/admin/days_code/batch/new'
                          state={"createbatch"}
                        >
                          <li
                            className={
                              state === "createbatch" ? "active-option" : ""
                            }
                          >
                            Create New Batches
                          </li>
                        </Link>
                      </ul>
                    </div>
                  ) : (
                    ""
                  )}
                </>
              </li>
              {authAdminGuard() && (
                <li>
                  <>
                    <div
                      className={`${mentorOption ? "no-hover" : ""} ${
                        state === "mentor" || state === "addmentor"
                          ? "active-drop"
                          : ""
                      }
                     side-nav-option`}
                      onClick={() => {
                        if (isOpen) {
                          setIsOpen(false);
                          setMentorOption(true);
                        }
                      }}
                    >
                      {isOpen ? (
                        <span
                          className={`close-drawer-icon ${
                            state === "mentor" || state === "addmentor"
                              ? "active-drop"
                              : ""
                          } `}
                        >
                          <FaUserTie />
                        </span>
                      ) : (
                        <span>
                          <span
                            onClick={() => {
                              setMentorOption(!mentorOption);
                            }}
                            className={`d-flex justify-content-between align-items-center open-drawer-item ${
                              state === "mentor" || state === "addmentor"
                                ? "active-drop"
                                : ""
                            }`}
                          >
                            <span
                              className={
                                state === "mentor" || state === "addmentor"
                                  ? "active-drop"
                                  : ""
                              }
                            >
                              <FaUserTie className='mr-1' />
                              Mentors
                            </span>
                            <span
                              className={
                                state === "mentor" || state === "addmentor"
                                  ? "active-drop"
                                  : ""
                              }
                            >
                              {mentorOption ? (
                                <ExpandLessRoundedIcon />
                              ) : (
                                <ExpandMoreRoundedIcon />
                              )}
                            </span>
                          </span>
                        </span>
                      )}
                    </div>
                    {isOpen ? (
                      ""
                    ) : mentorOption ? (
                      <div>
                        <ul className='option'>
                          <Link
                            to={"/admin/days_code/mentors"}
                            state={"mentor"}
                          >
                            <li>Mentors</li>
                          </Link>
                          <Link
                            to='/admin/days_code/mentor/new'
                            state={"addmentor"}
                          >
                            <li>Create New Mentor</li>
                          </Link>
                        </ul>
                      </div>
                    ) : (
                      ""
                    )}
                  </>
                </li>
              )}
              <li
                className={`side-nav-option ${
                  state === "codeclass" ? "active " : ""
                }`}
              >
                <Link to={"/admin/days_code/codeclass"} state={"codeclass"}>
                  {isOpen ? (
                    <span
                      className={`close-drawer-icon ${
                        state === "codeclass" ? "active " : ""
                      }`}
                    >
                      <MdOutlineClass />
                    </span>
                  ) : (
                    <span className={state === "codeclass" ? "active " : ""}>
                      <MdOutlineClass className='mr-1 open-drawer-item' />
                      Code Class
                    </span>
                  )}
                </Link>
              </li>
              <li>
                <>
                  <div
                    className={`${problemOption ? "no-hover" : ""} ${
                      state === "problemlist" || state === "addproblem"
                        ? "active-drop"
                        : ""
                    } side-nav-option`}
                    onClick={() => {
                      if (isOpen) {
                        setIsOpen(false);
                        setProblemOption(true);
                      }
                    }}
                  >
                    {isOpen ? (
                      <span
                        className={`close-drawer-icon ${
                          state === "problemlist" || state === "addproblem"
                            ? "active-drop"
                            : ""
                        }`}
                      >
                        <LuFileQuestion />
                      </span>
                    ) : (
                      <span>
                        <span
                          className={`d-flex justify-content-between align-items-center open-drawer-item ${
                            state === "problemlist" || state === "addproblem"
                              ? "active-drop"
                              : ""
                          }`}
                          onClick={() => {
                            setProblemOption(!problemOption);
                          }}
                        >
                          <span
                            className={
                              state === "problemlist" || state === "addproblem"
                                ? "active-drop"
                                : ""
                            }
                          >
                            <LuFileQuestion className='mr-1' />
                            Problems
                          </span>
                          <span
                            className={
                              state === "problemlist" || state === "addproblem"
                                ? "active-drop"
                                : ""
                            }
                          >
                            {problemOption ? (
                              <ExpandLessRoundedIcon />
                            ) : (
                              <ExpandMoreRoundedIcon />
                            )}
                          </span>
                        </span>
                      </span>
                    )}
                  </div>
                  {isOpen ? (
                    ""
                  ) : problemOption ? (
                    <div>
                      <ul className='option'>
                        <Link
                          to={"/admin/days_code/problems"}
                          state={"problemlist"}
                        >
                          <li>Problem List</li>
                        </Link>
                        {authAdminGuard() && (
                          <Link
                            to='/admin/days_code/problem/new'
                            state={"addproblem"}
                          >
                            <li>Add New Problem</li>
                          </Link>
                        )}
                      </ul>
                    </div>
                  ) : (
                    ""
                  )}
                </>
              </li>
              {authAdminGuard() && (
                <li>
                  <>
                    <div
                      className={`${lessonOption ? "no-hover" : ""} ${
                        state === "lessonlist" || state === "addlesson"
                          ? "active-drop"
                          : ""
                      } side-nav-option`}
                      onClick={() => {
                        if (isOpen) {
                          setIsOpen(false);
                          setLessonOption(true);
                        }
                      }}
                    >
                      {isOpen ? (
                        <span
                          className={`close-drawer-icon  ${
                            state === "lessonlist" || state === "addlesson"
                              ? "active-drop"
                              : ""
                          }`}
                        >
                          <MdOutlinePlayLesson />
                        </span>
                      ) : (
                        <span>
                          <span
                            className={`d-flex justify-content-between align-items-center open-drawer-item  ${
                              state === "lessonlist" || state === "addlesson"
                                ? "active-drop"
                                : ""
                            }`}
                            onClick={() => {
                              setLessonOption(!lessonOption);
                            }}
                          >
                            <span
                              className={
                                state === "lessonlist" || state === "addlesson"
                                  ? "active-drop"
                                  : ""
                              }
                            >
                              <MdOutlinePlayLesson className='mr-1' />
                              Lessons
                            </span>
                            <span
                              className={
                                state === "lessonlist" || state === "addlesson"
                                  ? "active-drop"
                                  : ""
                              }
                            >
                              {lessonOption ? (
                                <ExpandLessRoundedIcon />
                              ) : (
                                <ExpandMoreRoundedIcon />
                              )}
                            </span>
                          </span>
                        </span>
                      )}
                    </div>
                    {isOpen ? (
                      ""
                    ) : lessonOption ? (
                      <div>
                        <ul className='option'>
                          <Link
                            to={"/admin/days_code/lessons"}
                            state={"lessonlist"}
                          >
                            <li>View Lessons</li>
                          </Link>
                          <Link
                            to='/admin/days_code/lesson/new'
                            state={"addlesson"}
                          >
                            <li>Add New Lessons</li>
                          </Link>
                        </ul>
                      </div>
                    ) : (
                      ""
                    )}
                  </>
                </li>
              )}
              {authAdminGuard() && (
                <li>
                  <>
                    <div
                      className={`${quizOption ? "no-hover" : ""}  ${
                        state === "quizlist" || state === "addquiz"
                          ? "active-drop"
                          : ""
                      } side-nav-option`}
                      onClick={() => {
                        if (isOpen) {
                          setIsOpen(false);
                          setQuizOption(true);
                        }
                      }}
                    >
                      {isOpen ? (
                        <span
                          className={`close-drawer-icon ${
                            state === "quizlist" || state === "addquiz"
                              ? "active-drop"
                              : ""
                          }`}
                        >
                          <MdOutlineQuiz />
                        </span>
                      ) : (
                        <span>
                          <span
                            className={`d-flex justify-content-between align-items-center open-drawer-item ${
                              state === "quizlist" || state === "addquiz"
                                ? "active-drop"
                                : ""
                            }`}
                            onClick={() => {
                              setQuizOption(!quizOption);
                            }}
                          >
                            <span
                              className={
                                state === "quizlist" || state === "addquiz"
                                  ? "active-drop"
                                  : ""
                              }
                            >
                              <MdOutlineQuiz className='mr-1' />
                              Quizes
                            </span>
                            <span
                              className={
                                state === "quizlist" || state === "addquiz"
                                  ? "active-drop"
                                  : ""
                              }
                            >
                              {quizOption ? (
                                <ExpandLessRoundedIcon />
                              ) : (
                                <ExpandMoreRoundedIcon />
                              )}
                            </span>
                          </span>
                        </span>
                      )}
                    </div>
                    {isOpen ? (
                      ""
                    ) : quizOption ? (
                      <div>
                        <ul className='option'>
                          <Link
                            to={"/admin/days_code/quiz_challenges"}
                            state={"quizlist"}
                          >
                            <li> View Quiz</li>
                          </Link>
                          <Link
                            to='/admin/days_code/quiz_challenge/new'
                            state={"addquiz"}
                          >
                            <li>Add New Quiz</li>
                          </Link>
                        </ul>
                      </div>
                    ) : (
                      ""
                    )}
                  </>
                </li>
              )}
              <li
                className={`side-nav-option ${
                  state === "submission" ? "active " : ""
                }`}
              >
                <Link to={"/admin/days_code/submissions"} state={"submission"}>
                  {isOpen ? (
                    <span
                      className={`close-drawer-icon ${
                        state === "submission" ? "active " : ""
                      } `}
                    >
                      <TbBookUpload />
                    </span>
                  ) : (
                    <span className={state === "submission" ? "active " : ""}>
                      <TbBookUpload className='mr-1 open-drawer-item' />
                      Submission List
                    </span>
                  )}
                </Link>
              </li>
              <li>
                <>
                  <div
                    className={`${userOption ? "no-hover" : ""} ${
                      state === "session_attendance" ||
                      state === "users" ||
                      state === "batchuser"
                        ? "active-drop"
                        : ""
                    } side-nav-option`}
                    onClick={() => {
                      if (isOpen) {
                        setIsOpen(false);
                        setUserOption(true);
                      }
                    }}
                  >
                    {isOpen ? (
                      <span
                        className={`close-drawer-icon  ${
                          state === "session_attendance" ||
                          state === "users" ||
                          state === "batchuser"
                            ? "active-drop"
                            : ""
                        }`}
                      >
                        <HiOutlineUsers />
                      </span>
                    ) : (
                      <span>
                        <span
                          className={`d-flex justify-content-between align-items-center open-drawer-item  ${
                            state === "session_attendance" ||
                            state === "users" ||
                            state === "batchuser"
                              ? "active-drop"
                              : ""
                          }`}
                          onClick={() => {
                            setUserOption(!userOption);
                          }}
                        >
                          <span
                            className={
                              state === "session_attendance" ||
                              state === "users" ||
                              state === "batchuser"
                                ? "active-drop"
                                : ""
                            }
                          >
                            <HiOutlineUsers className='mr-1' />
                            Users
                          </span>
                          <span
                            className={
                              state === "session_attendance" ||
                              state === "users" ||
                              state === "batchuser"
                                ? "active-drop"
                                : ""
                            }
                          >
                            {userOption ? (
                              <ExpandLessRoundedIcon />
                            ) : (
                              <ExpandMoreRoundedIcon />
                            )}
                          </span>
                        </span>
                      </span>
                    )}
                  </div>
                  {isOpen ? (
                    ""
                  ) : userOption ? (
                    <ul className='option'>
                      <Link
                        to={"/admin/days_code/session_attendance"}
                        state={"session_attendance"}
                      >
                        <li> User Attendance</li>
                      </Link>
                      {authAdminGuard() && (
                        <Link to={"/admin/days_code/users/3"} state={"users"}>
                          <li>User List</li>
                        </Link>
                      )}
                      <Link
                        to={"/admin/days_code/batch_users"}
                        state={"batchuser"}
                      >
                        <li>Batch User List</li>
                      </Link>
                    </ul>
                  ) : (
                    ""
                  )}
                </>
              </li>
              <li
                className={`side-nav-option ${
                  state === "feedback" ? "active " : ""
                }`}
              >
                <Link to={"/admin/days_code/feedbacks"} state={"feedback"}>
                  {isOpen ? (
                    <span
                      className={`close-drawer-icon ${
                        state === "feedback" ? "active " : ""
                      }`}
                    >
                      <VscFeedback />
                    </span>
                  ) : (
                    <span className={state === "feedback" ? "active " : ""}>
                      <VscFeedback className='mr-1 open-drawer-item' />
                      Feedbacks
                    </span>
                  )}
                </Link>
              </li>
              <li
                className={`side-nav-option ${
                  state === "notification" ? "active " : ""
                }`}
              >
                <Link
                  to={"/admin/days_code/notification"}
                  state={"notification"}
                >
                  {isOpen ? (
                    <span
                      className={`close-drawer-icon ${
                        state === "notification" ? "active " : ""
                      }`}
                    >
                      <MdOutlineNotificationAdd />
                    </span>
                  ) : (
                    <span className={state === "notification" ? "active " : ""}>
                      <MdOutlineNotificationAdd className='mr-1 open-drawer-item' />
                      Notification
                    </span>
                  )}
                </Link>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </>
  );
};

const mapStateToProps = ({ dayscode }) => ({
  activeTab: dayscode.activeTab,
});

const mapDispatchToProps = {
  setSettingActiveTab,
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(SettingsDashboardNav);

import React, { useEffect, useState } from 'react';
import dayjs from 'dayjs';
import { styled } from "@mui/material/styles";
import { DemoContainer, DemoItem } from '@mui/x-date-pickers/internals/demo';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { DateCalendar } from '@mui/x-date-pickers/DateCalendar';
import { PickersDay } from "@mui/x-date-pickers/PickersDay";

const HighlightedDay = styled(PickersDay)(({ theme }) => ({
  "&.Mui-selected": {
    backgroundColor: theme.palette.primary.main,
    color: theme.palette.primary.contrastText,
  },
  '&.Mui-selected:hover': {
    backgroundColor: '#000',
  }
}));

const ServerDay = (props) => {
  const { highlightedDays = [], day, outsideCurrentMonth, ...other } = props;

  const isSelected =
    !props.outsideCurrentMonth &&
    highlightedDays.includes(day.format("YYYY-MM-DD"));

  return (
    <HighlightedDay
      {...other}
      outsideCurrentMonth={outsideCurrentMonth}
      day={day}
      selected={isSelected}
    />
  );
};

export default function CustomCalendar({
  startDate, endDate
}) {
  const getDatesBetween = (startDate, endDate) => {
    const dates = [];
    let currentDate = new Date(startDate);
    let endDateFormate = new Date(endDate)
    // Move to the next day
    currentDate.setDate(currentDate.getDate() + 1);

    while (currentDate < endDateFormate) {
      dates.push(currentDate.toISOString().split('T')[0]);
      currentDate.setDate(currentDate.getDate() + 1);
    }
    return dates;
  }

  useEffect(() => {
    if (startDate && endDate) {
      let datesArray = getDatesBetween(startDate, endDate)
      console.log(datesArray)
      setHighlitedDays(datesArray)
    }
  }, [startDate, endDate])

  const [value, setValue] = React.useState("");
  const [highlightedDays, setHighlitedDays] = useState(getDatesBetween(startDate, endDate));

  const handleSelectedDate = (date) => {
    console.log("handleSelectedDate", date)
  }

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <DemoContainer components={['DateCalendar', 'DateCalendar', 'DateCalendar']}>
        <DemoItem label={''}>
          <DateCalendar
            onChange={(newValue) => handleSelectedDate(newValue)}
            defaultValue={dayjs(new Date())}
            views={['year', 'month', 'day']}
            slots={{
              day: ServerDay,
            }}
            slotProps={{
              day: {
                highlightedDays,
              },
            }}
          />
        </DemoItem>
      </DemoContainer>
    </LocalizationProvider>
  );
}
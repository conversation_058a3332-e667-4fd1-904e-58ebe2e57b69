import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useParams } from "react-router";
import { MdEventAvailable } from "react-icons/md";

import { deleteEventTimeline, getEventDetails } from "../../../../actions";
import CustomLoader from "../../../../../../components/sharedComponents/CustomLoader";
import EventTimelineFormDrawer from "./EventTimelineFormDrawer";
import EventTimelineList from "./EventTimelineList";

const EventTimeline = () => {

  const dispatch = useDispatch();
  const { id } = useParams();

  const { eventDetailsLoading, eventDetails } =
    useSelector((state) => state.event) || {};

  useEffect(() => {
    console.log(id);
    dispatch(getEventDetails(id));
  }, [dispatch, id]);

  /*  useEffect(() => {
    toast.promise(
      dispatch(getEventTimelines({ eventId: id })),
      {
        loading: 'Getting Timlines...',
        success: <b>Timlines Loaded!</b>,
        error: <b>Could not save.</b>,
      }
    );
  }, []) */

  return (
    <>
      <div className='event-timeline p-3'>
        <h1>Event Timeline</h1>
        {eventDetailsLoading ? (
          <>
            <CustomLoader />
          </>
        ) : (
          <>
            <div className='row mb-5 timeline mx-0'>
              <div className='col-12 timeline-header '>
                <h4 className='mb-0'>
                  <MdEventAvailable /> {eventDetails?.timelines?.length}
                </h4>
                <EventTimelineFormDrawer />
              </div>
            </div>
            <div className='bg-white border rounded-lg shadow m-5 p-3'>
              <EventTimelineList
                eventId={id}
                deleteEventTimeline={deleteEventTimeline}
                eventDetails={eventDetails}
              />
            </div>
          </>
        )}
      </div>
    </>
  );
};

export default EventTimeline;

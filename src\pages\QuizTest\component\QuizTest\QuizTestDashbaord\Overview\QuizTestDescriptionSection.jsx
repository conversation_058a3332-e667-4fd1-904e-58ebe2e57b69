import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useParams } from "react-router-dom";
import Parse from 'html-react-parser';

import { EditQuizTest } from "../../../../actions";
import TextEditor from "../../../../../../components/sharedComponents/TextEditor";

const QuizTestDescriptionSection = () => {
  
  const dispatch = useDispatch();;
  const {id} = useParams();

  const {currentQuizTest} = useSelector(state=>state.quizTest);

  const [testDescription, setTestDescription ] = useState('<p></p>');
  const [isEditable, setIsEditable] = useState(false);

  useEffect(()=>{
    setTestDescription(currentQuizTest?.description);
  },[currentQuizTest]);

  const handleAddClick = (e) => {
    e.preventDefault();
    setIsEditable(true);
  };

  const handleSaveClick = (e) => {
    e.preventDefault();
    setIsEditable(false);
    const data={"quizTest":id,"description":testDescription};
    dispatch(EditQuizTest(data))
  };

  return (
    <div className="row mx-0 mb-4 px-0 w-100">
      <div className="col-12 px-0 d-flex align-items-baseline">
        <span>
          <h5>Test Description</h5>
        </span>
        <span>
          {isEditable ? (
            <a href="" className="m-2" onClick={handleSaveClick}>
              Save
            </a>
          ) : (
            <a href="" className="m-2" onClick={handleAddClick}>
              Add
            </a>
          )}
        </span>
      </div>
      {isEditable && (
        <div className="col-12 py-4 px-0 border">
          <TextEditor
            text = {testDescription} 
            handleTextEditor = {setTestDescription} 
          />
        </div>
      )}
      {!isEditable && (
        <div className="col-12 py-4 test-description">{testDescription && Parse(testDescription)}</div>
      )}
    </div>
  );
};

export default QuizTestDescriptionSection;

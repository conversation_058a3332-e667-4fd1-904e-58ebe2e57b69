import { connect } from "react-redux";
import {
  getDaysUsersListOptions,
  addUserAttendancePresent,
  deleteUserAttendancePresent,
} from "../../../../actions";

import AddUserAttendance from "./AddUserAttendance";

const mapStateToProps = ({ dayscode, auth }) => ({
  currentUser: auth.currentUser ? auth.currentUser : {},
  problemsListLoading: dayscode.problemsListLoading,
  problemDetailsLoading: dayscode.problemDetailsLoading,
  submissions: dayscode.submissions,
  daysUsers: dayscode.daysUsers,
  getUserSubmissionsLoading: dayscode.getUserSubmissionsLoading,
  userAttendancePresentLoading: dayscode.userAttendancePresentLoading,
});

const mapDispatchToProps = {
  getDaysUsersListOptions,
  addUserAttendancePresent,
  deleteUserAttendancePresent,
};

export default connect(mapStateToProps, mapDispatchToProps)(AddUserAttendance);

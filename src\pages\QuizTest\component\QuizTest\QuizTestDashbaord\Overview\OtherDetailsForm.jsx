import React, { useEffect } from "react";
import { Field, reduxForm } from "redux-form";
import { useDispatch, useSelector } from "react-redux";
import { useParams } from "react-router";

import { EditQuizTest } from "../../../../actions";

const OtherDetailsForm = ({ isEditable, toggle, initialize, handleSubmit }) => {
  
  const dispatch = useDispatch();
  const { id } = useParams();

  const { currentQuizTest } = useSelector((state) => state.quizTest);

  const onSubmit = (value) => {
    let formData = {
      ...value,
      quizTest: id,
    };
    dispatch(EditQuizTest(formData));
  };

  useEffect(() => {
    initialize(currentQuizTest);
  }, [currentQuizTest, initialize]);

  return (
    <>
      {currentQuizTest && (
        <form
          className='otherdetailsform w-100'
          onSubmit={handleSubmit(onSubmit)}
        >
          <div className='row mx-0'>
            <div className='col-6'>
              <div className='col-12 pt-3 px-0'>
                <p>Test Name</p>
                <Field
                  name='quizTitle'
                  component='input'
                  type='text'
                  readOnly={!isEditable}
                  className='form-control'
                />
              </div>
              <div className='col-12 pt-3 px-0'>
                <p>Company Name</p>
                <Field
                  name='cmpName'
                  component='input'
                  type='text'
                  readOnly={!isEditable}
                  className='form-control'
                />
              </div>
              <div className='col-12 pt-3 px-0'>
                <p>Company URL</p>
                <Field
                  name='cmpUrl'
                  component='input'
                  type='text'
                  readOnly={!isEditable}
                  className='form-control'
                />
              </div>
              <div className='col-12 pt-3 px-0'>
                <p>Company Logo URL</p>
                <Field
                  name='cmpLogoUrl'
                  component='input'
                  type='text'
                  readOnly={!isEditable}
                  className='form-control'
                />
              </div>
              <div className='col-12 pt-3 px-0'>
                <p>Test Technology</p>
                <Field
                  name='quizTechnology'
                  component='input'
                  type='text'
                  readOnly={!isEditable}
                  className='form-control'
                />
              </div>
              <div className='col-12 pt-3 px-0'>
                <p>Test Category</p>
                <Field
                  name='quizCategory'
                  component='select'
                  disabled={!isEditable}
                  className='form-control'
                >
                  <option value=''>Select a category</option>
                  <option value='assessment'>Assessment</option>
                  <option value='quizTest'>Quiz</option>
                </Field>
              </div>
              <div className='col-12 pt-3 px-0'>
                <p>Test duration</p>
                <Field
                  name='duration'
                  component='input'
                  type='text'
                  readOnly={!isEditable}
                  className='form-control'
                />
              </div>
            </div>
            <div className='col-6'>
              <div className='col-12 pt-3 px-0'>
                <p>Starts On</p>
                <Field
                  name='startDate'
                  component='input'
                  type='date'
                  readOnly={!isEditable}
                  className='form-control'
                />
              </div>
              <div className='col-12 pt-3 px-0'>
                <p>Ends On</p>
                <Field
                  name='endDate'
                  component='input'
                  type='date'
                  readOnly={!isEditable}
                  className='form-control'
                />
              </div>
              <div className='col-12 pt-3 px-0'>
                <p>Test Link</p>
                <Field
                  name='test_link'
                  component='input'
                  type='text'
                  readOnly={!isEditable}
                  className='form-control'
                />
              </div>
              <div className='col-12 pt-3 px-0'>
                <p>Practice Test Link</p>
                <Field
                  name='practice_test_link'
                  component='input'
                  type='text'
                  readOnly={!isEditable}
                  className='form-control'
                />
              </div>
              <div className='col-12 pt-3 px-0'>
                <p>Test Type</p>
                <Field
                  name='access'
                  component='input'
                  type='text'
                  readOnly={!isEditable}
                  className='form-control'
                />
              </div>
              <div className='col-12 pt-3 px-0'>
                <p>Assessment Id</p>
                <Field
                  name='_id'
                  component='input'
                  type='text'
                  readOnly
                  className='form-control'
                />
              </div>
              <div className='col-12 pt-3 px-0'>
                <p>Tags</p>
                <Field
                  name='tags'
                  component='input'
                  type='text'
                  readOnly={!isEditable}
                  placeholder='+ Add Tags'
                  className='form-control'
                />
              </div>
            </div>
          </div>
          {isEditable && (
            <div className='d-flex justify-content-end mt-3'>
              <button type='submit' className='btn btn-primary mx-2'>
                Edit
              </button>
              <button
                className='btn btn-secondary mx-2'
                onClick={(e) => {
                  e.preventDefault();
                  toggle();
                }}
              >
                Cancel
              </button>
            </div>
          )}
        </form>
      )}
    </>
  );
};

export default reduxForm({
  form: "otherDetailsForm",
})(OtherDetailsForm);

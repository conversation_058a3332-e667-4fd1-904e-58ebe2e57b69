import React from "react";
import { RiHome2Line } from "react-icons/ri";
import { LuSettings } from "react-icons/lu";
import { LuLayout } from "react-icons/lu";
import { BsLayoutTextWindowReverse } from "react-icons/bs";
import { RiTimeLine } from "react-icons/ri";
import { RiUserVoiceLine } from "react-icons/ri";
import { SiGithubsponsors } from "react-icons/si";
import { IoTicketOutline } from "react-icons/io5";
import { FiImage } from "react-icons/fi";
import { CgDatabase } from "react-icons/cg";
import { FaProjectDiagram } from "react-icons/fa";

import ToolTip from "../../../../../components/sharedComponents/ToolTip";

const EventDashboardNav = ({ activeTab, setActiveTab }) => {
  return (
    <>
      <div className='row mr-0'>
        <div className='col-12'>
          <div className='event-dashboard-header w-100 px-0 px-3 py-2 table-responsive table'>
            <ul>
              <li>
                <ToolTip title='General Details' placement='bottomLeft'>
                  <span
                    onClick={() => setActiveTab("general")}
                    className={`text-nowrap d-flex align-items-center ${
                      activeTab === "general" ? "active" : ""
                    }`}
                  >
                    <RiHome2Line className='d-flex align-items-center overview-nav-img' />
                    General
                  </span>
                </ToolTip>
              </li>
              <li>
                <ToolTip title='Edit Event Details' placement='bottomLeft'>
                  <span
                    onClick={() => setActiveTab("settings")}
                    className={`text-nowrap d-flex align-items-center ${
                      activeTab === "settings" ? "active" : ""
                    }`}
                  >
                    <LuSettings className='d-flex align-items-center overview-nav-img' />
                    Settings
                  </span>
                </ToolTip>
              </li>
              <li>
                <ToolTip title='Ticket' placement='bottomLeft'>
                  <span
                    onClick={() => setActiveTab("ticket")}
                    className={`text-nowrap d-flex align-items-center ${
                      activeTab === "ticket" ? "active" : ""
                    }`}
                  >
                    <IoTicketOutline className='d-flex align-items-center overview-nav-img' />
                    Ticket
                  </span>
                </ToolTip>
              </li>
              <li>
                <ToolTip title='Images' placement='bottomLeft'>
                  <span
                    onClick={() => setActiveTab("images")}
                    className={`text-nowrap d-flex align-items-center ${
                      activeTab === "images" ? "active" : ""
                    }`}
                  >
                    <FiImage className='d-flex align-items-center overview-nav-img ' />
                    Images
                  </span>
                </ToolTip>
              </li>
              <li>
                <ToolTip title='Resource' placement='bottomLeft'>
                  <span
                    onClick={() => setActiveTab("resource")}
                    className={`text-nowrap d-flex align-items-center ${
                      activeTab === "resource" ? "active" : ""
                    }`}
                  >
                    <CgDatabase className='d-flex align-items-center overview-nav-img' />
                    Resource
                  </span>
                </ToolTip>
              </li>
              <li>
                <ToolTip title='Page Layout' placement='bottomLeft'>
                  <span
                    onClick={() => setActiveTab("layout")}
                    className={`text-nowrap d-flex align-items-center ${
                      activeTab === "layout" ? "active" : ""
                    }`}
                  >
                    <LuLayout className='d-flex align-items-center overview-nav-img' />
                    Page Layout
                  </span>
                </ToolTip>
              </li>
              <li>
                <ToolTip title='Event Forms layout' placement='bottomLeft'>
                  <span
                    onClick={() => setActiveTab("formLayout")}
                    className={`text-nowrap d-flex align-items-center ${
                      activeTab === "formLayout" ? "active" : ""
                    }`}
                  >
                    <BsLayoutTextWindowReverse className='d-flex align-items-center overview-nav-img' />
                    Forms Layout
                  </span>
                </ToolTip>
              </li>
              <li>
                <ToolTip
                  title='Content Management System'
                  placement='bottomLeft'
                >
                  <span
                    onClick={() => setActiveTab("content")}
                    className={`text-nowrap d-flex align-items-center ${
                      activeTab === "content" ? "active" : ""
                    }`}
                  >
                    <FaProjectDiagram className='d-flex align-items-center overview-nav-img' />
                    CMS
                  </span>
                </ToolTip>
              </li>
              <li>
                <ToolTip title='Event timeline' placement='bottomLeft'>
                  <span
                    onClick={() => setActiveTab("timeline")}
                    className={`text-nowrap d-flex align-items-center ${
                      activeTab === "timeline" ? "active" : ""
                    }`}
                  >
                    <RiTimeLine className='d-flex align-items-center overview-nav-img' />
                    Timeline
                  </span>
                </ToolTip>
              </li>
              <li>
                <ToolTip title='Event Speakers' placement='bottomLeft'>
                  <span
                    onClick={() => setActiveTab("speaker")}
                    className={`text-nowrap d-flex align-items-center ${
                      activeTab === "speaker" ? "active" : ""
                    }`}
                  >
                    <RiUserVoiceLine className='d-flex align-items-center overview-nav-img' />
                    Speakers
                  </span>
                </ToolTip>
              </li>
              <li>
                <ToolTip title='Event Sponsers' placement='bottomLeft'>
                  <span
                    onClick={() => setActiveTab("sponsor")}
                    className={`text-nowrap d-flex align-items-center ${
                      activeTab === "sponsor" ? "active" : ""
                    }`}
                  >
                    <SiGithubsponsors className='d-flex align-items-center overview-nav-img' />
                    Sponsors
                    {/* 30006d 1671a6 */}
                  </span>
                </ToolTip>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </>
  );
};

export default EventDashboardNav;

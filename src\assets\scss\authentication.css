.btn-login {
  background-color: #652fec;
  padding: 10px 30px;
  border-radius: 5px;
  outline: none;
  border: none;
  color: white;
  font-weight: 500;
  width: 100%;
  font-size: 18px;
  transition: all 0.3s ease;
}

.btn-login:hover {
  background-color: #6a40d6;
}

.auth-page .auth-header img {
  height: 40px;
}
.auth-page > .row {
  height: calc(100vh - 50px);
}
.auth-page > .row .auth-left {
  height: 100%;
}
.auth-page > .row .auth-left img {
  width: 70%;
}
.auth-page > .row .btn-link {
  color: white;
  font-style: italic;
  text-decoration: underline;
}
.auth-page > .row .auth-right {
  background: linear-gradient(135deg, #e8bbfc 0%, #9181eb 100%);
  height: 100%;
  position: relative;
}
.auth-page > .row .auth-right .auth-container {
  padding: 20px;
  color: white;
  width: 100%;
}
.auth-page > .row .auth-right .auth-container h1 {
  font-weight: bold;
}
.auth-page > .row .auth-right .auth-container form {
  max-width: 450px;
  margin: 50px auto 0;
}
.auth-page > .row .auth-right .auth-container form .form-group {
  width: 100%;
  margin-top: 10px;
  display: block;
}

@media (max-width: 767px) {
  .auth-page .auth-right .auth-container {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    gap: 10px;
  }
  .auth-page .auth-right .auth-container form {
    margin-top: 20px;
  }
}/*# sourceMappingURL=authentication.css.map */
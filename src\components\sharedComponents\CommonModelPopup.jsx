import React from "react";
import PropTypes from 'prop-types'
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, <PERSON>dal<PERSON>ody, ModalFooter } from "reactstrap";
import { FaCheckCircle, FaExclamationTriangle } from "react-icons/fa";

const CommonModelPopup = ({
  model,
  toggle,
  message,
  submitButtonText = "Okay",
  modeltitle,
  type,
  submitButtonColor,
}) => {
  return (
    <div>
      <Modal centered isOpen={model} toggle={toggle} className="text-center">
        <ModalHeader
          toggle={toggle}
        > <div className="d-flex justify-content-space-between align-items-start">
          {type === "success" ? (<div className="icon-s mr-4"> <FaCheckCircle className="confirm-icon" aria-hidden="true" /></div>)
          : type === "danger" ? (
            <div className="icon-d mr-4"> <FaExclamationTriangle className="tri-icon" aria-hidden="true" /> </div>
          ) : ("")}
          <h4 className="modal-title mb-5">{modeltitle}</h4>
          </div>
        </ModalHeader>

        <ModalBody>

          <p>{message}</p>
        </ModalBody>
        <ModalFooter className="d-flex justify-content-center align-items-center">
          <Button color={submitButtonColor} onClick={toggle}>
            {submitButtonText}
          </Button>
        </ModalFooter>
      </Modal>
    </div>
  );
};

CommonModelPopup.propTypes = {
  model: PropTypes.bool.isRequired,
  toggle: PropTypes.func.isRequired,
  message: PropTypes.string.isRequired,
  submitButtonText: PropTypes.string,
  modeltitle: PropTypes.string.isRequired,
  type: PropTypes.string.isRequired,
  submitButtonColor: PropTypes.string.isRequired,
};

export default CommonModelPopup;

import React, { useState } from "react";

import CourseHeader from "./CourseHeader";
import CourseSideNav from "./CourseSideNav";

const CourseLayoutContainer = ({ children }) => {
  
  const [sideDrawerOpen, setSideDrawerOpen] = useState(false)

  const drawerToggleClickHandler = () => {
    setSideDrawerOpen(!sideDrawerOpen);
  };

  return (
    <>
      <div className={`px-0 ${sideDrawerOpen ? 'course-side-nav open' : 'course-side-nav'}`}>
        <CourseSideNav />
      </div>
      <div className='px-0 course-page-wrapper'>
        <CourseHeader drawerClickHandler={drawerToggleClickHandler} />
        <div className="course-list">
          {children}
        </div>
      </div>
    </>
  );
};

export default CourseLayoutContainer;

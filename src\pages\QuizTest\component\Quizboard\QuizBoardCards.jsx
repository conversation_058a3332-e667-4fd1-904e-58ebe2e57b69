import { Link } from "react-router-dom";

import image from "../../../../assets/images/svg/quiz-dashborad-aasses.png";

const QuizBoardCards = () => {
  return (
    <div className='col-12 px-5'>
      <div className='row  py-4 task-section'>
        
        <Link to='/quiz/tests' className='col-lg-3 col-md-6 col-sm-12 task-card'>
          <div className='card-content d-flex'>
            <div className='card-image'>
              <img src={image} alt='Assessment' className='img-fluid' />
            </div>
            <div className='card-text'>
                <h6 className='card-title'>Assessments</h6>
              <p className='card-description'>View and edit your assessments</p>
            </div>
          </div>
        </Link>
        

        <Link to='/#'className='col-lg-3 col-md-6 col-sm-12 task-card'>
          <div className='card-content d-flex'>
            <div className='card-image'>
              <img src={image} alt='Profile' className='img-fluid' />
            </div>
            <div className='card-text'>
                <h6 className='card-title'>Profile</h6>
              <p className='card-description'>
                View candidate links, profiles, and test status
              </p>
            </div>
          </div>
        </Link>
        

        <Link to='/#' className='col-lg-3 col-md-6 col-sm-12 task-card'>
          <div className='card-content d-flex'>
            <div className='card-image'>
              <img src={image} alt='Library' className='img-fluid' />
            </div>
            <div className='card-text'>
                <h6 className='card-title'>Library</h6>
              <p className='card-description'>
                Access all the questions created by admin
              </p>
            </div>
          </div>
       </Link>
       
      </div>
    </div>
  );
};

export default QuizBoardCards;

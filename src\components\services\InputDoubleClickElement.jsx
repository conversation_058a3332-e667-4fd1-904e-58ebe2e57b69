// ElementMaker.js

import React from "react";

// Creat an ElementMaker component
const InputDoubleClickElement = ({ showInputEle, handleDoubleClick, value, handleChange, handleBlur }) => {
  return (
    // Render a <span> element
    <span>
      {
        // Use JavaScript's ternary operator to specify <span>'s inner content
        showInputEle ? (
          <input
            type="text"
            value={value}
            onChange={handleChange}
            onBlur={handleBlur}
            autoFocus
          />
        ) : (
          <span
            onDoubleClick={handleDoubleClick}
            style={{
              display: "inline-block",
              height: "25px",
              minWidth: "300px",
            }}
          >
            {value}
          </span>
        )
      }
    </span>
  );
}

export default InputDoubleClickElement;
import React from "react";

import LayoutContainer from "../LayoutContainer";
import CardDesign from "./DetailCard";
import FeedbackForm from "./FeedbackForm";

const General = () => {
  return (
    <>
      <LayoutContainer>
        <div className='row mx-0 solution-nav'>
          <div className='col-md-8 col-6 mx-0 p-2 d-flex align-items-center'>
            <h4 className='py-md-3 py-2 mb-0'>
              <i className='fal fa-file-alt mr-2' />
              General
            </h4>
          </div>
        </div>

        <div style={{ backgroundColor: "#222222" }} className='rounded m-3 p-3'>
          <div
            style={{ color: "white", fontSize: "30px" }}
            className=' d-flex justify-content-center'
          >
            Overall Batches
          </div>
          <ul>
            <li style={{ color: "white", fontSize: "25px" }} className='ml-4 '>
              {" "}
              Total no. of batches:{" "}
            </li>
            <li style={{ color: "white", fontSize: "25px" }} className='ml-4 '>
              {" "}
              Increase in users in past few years :{" "}
            </li>
            <svg
              height='300px'
              viewBox='0 0 2000 1000'
              xmlns='http://www.w3.org/2000/svg'
            >
              <path
                d='M0 1000h80v-8.654q0-4-4-4H4q-4 0-4 4ZM100 1000h80v-35.96q0-4-4-4h-72q-4 0-4 4ZM200 1000h80V898.81q0-4-4-4h-72q-4 0-4 4ZM300 1000h80V861.768q0-4-4-4h-72q-4 0-4 4ZM400 1000h80V848.94q0-4-4-4h-72q-4 0-4 4ZM500 1000h80V755.041q0-4-4-4h-72q-4 0-4 4ZM600 1000h80V811.215q0-4-4-4h-72q-4 0-4 4ZM700 1000h80V770.411q0-4-4-4h-72q-4 0-4 4ZM800 1000h80V759.984q0-4-4-4h-72q-4 0-4 4ZM900 1000h80V587.048q0-4-4-4h-72q-4 0-4 4ZM1000 1000h80V610.71q0-4-4-4h-72q-4 0-4 4ZM1100 1000h80V712.088q0-4-4-4h-72q-4 0-4 4ZM1200 1000h80V603.595q0-4-4-4h-72q-4 0-4 4ZM1300 1000h80V532.337q0-4-4-4h-72q-4 0-4 4ZM1400 1000h80V610.264q0-4-4-4h-72q-4 0-4 4ZM1500 1000h80V282.68q0-4-4-4h-72q-4 0-4 4ZM1600 1000h80V552.15q0-4-4-4h-72q-4 0-4 4ZM1700 1000h80V238.524q0-4-4-4h-72q-4 0-4 4ZM1800 1000h80V266.735q0-4-4-4h-72q-4 0-4 4ZM1900 1000h80V-.835q0-4-4-4h-72q-4 0-4 4Z'
                fill='white'
              />
            </svg>
          </ul>
          <CardDesign />
          <CardDesign />
          <FeedbackForm />
        </div>
      </LayoutContainer>
    </>
  );
};

export default General;

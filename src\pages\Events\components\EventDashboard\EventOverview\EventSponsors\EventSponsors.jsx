import React, { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { reduxForm } from "redux-form";
import { useParams } from "react-router-dom";
import { RiDeleteBin6Line } from "react-icons/ri";
import {
  DropdownItem,
  DropdownMenu,
  DropdownToggle,
  UncontrolledDropdown,
} from "reactstrap";

import { deleteEventSponsor, getEventSponsors } from "../../../../actions";
import ToolTip from "../../../../../../components/sharedComponents/ToolTip";
import { setSponsorsList } from "../../../../../Sponsors/actions";
import CustomLoader from "../../../../../../components/sharedComponents/CustomLoader";
import ImageFrame from "../../../../../../components/sharedComponents/ImageFrame";
import AddEventSponsorDrawer from "./AddEventSponsorDrawer";

const EventSponsors = () => {
  
  const dispatch = useDispatch();
  const { id } = useParams();

  const { eventSponsorsLoading, eventSponsors } =
    useSelector((state) => state.event) || {};

  useEffect(() => {
    dispatch(setSponsorsList({ Speakers: [] }));
    dispatch(getEventSponsors(id));
  }, [dispatch]);

  return (
    <>
      <div className='row mx-0 p-2'>
        <div className='sponsor-header'>
          <h3>Event Sponsors List</h3>
          <AddEventSponsorDrawer />
        </div>
        <div className='col-12 p-0  '>
          {eventSponsorsLoading ? (
            <CustomLoader />
          ) : (
            <>
              <div className='row mx-0 py-3'>
                {eventSponsors.length ? (
                  eventSponsors.map((sponsor, i) => (
                    <div className='col-12 col-md-3' key={i}>
                      <div className='sponsor-card'>
                        <UncontrolledDropdown
                          setActiveFromChild
                          className='event-host-action-toggle'
                        >
                          <DropdownToggle tag='span' className='card-action'>
                            <ToolTip title='Remove' placement='bottom'>
                              <RiDeleteBin6Line className='dlt-card-btn' />
                            </ToolTip>
                          </DropdownToggle>
                          <DropdownMenu
                            className='dropdown-menu mt-3 card-shadow'
                            left='true'
                          >
                            <DropdownItem header>
                              <span>Actions</span>
                            </DropdownItem>
                            <DropdownItem
                              onClick={() =>
                                dispatch(
                                  deleteEventSponsor({
                                    eventId: id,
                                    id: sponsor._id,
                                  }),
                                )
                              }
                              className='remove-btn'
                            >
                              Remove Sponsor
                            </DropdownItem>
                          </DropdownMenu>
                        </UncontrolledDropdown>
                        <ImageFrame imgUrl={sponsor?.sponsor.imgUrl} />
                        <h5 className='mt-2'>{sponsor?.sponsor?.name}</h5>
                        <span>{sponsor?.type}</span>
                        <p className='mb-0'>{sponsor.bio}</p>
                      </div>
                    </div>
                  ))
                ) : (
                  <span className='col-12 no-speaker'>
                    No Sponsor Added Into Event
                  </span>
                )}
              </div>
            </>
          )}
        </div>
      </div>
    </>
  );
};

export default reduxForm({
  form: "add-event-speaker",
})(EventSponsors);

import React from "react";
import usePermissions from "../../../../../hooks/userPermission";

const RoadmapListCard = ({ roadmapList, handelroadmapdelete ,editAction}) => {
  const { hasPermission } = usePermissions();
  return (
    <>
      <table className="table">
        <thead>
          <tr>
            <th>Day</th>
            <th>Title</th>
            <th>Topic</th>
            <th>Action</th>
          </tr>
        </thead>
        <tbody>
          {roadmapList
            .sort((a, b) => a.day - b.day)
            .map((roadmap) => {
              return (
                <tr key={roadmap._id}>
                  <td>{roadmap.day}</td>
                  <td>{roadmap.title}</td>
                  <td>{roadmap.topic}</td>
                  <td>
                   { hasPermission("batch", "delete") &&<i
                      className="fas fa-trash"
                      onClick={() => handelroadmapdelete(roadmap._id)}
                    />}
                    <i
                      className="fas fa-edit"
                      onClick={() => editAction(roadmap)}
                    />
                  </td>
                </tr>
              );
            })}
        </tbody>
      </table>
    </>
  );
};

export default RoadmapListCard;

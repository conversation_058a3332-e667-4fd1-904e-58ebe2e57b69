import { Link } from "react-router-dom";
import { MdOutlineEventNote } from "react-icons/md";
import { LuSettings } from "react-icons/lu";
import { MdOutlineInsights } from "react-icons/md";
import { PiUsers } from "react-icons/pi";

import ToolTip from "../../../../components/sharedComponents/ToolTip";

const EventStudioSideNav = () => {
  return (
    <>
      <div className="event-side-nav">
        <ul>
          <ToolTip title="View Event List" placement="right">
            <li>
              <Link to="/event/dashboard">
                <MdOutlineEventNote />
                <span>Events List</span>
              </Link>
            </li>
          </ToolTip>
          <ToolTip title="Setting" placement="right">
            <li>
              <Link to="/event/settings">
                <LuSettings />
                <span>Settings</span>
              </Link>
            </li>
          </ToolTip>
          <ToolTip title="Event Insight" placement="right">
            <li id="Event-insight">
              <Link to="/event/insights">
                <MdOutlineInsights />
                <span>Insights</span>
              </Link>
            </li>
          </ToolTip>
          <ToolTip title="View Contributors" placement="right">
            <li>
              <Link to="/event/contributors">
                <PiUsers />
                Contributors
              </Link>
            </li>
          </ToolTip>
        </ul>
      </div>
    </>
  );
};

export default EventStudioSideNav;

import React, { useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { Field, reduxForm } from 'redux-form'
import { Link, useNavigate } from 'react-router-dom'

import { createCourse } from '../../actions/operations'
import { renderInputField, renderSelectField } from '../../../../components/sharedComponents/ReduxFormFields'
import BannerImageUpload from '../../../../components/sharedComponents/BannerImageUpload';
import { getOptions } from '../../../../components/utils';
import { priceOptions } from './helper';
import CustomLoader from '../../../../components/sharedComponents/CustomLoader'

const CreateCourse = ({
  reset,
  handleSubmit,
  submitting,
  type
}) => {
  const dispatch = useDispatch()
  const navigate = useNavigate()

  const { createCourseLoading } = useSelector((state) => state.course) || {}

  const [uplodedImg, setUploadedImg] = useState('')

  const onSubmit = (values) => {
    const course = { ...values }
    course['coverImage'] = uplodedImg
    dispatch(createCourse(course)).then((res) => {
      if (res && res.success) {
        navigate(`/course/${res.course._id}/edit`)
      }
    })
    reset('create-course')
  }

  return (
    <>
      <div className="row mx-0">
        <div className="col-md-12 col-6 mx-0 px-md-5 px-2 d-flex align-items-center justify-content-between">
          <h4 className="mb-0 py-md-3 py-2">{'Create Course'}</h4>
          <h4><Link to="/course"><i className="fal fa-times" /></Link></h4>
        </div>
      </div>
      <div className="row mx-0 d-flex justify-content-center">
        <div className="col-6 border rounded-lg m-5">
          <form className="py-3 px-md-5 px-1" onSubmit={handleSubmit(onSubmit)}>
            <small>
              Lorazepam, sold under the brand name Ativan among others, is a benzodiazepine medication. It is used to treat anxiety disorders, trouble sleeping, severe agitation, active seizures including status epilepticus, alcohol withdrawal, and chemotherapy-induced nausea and vomiting
            </small>
            <div>
              <span className="form-label">Upload Banner Image</span>
              {
                uplodedImg &&
                <div className='uploaded-img-preview border p-2'>
                  <img src={uplodedImg} alt="course-banner" className='mx-auto img-fluid' />
                </div>
              }
              <BannerImageUpload setUploadedImg={setUploadedImg} />
            </div>
            <Field
              type="text"
              name="name"
              label="Title"
              placeholder=""
              component={renderInputField}
              // validate={[required]}
            />
            <Field
              type="textarea"
              name="summary"
              label="Summary"
              placeholder=""
              component={renderInputField}
              // validate={[required]}
            />
            <Field
              type="text"
              name="promoVideo"
              label="Promotional video (YouTube URL only)"
              placeholder=""
              component={renderInputField}
            />
            <Field
              type="textarea"
              name="description"
              label="Description"
              placeholder=""
              component={renderInputField}
            />
            <div className="row m-0 border rounded-lg my-2 p-md-3 p-0">
              <div className="col-md-6 col-12">
                <Field
                  name="price"
                  label="Problem Day"
                  placeholder="300₹"
                  options={getOptions(priceOptions, 'price')} // price is form variable
                  textField={'price'}
                  component={renderSelectField}
                  // validate={[required]}
                />
              </div>
            </div>
            <div className="row my-4">
              <div className="col-12 text-right">
                <button type="submit" className="btn btn-primary" disabled={submitting}>
                  <span>{createCourseLoading ? <CustomLoader /> : 'Create Course'}</span>
                </button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </>
  )
}

export default reduxForm({
  form: 'create-course',
})(CreateCourse);

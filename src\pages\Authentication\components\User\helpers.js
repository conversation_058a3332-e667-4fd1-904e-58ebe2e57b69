import React from "react";
import moment from "moment";
import { <PERSON> } from "react-router-dom";
import { Button } from "reactstrap";

export const getColumns = (UserForm) => [
  {
    dataField: "",
    text: "S.NO.",
    align: "center",
    headerAlign: "center",
    headerClasses: "table-header s-no",
    formatter: (cell, row, index) => <span>{++index}</span>,
  },
  {
    dataField: "email",
    text: "Email",
    sort: true,
    style: { color: "#757575" },
    headerClasses: "table-header status",
    formatter: (cell, row) => (
      <span>
        <Link to={`/profile/${row.userName}`}>{row.email}</Link>
      </span>
    ),
  },
  {
    dataField: "firstName",
    text: "Name",
    sort: true,
    headerClasses: "table-header name",
    style: { color: "#757575" },
  },

  {
    dataField: "phone",
    text: "Phone",
    sort: true,
    style: { color: "#757575" },
    align: "left",
    headerAlign: "left",
    headerClasses: "table-header group-name",
  },
  {
    dataField: "university",
    text: "University",
    sort: true,
    style: { color: "#757575" },
    align: "left",
    headerAlign: "left",
    headerClasses: "table-header group-name",
  },
  {
    dataField: "createdAt",
    text: "Registered Date",
    sort: true,
    style: { color: "#757575" },
    align: "left",
    headerAlign: "left",
    headerClasses: "table-header group-name",
    formatter: (cell, row) => (
      <span>
        {row.createdAt === null
          ? "Invalid date"
          : moment(row.createdAt).format("LLL")}
      </span>
    ),
  },
  {
    dataField: "",
    text: " Actions",
    style: { color: "#757575" },
    align: "left",
    headerAlign: "left",
    headerClasses: "table-header group-name",
    formatter: (cell, row) => (
      <Button onClick={()=>UserForm(row)}>Add Review</Button>
    ),
  }
];

export const daysCodeList = [
  "<EMAIL>",
  "<EMAIL>",
  "Yashashri",
  "Diksha",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "Snehita S Naik",
  "Army",
  "Sheetal",
  "Anutoshi333",
  "<EMAIL>",
  "<EMAIL>",
  "Zehra Sadik",
  "janicemonteiro",
  "eliiie",
  "DevansheeGupta",
  "<EMAIL>",
  "Shivangi11",
  "<EMAIL>",
  "<EMAIL>",
  "Chandana",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "Madhav2204",
  "<EMAIL>",
  "Pranav_SR",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "jay_vk18",
  "<EMAIL>",
  "bernce",
  "Annan1035",
  "antim@123",
  "RJ_SWARAJ",
  "anmol957",
  "deepak",
  "<EMAIL>",
  "Bhavesh Sove",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "Rohit_Bhati",
  "Saurabhsawant123",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "Nagesh98@",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "jakarta90",
  "Harsh@01",
  "<EMAIL>",
  "Ravi2001",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "Saksham",
  "Aditya Maltare",
  "MENTALIST@2526",
  "CoderX",
  "<EMAIL>",
];
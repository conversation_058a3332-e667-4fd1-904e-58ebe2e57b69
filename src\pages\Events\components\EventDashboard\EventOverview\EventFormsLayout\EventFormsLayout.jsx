import { useEffect, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import { useParams } from "react-router-dom";
import Combobox from "react-widgets/Combobox";

import { getOptions } from "../../../../../../components/utils";
import { getEventFormFields, orderEventFormField } from "../../../../actions";
import EventRegFormPreview from "./EventRegFormPreview";
import { formTypeOptions } from "./helper";
import EventCustomFormDrawer from "./EventCustomFormDrawer";

const EventFormsLayout = () => {
  const dispatch = useDispatch();
  const { id } = useParams();

  const { eventFormFields } = useSelector(({ event }) => event) || {};

  const [open, setOpen] = useState(false);
  const [formName, setFormName] = useState("registration");
  const [isEdit, setEdit] = useState(false);
  const [tempEditField, setTempEditField] = useState();

  useEffect(() => {
    dispatch(getEventFormFields({ eventId: id, formName }));
  }, [formName, dispatch]);

  const handelFormFieldOrder = (index, signal) => {
    let tempData = eventFormFields;
    let temp = tempData[index];
    tempData[index] = tempData[index - signal];
    tempData[index - signal] = temp;
    // We need to manage order according to event form type
    dispatch(orderEventFormField(tempData, id, formName));
  };

  const showDrawer = () => {
    setOpen(true);
  };
  
  const onClose = () => {
    setOpen(false);
  };

  const handleFieldEdit = (field) => {
    setEdit(true);
    setTempEditField(field);
    showDrawer();
  };

  return (
    <>
      <div className='event-form-layout p-3'>
        <h1>Event Forms Layout</h1>
        <div className='rounded-lg border bg-white shadow m-5 pb-5'>
          <div className='row mx-0'>
            <div className='col-12 col-md-8'>
              <h3 className=' mt-3 text-center'>Select Form Type</h3>
              <div className='form-group row justify-content-center mt-4 '>
                <Combobox
                  className='col-6'
                  data={getOptions(formTypeOptions, "formName")}
                  dataKey={"value"}
                  textField='formName'
                  placeholder={"Select Form Type"}
                  value={formName}
                  onChange={(v) => setFormName(v.value)}
                />
                <div>
                  <EventCustomFormDrawer
                    tempEditField={tempEditField}
                    isEdit={isEdit}
                    setEdit={setEdit}
                    formName={formName}
                    showDrawer={showDrawer}
                    onClose={onClose}
                    open={open}
                  />
                </div>
              </div>
            </div>
          </div>
          <div className='form-page row mx-0 my-4'>
            <div className=' col-8'>
              {eventFormFields?.length > 0 && (
                <EventRegFormPreview
                  handleFieldEdit={handleFieldEdit}
                  formName={formName}
                  eventFormFields={eventFormFields}
                  handelFormFieldOrder={handelFormFieldOrder}
                  showDrawer={showDrawer}
                />
              )}
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default EventFormsLayout;

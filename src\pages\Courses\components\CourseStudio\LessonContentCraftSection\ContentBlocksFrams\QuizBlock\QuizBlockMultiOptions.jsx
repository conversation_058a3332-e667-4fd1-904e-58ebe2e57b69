import React, { useEffect } from "react";
import { Field, getFormValues, reduxForm } from "redux-form";
import { connect, useDispatch } from "react-redux";

import { required } from "../../../../../../../components/utils/validators";
import {
  renderCheckboxField,
  renderInputField,
} from "../../../../../../../components/sharedComponents/ReduxFormFields";
import ContentBlockLayout from "../ContentBlockLayout";
import { editContent } from "../../../../../actions";

const QuizBlockMultiOptions = ({
  handleSubmit,
  formValues,
  item,
  initialize,
}) => {
  const dispatch = useDispatch();

  useEffect(() => {
    initialize(item.payload);
  }, []);
  
  const onSubmit = (value) => {
    const data = {
      id: item._id,
      payload: value,
    };
    dispatch(editContent(data));
  };

  const optionsCheckBoxRow = () => {
    var row = [];
    for (let i = 1; i <= 4; i++) {
      row.push(
        // <div className={ `p-3 my-2 ${((formValues && formValues[ `option${i}` ])) ? 'border border-success' : 'border'}` }>
        <div className="my-2">
          <div className="row mx-0 quiz-challenge-option type-checkbox d-flex align-items-center">
            <div className="col-1 px-0 d-flex justify-content-center align-items-center">
              <Field
                name={`option${i}`}
                component={renderCheckboxField}
                label={i}
              />
            </div>
            <div className="col-11 px-0 option-text d-flex align-items-center">
              <Field
                type="text"
                name={`text${i}`}
                placeholder={`Options ${i}`}
                component={renderInputField}
                validate={[required]}
                inputClass="mt-0"
              />
            </div>
          </div>
        </div>
      );
    }
    return row;
  };

  const headerHtml = () => {
    return (
      <>
        <div className="embeds-block-header d-flex justify-content-between">
          <div>
            <div className="d-flex embeds-block-title">
              <i className="fas fa-question" />
              <p className="mb-0">Multi Option Quiz</p>
            </div>
            <p className="mb-0">
              Type your question and select the correct answer(s).
            </p>
          </div>
        </div>
      </>
    );
  };

  return (
    <>
      <ContentBlockLayout
        item={item}
        blockHeader={headerHtml()}
        previewVisibility={true}
      >
        <form onSubmit={handleSubmit(onSubmit)}>
          <Field
            type="text"
            name="question"
            label="Question"
            placeholder="Type your Question"
            component={renderInputField}
            validate={[required]}
          />
          {optionsCheckBoxRow()}
          <div className="d-flex justify-content-end">
            <button type="submit" className="btn btn-primary">
              Save
            </button>
          </div>
        </form>
      </ContentBlockLayout>
    </>
  );
};

export default connect((state) => ({
  formValues: getFormValues()(state),
}))(reduxForm()(QuizBlockMultiOptions));

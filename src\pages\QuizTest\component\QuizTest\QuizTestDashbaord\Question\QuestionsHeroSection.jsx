import { useSelector } from "react-redux";
import QuestionRowCard from "./QuestionRowCard";

const QuestionHeroSection = () => {
  const { quizTestsQuestions } = useSelector((state) => state.quizTest);
  return (
    <>
      <div className='row mx-0 questions-list'>
        <div className='col-lg-12 col-sm-6 p-0 pt-3 d-flex justify-content-between align-items-center'>
          <div className='d-flex align-items-center'>
            <input type='checkbox' className='mr-2' />
            <h5 className='mr-2 mb-0'>Multiple Choice Questions</h5>
            <span>
              Total score (
              {quizTestsQuestions?.reduce(
                (acc, q) => acc + (parseFloat(q.maxScore) || 0),
                0,
              )}
              )
            </span>
          </div>
          <div className='d-flex align-items-center'>
            <a href='#' className='mr-2'>
              <i className='bi bi-filter-left'></i>
            </a>
            <a href='#' className='mr-2'>
              <i className='bi bi-plus'></i>
            </a>
            <a href='#'>
              <i className='bi bi-three-dots mx-3'></i>
            </a>
          </div>
        </div>
        <div className='col-lg-12 py-4 px-0'>
          {quizTestsQuestions?.map((item, index) => (
            <QuestionRowCard key={index} item={item} index={index} />
          ))}
        </div>
      </div>
    </>
  );
};

export default QuestionHeroSection;

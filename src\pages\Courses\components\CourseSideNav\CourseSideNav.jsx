import React from "react";
import { Link, useLocation } from "react-router-dom";
import { Fa<PERSON><PERSON><PERSON><PERSON>, FaBookOpen } from "react-icons/fa6";
import { ImHome2 } from "react-icons/im";
import { FaUsersCog, FaUsers } from "react-icons/fa";
import { TbUsersPlus } from "react-icons/tb";

import ToolTip from "../../../../components/sharedComponents/ToolTip";

const CourseSideNav = () => {
  const location = useLocation();

  return (
    <>
      <div className="row mx-0">
        <div className="col-12 px-0">
          <div className="side-nav-header">
            <FaBoxOpen />

            <span className="mb-0 ml-2">Course Studio</span>
          </div>
          <div className="side-nav-menu">
            <ul>
              <Link to="/course">
                <ToolTip title="Dashboard" placement="right">
                  <li
                    className={`${
                      location.pathname === "/course" ? "active-tab" : ""
                    }`}
                  >
                    <ImHome2 className="mr-2" />
                    <span>Dashboard</span>
                  </li>
                </ToolTip>
              </Link>
              <Link to="/courses/list">
                <ToolTip title="Create courses" placement="right">
                  <li
                    className={`${
                      location.pathname === "/courses/list" ? "active-tab" : ""
                    }`}
                  >
                    <FaBookOpen className="mr-2" />
                    <span>Courses</span>
                  </li>
                </ToolTip>
              </Link>
              <Link to="/course/students">
                <ToolTip title="Course Students" placement="right">
                  <li
                    className={`${
                      location.pathname === "/course/students"
                        ? "active-tab"
                        : ""
                    }`}
                  >
                    <FaUsers className="mr-2" />
                    <span>Students</span>
                  </li>
                </ToolTip>
              </Link>
              <Link to="/course/enrolls">
                <ToolTip title="Course Enrollments" placement="right">
                  <li
                    className={`${
                      location.pathname === "/course/enrolls"
                        ? "active-tab"
                        : ""
                    }`}
                  >
                    <TbUsersPlus className="mr-2" />
                    <span>Enrollments</span>
                  </li>
                </ToolTip>
              </Link>
              <Link to="/course/settings">
                <ToolTip title="Course Settings" placement="right">
                  <li
                    className={`${
                      location.pathname === "/course/settings"
                        ? "active-tab"
                        : ""
                    }`}
                  >
                    <FaUsersCog className="mr-2" />
                    <span>Settings</span>
                  </li>
                </ToolTip>
              </Link>
            </ul>
          </div>
        </div>
      </div>
    </>
  );
};

export default CourseSideNav;

import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Header } from "reactstrap";
import QuizInviteForm from "./QuizInviteForm";
import { RxCross1 } from "react-icons/rx";

const QuizInviteModal = ({ toggle, isopen, type, formData }) => {
  const closeBtn = (
    <button className="close" onClick={toggle}>
      <RxCross1 />
    </button>
  );
  return (
    <>
      <Modal isOpen={isopen} toggle={toggle}>
        <ModalHeader toggle={toggle} close={closeBtn}>
          Invite Candidate
        </ModalHeader>
        <ModalBody>
          <QuizInviteForm toggle={toggle} type={type} formData={formData} />
        </ModalBody>
      </Modal>
    </>
  );
};

export default QuizInviteModal;

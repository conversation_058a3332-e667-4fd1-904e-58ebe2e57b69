import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from "antd";
import { isMobile } from "react-device-detect";

import ToolTip from "../../../../../../components/sharedComponents/ToolTip";
import EventSponsorFilter from "./EventSponsorFilter";

const AddEventSponsorDrawer = () => {
  
  const [open, setOpen] = useState(false);
  const [placement, setPlacement] = useState("right");
  const showDrawer = () => {
    setOpen(true);
  };
  const onClose = () => {
    setOpen(false);
  };
  // const onChange = (e) => {
  //   setPlacement(e.target.value);
  // };
  return (
    <>
      <Space>
        <ToolTip title="Add Sponser" placement="bottom">
          <Button
            className="add-sponsor-btn "
            type="primary"
            onClick={showDrawer}
          >
            Add New Sponsor
          </Button>
        </ToolTip>
      </Space>
      <Drawer
        title="Select Datacode Speaker"
        placement={isMobile ? "bottom" : placement}
        closable={false}
        onClose={onClose}
        open={open}
        key={placement}
      >
        <EventSponsorFilter />
      </Drawer>
    </>
  );
};
export default AddEventSponsorDrawer;

import PropTypes from 'prop-types'
const ImageFrame = ({
  imgUrl = 'https://res.cloudinary.com/datacode/image/upload/v1664786288/xs4idyfyatoqbsuvfoni.png'
}) => {
  return (
    <>
      <div className="d-flex justify-content-center">
        <img
          className="border rounded-circle header-profile-img pic"
          height="140"
          width="140"
          loading="lazy"
          src={imgUrl ? imgUrl : 'https://res.cloudinary.com/datacode/image/upload/v1664786288/xs4idyfyatoqbsuvfoni.png'}
          alt="avatar"
        />
      </div>
    </>
  )
}

ImageFrame.propTypes = {
  imgUrl: PropTypes.string,
};

export default ImageFrame
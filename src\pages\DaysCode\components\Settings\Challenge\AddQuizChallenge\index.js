import { connect } from "react-redux";
import { getFormValues } from "redux-form";

import {
  createQuizChallenge,
  editQuizChallenge,
  getQuizChallengeDetails,
} from "../../../../actions";

import AddQuizChallenge from "./AddQuizChallenge";

const mapStateToProps = (state) => ({
  currentUser: state.auth.currentUser ? state.auth.currentUser : {},
  problemDetailsLoading: state.dayscode.problemDetailsLoading,
  quizChallengeDetails: state.dayscode.quizChallengeDetails,
  formStates: getFormValues("create-quiz-challenge")(state),
});

const mapDispatchToProps = {
  createQuizChallenge,
  editQuizChallenge,
  getQuizChallengeDetails,
};

export default connect(mapStateToProps, mapDispatchToProps)(AddQuizChallenge);

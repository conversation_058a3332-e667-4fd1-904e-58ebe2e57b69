import { FaEllipsisVertical } from "react-icons/fa6";
import { PiPencilSimpleLineLight } from "react-icons/pi";
import { PiTrashSimple } from "react-icons/pi";
import {
  DropdownItem,
  DropdownMenu,
  DropdownToggle,
  UncontrolledDropdown,
} from "reactstrap";

import ImageFrame from "../../../../components/sharedComponents/ImageFrame";
import ToolTip from "../../../../components/sharedComponents/ToolTip";

const SpeakersGridView = ({
  speakersList,
  handleDeleteSpeaker,
  handleEditSpeaker,
}) => {
  return (
    <>
      <div className='row mx-0'>
        {speakersList?.length ? (
          speakersList.map((speaker) => (
            <div key={speaker._id} className='col-12 col-md-4 p-3 custom-card'>
              <div className='nav-speaker-card'>
                <div className=''>
                  <UncontrolledDropdown
                    setActiveFromChild
                    className='event-host-action-toggle'
                  >
                    <DropdownToggle tag='span' className='card-action'>
                      <ToolTip title='Edit Speaker Details' placement='bottom'>
                        <FaEllipsisVertical className='icon' />
                      </ToolTip>
                    </DropdownToggle>
                    <DropdownMenu
                      className='dropdown-menu mt-3 card-schadow'
                      left='true'
                    >
                      <DropdownItem header>
                        <span>Actions</span>
                      </DropdownItem>
                      <DropdownItem
                        onClick={() => handleEditSpeaker(speaker._id)}
                      >
                        <span>
                          <PiPencilSimpleLineLight className='icon' />
                          Edit Speaker
                        </span>
                      </DropdownItem>
                      <DropdownItem
                        onClick={() => handleDeleteSpeaker(speaker._id)}
                      >
                        <span>
                          <PiTrashSimple className='icon' />
                          Delete Speaker
                        </span>
                      </DropdownItem>
                    </DropdownMenu>
                  </UncontrolledDropdown>
                </div>
                <div className='d-flex justify-content-center'>
                  <ImageFrame imgUrl={speaker?.imgUrl} />
                </div>
                <div className=''>Name: {speaker.name}</div>
                <div className=''>Email: {speaker.email}</div>
                <div className=''>Phone: {speaker.phone}</div>
                <div className=''>Universal: {speaker.university}</div>
                <div className='.speaker-user-info'>Bio: {speaker.bio}</div>
              </div>
            </div>
          ))
        ) : (
          <span> No Speaker Availabel </span>
        )}
      </div>
    </>
  );
};
export default SpeakersGridView;

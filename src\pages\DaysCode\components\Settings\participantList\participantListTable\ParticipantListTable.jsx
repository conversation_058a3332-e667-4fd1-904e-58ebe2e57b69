import BootstrapTable from "react-bootstrap-table-next";

import { getColumns } from "./helpers";
import "./styles.css";

const ParticipantListTable = ({
  participants,
  editAction,
  participantDeleteAction,
  toggleMentorAssignModal,
}) => {
  let columns = getColumns(
    editAction,
    participantDeleteAction,
    toggleMentorAssignModal,
  );

  return (
    <>
      {participants && participants.length && (
        <div className='table table-responsive'>
          <BootstrapTable
            keyField='id'
            data={participants}
            columns={columns}
            search={{
              search: true,
              searchPosition: "top",
              delay: 500,
            }}
            bordered={true}
            hover
            condensed
            headerClasses='header-class'
            rowClasses='row-class'
          />
        </div>
      )}
    </>
  );
};

export default ParticipantListTable;

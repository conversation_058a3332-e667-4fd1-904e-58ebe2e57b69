import React from "react";
import { Line } from "react-chartjs-2";
import { Chart as ChartJS } from "chart.js/auto";

const EventRegistrationChart = ({ dayData }) => {
  const labels = dayData.map((item) => item.date);
  const registrationCounts = dayData.map((item) => item.count);

  const data = {
    labels: labels,
    datasets: [
      {
        label: "No. of Registration",
        data: registrationCounts,
        fill: false,
        borderColor: "rgb(75, 192, 192)",
        tension: 0.1,
      },
    ],
  };
  const options = {
    responsive: true,
    maintainAspectRatio: true,
  };
  //   const config = {
  //     type: "line",
  //     data: data,
  //   };

  return (
    <div>
      <Line data={data} options={options} />
    </div>
  );
};

export default EventRegistrationChart;

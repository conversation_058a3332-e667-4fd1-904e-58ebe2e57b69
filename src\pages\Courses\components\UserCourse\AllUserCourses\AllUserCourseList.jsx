import React, { useState } from "react";
import { <PERSON><PERSON><PERSON><PERSON>hin, PiArrowCircleDownLeft } from "react-icons/pi";
import { GoClock } from "react-icons/go";
import { CiLock } from "react-icons/ci";
import { RiCheckboxCircleLine } from "react-icons/ri";
import { Progress } from "reactstrap";
import { Link } from "react-router-dom";

const AllUserCourseList = ({ coursesList, userCourseList }) => {
  const [buttonName, setButtonName] = useState("Enroll Course");

  const getCourseDurationsFromLesson = (course) => {
    let totalDuration = 0;
    course.sections.forEach((section) => {
      section.lessons.forEach((lesson) => {
        const lessonDuration = lesson.duration;
        totalDuration += parseInt(lessonDuration, 10);
      });
    });
    return totalDuration;
  };

  if (userCourseList === true) {
    return (
      <>
        <div className="container-fluid">
          <div className=" row mx-0 course-list">
            {coursesList &&
              coursesList.map((course) => {
                const totalCourseTime = getCourseDurationsFromLesson(course);
                const hours = Math.floor(totalCourseTime / 60);
                const remainingMinutes = totalCourseTime % 60;
                const totalCourseformattedTime = `${hours} hr${
                  hours !== 1 ? "s" : ""
                } , ${remainingMinutes} min${
                  remainingMinutes !== 1 ? "s" : ""
                }`;
                return (
                  <div
                    className="col-12 col-lg-4 col-md-4 col-sm-12 d-flex justify-content-center px-0  limited-height"
                    key={course._id}
                  >
                    <div className="list">
                      <div className="icon">
                        {course?.progress === 100 ? (
                          <PiArrowCircleDownLeft className="goal-icon" />
                        ) : course?.progress > 0 ? (
                          <PiFlagThin className="goal-icon" />
                        ) : (
                          <PiFlagThin className="goal-icon" />
                        )}
                      </div>

                      <Progress
                        className="my-0 "
                        style={{
                          height: "6px",
                        }}
                        value={course?.progress}
                      />

                      <div className="detail">
                        <small>type</small>
                        <h5 className="fs-5 mt-2">{course?.name}</h5>
                        <Link to={`/course/${course._id}`}>
                          <p className="fs-6 new-text-color ">
                            {course.description &&
                            course.description.length > 50
                              ? course.description.slice(0, 50) + " ..."
                              : course.description}{" "}
                          </p>
                        </Link>

                        <small>
                          <GoClock />
                          {totalCourseTime
                            ? totalCourseformattedTime
                            : "Time duration not mentioned"}
                        </small>
                      </div>
                      <Link to={`/courses/preview/${course._id}`}>
                        <div className="button">
                          <button>
                            <h6>
                              {course?.progress === 100 ? (
                                <>
                                  <RiCheckboxCircleLine className="tickMark" />
                                  Course Completed
                                </>
                              ) : course?.progress > 0 ? (
                                <>
                                  <GoClock className="tickMark" />
                                  Resume Course
                                </>
                              ) : (
                                <>
                                  <CiLock className="tickMark" />
                                  Start Course
                                </>
                              )}
                            </h6>
                          </button>
                        </div>
                      </Link>
                    </div>
                  </div>
                );
              })}
          </div>
        </div>
      </>
    );
  }

  return (
    <>
      <div className="container-fluid">
        <div className="course-list">
          {coursesList &&
            coursesList.map((course) => {
              const totalCourseTime = getCourseDurationsFromLesson(course);
              const hours = Math.floor(totalCourseTime / 60);
              const remainingMinutes = totalCourseTime % 60;
              const totalCourseformattedTime = `${hours} hr${
                hours !== 1 ? "s" : ""
              } , ${remainingMinutes} min${remainingMinutes !== 1 ? "s" : ""}`;
              return (
                <div
                  className="col-12 col-lg-4 col-md-4 col-sm-12 d-flex justify-content-center px-0 limited-height"
                  key={course._id}
                >
                  <div className="list">
                    <div className="icon">
                      <PiFlagThin className="goal-icon" />
                    </div>
                    <Progress
                      className="my-0 "
                      style={{
                        height: "6px",
                      }}
                      value={0}
                    />
                    <div className="detail">
                      <small>type</small>
                      <h5 className="fs-5 mt-2">{course.name}</h5>
                      <Link to={`/course/${course._id}`}>
                        <p className="fs-6 new-text-color ">
                          {course.description && course.description.length > 50
                            ? course.description.slice(0, 50) + "..."
                            : course.description}{" "}
                        </p>
                      </Link>
                      <small>
                        <GoClock />{" "}
                        {totalCourseTime
                          ? totalCourseformattedTime
                          : "Time duration not mentioned"}
                      </small>
                    </div>
                    <Link to={`/course/${course._id}`}>
                      <div className="button">
                        <button>
                          <h6>
                            <CiLock className="tickMark" />
                            {buttonName}{" "}
                          </h6>
                        </button>
                      </div>
                    </Link>
                  </div>
                </div>
              );
            })}
          ;
        </div>
      </div>
    </>
  );
};

export default AllUserCourseList;
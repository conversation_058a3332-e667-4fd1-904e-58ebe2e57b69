import React, { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { <PERSON><PERSON>, Drawer, Space } from "antd";
import { Combobox } from "react-widgets";

import { addLessonIntoBatch, getLessons } from "../../../actions";
import { getTopicOptions } from "../../utils";
import { DSAContent } from "../../Constants/helper";
import ProblemListCard from "../Problems/ProblemsList/ProblemListCard";

const AddBatchLessonDrawer = ({
  batch,
  open,
  setOpen,
  showDrawer,
  setOpenAddDay,
  openAddDay,
  onClose,
  tempDay,
}) => {

  const dispatch = useDispatch();

  const { lessonsList } = useSelector((state) => state.dayscode) || {};
  
  const [placement, setPlacement] = useState("right");
  const [topic, setTopic] = useState();

  const handleLessonTopic = (topic) => {
    setTopic(topic.name);
    dispatch(getLessons({ status: "public", topic: topic.name }));
  };

  const handleAddLessonIntoBatch = (data) => {
    dispatch(
      addLessonIntoBatch({
        batch: data.batch,
        lessonDetails: data.id,
        day: data.day,
        status: data.status,
      }),
    );
  };

  return (
    <>
      <Space>
        <Button type='primary' onClick={showDrawer}>
          Add Lesson
        </Button>
      </Space>
      <Drawer
        title='Add Lesson Into Batch'
        placement={placement}
        setPlacement={setPlacement}
        closable={false}
        onClose={onClose}
        open={open}
        key={placement}
      >
        <h1>Add Lesson</h1>
        <Combobox
          data={getTopicOptions(DSAContent)}
          dataKey={"value"}
          textField='name'
          placeholder={"Select Topic Name"}
          value={topic}
          onChange={(value) => handleLessonTopic(value)}
        />
        {lessonsList &&
          lessonsList.map((lesson, key) => {debugger;
            return(
            // Resuable component for Quiz and Problem and Lesson
            <div key={key}>
              <ProblemListCard
                handleAddIntoBatch={handleAddLessonIntoBatch}
                showAddBatchIcon={true}
                item={lesson}
                openAddDay={openAddDay}
                setOpenAddDay={setOpenAddDay}
                type={"lesson"}
                batch={batch}
                tempDay={tempDay}
              />
            </div>
          )})}
      </Drawer>
    </>
  );
};

export default AddBatchLessonDrawer;

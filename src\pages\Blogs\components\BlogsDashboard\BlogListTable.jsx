import React from 'react'
import BootstrapTable from 'react-bootstrap-table-next';

import { getColumns } from './helpers';
import './style.css';

const BlogListTable = ({lessons ,items ,handleApprove ,selectedBLog}) => {
 
  let columns = getColumns(items ,handleApprove,selectedBLog);

  return (
    <>
      {lessons &&
        <div className="table table-responsive">
          <BootstrapTable
            keyField='id'
            data={lessons}
            columns={columns}
            search={{
              search: true,
              searchPosition: "top",
              delay: 500,
            }}
            bordered={false}
            hover
            condensed
            headerClasses="header-class"
            rowClasses="row-class"
          />
        </div>
      }
    </>
  )
}

export default BlogListTable

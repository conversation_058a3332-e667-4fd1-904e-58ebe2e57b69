import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { FaPlus, FaSearch } from "react-icons/fa";

import { getQuizTestsList } from "../../../../actions/operations.js";
import CustomLoader from "../../../../../../components/sharedComponents/CustomLoader.jsx";
import CreateQuizModal from "../create_quiz_header/CreateQuizModal.js";
import SideNavbar_Drawer from "../../QuizTestDashbaord/Question/Drawers/SideNavbar_Drawer.jsx";
import QuizTestCard from "../../../ui/QuizTestCard.jsx";

const QuizTestsList = () => {
  
  const dispatch = useDispatch();
 

  const { quizTestsList, quizTestsListLoading, deleteLoading } = useSelector(
    (state) => state.quizTest,
  );
  const [openModal, setOpenModal] = useState(false);
  const [quizTestCategory, setQuizTestCategory] = useState("");

  useEffect(() => {
    dispatch(getQuizTestsList({ category: quizTestCategory }));
  }, [quizTestCategory]);

  const toggleModal = () => {
    setOpenModal(!openModal);
  };

  return (
    <div className='container-fluid quiz-list'>
      <div className='row'>
        <div className='col-lg-2 d-none d-lg-block p-0'>
          <SideNavbar_Drawer setQuizTestCategory={setQuizTestCategory} />
        </div>

        <div className='col-lg-10 col-md-12 quiz-list-body px-0'>
          <div className='row mx-0  d-flex justify-content-between align-items-center quiz-list-header'>
            <div className='col-md-8 col-12 quiz-list-search'>
              <div className='search-container'>
                <input
                  type='text'
                  name='search'
                  placeholder='Search by tests name or tags'
                  className='form-control'
                />
                <span className='search-icon'>
                  <FaSearch />
                </span>
              </div>
            </div>

            <div className='col-md-4 text-md-end text-center mt-3 mt-md-0'>
              <button
                className='btn btn-primary create-btn'
                onClick={toggleModal}
              >
                <FaPlus className='me-2' /> Create New Test
              </button>
            </div>
          </div>

          <div className='quiz-list-content'>
            {quizTestsListLoading || deleteLoading ? (
              <CustomLoader />
            ) : quizTestsList && quizTestsList.length > 0 ? (
              quizTestsList.map((item, index) => (
                <div className='row mx-0 py-3' key={index}>
                  <QuizTestCard item={item} index={index} />
                </div>
              ))
            ) : (
              <p className='text-center mt-4'>No quizzes found.</p>
            )}
          </div>
        </div>
      </div>

      <CreateQuizModal open={openModal} toggle={toggleModal} />
    </div>
  );
};

export default QuizTestsList;

import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";

import ProblemListCard from "../Problems/ProblemsList/ProblemListCard";
import {
  deleteQuizFromBatch,
  editQuizIntoBatch,
  getBatchQuiz,
} from "../../../actions";
import CustomLoader from "../../../../../components/sharedComponents/CustomLoader";
import BatchContentHeader from "./BatchContentHeader";
import NoDataBlock from "../../../../../components/sharedComponents/NoDataBlock";
import AddBatchChallengeDrawer from "./AddBatchChallengeDrawer";

const QuizList = ({
  batch,
  codeBatchDetails,
  codeBatchesLoading,
  setActiveTab,
}) => {
  const dispatch = useDispatch();
  const { batchQuizChallengesList, problemsListLoading } = useSelector(
    ({ dayscode }) => dayscode
  );

  const [QuizStatus, setQuizStatus] = useState(null);
  const [open, setOpen] = useState(false);
  const [openAddDay, setOpenAddDay] = useState("");

  useEffect(() => {
    dispatch(
      getBatchQuiz({
        id: codeBatchDetails._id,
        status: QuizStatus,
      })
    );
  }, [QuizStatus, dispatch, codeBatchDetails._id]);

  const showDrawer = () => {
    setOpen(true);
  };
  const onClose = () => {
    setOpen(false);
    setOpenAddDay("");
  };

  const handleDeleteQuizFromBatch = (quiz) => {
    dispatch(deleteQuizFromBatch({ batch: batch, quiz }));
  };

  const handleEditQuizIntoBatch = (contentData) => {
    dispatch(editQuizIntoBatch(contentData));
  };

  return (
    <>
      <BatchContentHeader
        type={"quiz"}
        batch={batch}
        setBatchContentStatus={setQuizStatus}
        setActiveTab={setActiveTab}
        open={open}
        setOpen={setOpen}
        showDrawer={showDrawer}
        setOpenAddDay={setOpenAddDay}
        openAddDay={openAddDay}
        onClose={onClose}
      />
      {problemsListLoading ? (
        <div className="row d-flex justify-items-center">                                                                                                                                         
          <div className="col-12 align-items-center text-center ">
            <CustomLoader />
          </div>
        </div>
      ) : (
        <div className="row mx-4 mt-3 border-top">
          {batchQuizChallengesList && batchQuizChallengesList.length > 0 ? (
            batchQuizChallengesList.map((quiz, key) => (
              <div className="col-md-4 col-12 p-2" key={key}>
                <ProblemListCard
                  batch={batch}
                  day={quiz?.day}
                  batchContent={quiz}
                  item={quiz?.quizDetails}
                  showDeleteFromBatchIcon={true}
                  handleDeleteFromBatch={handleDeleteQuizFromBatch}
                  handleAddIntoBatch={handleEditQuizIntoBatch}
                  type={"quiz"}
                />
              </div>
            ))
          ) : (
            <div className="w-100 h-100">
              <NoDataBlock
                route={
                  <AddBatchChallengeDrawer
                    batch={batch}
                    open={open}
                    setOpen={setOpen}
                    showDrawer={showDrawer}
                    setOpenAddDay={setOpenAddDay}
                    openAddDay={openAddDay}
                    onClose={onClose}
                  />
                }
              />
            </div>
          )}
        </div>
      )}
    </>
  );
};

export default QuizList;

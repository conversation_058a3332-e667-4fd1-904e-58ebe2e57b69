@import "_variables";
@import "QuestionEditMainSection.scss";

.questions-header {
  .custom-btn {
    background-color: #673de6;
    color: white;
    border: none;
    padding: 8px 16px;
    font-size: 14px;
    border-radius: 4px;
    // margin: 0 8px;
    transition: background-color 0.3s ease, transform 0.3s ease;

    &:hover {
      background-color: darken(#673de6, 10%);
      transform: translateY(-2px);
    }

    &.btn-outline-primary {
      background-color: transparent;
      color: #673de6;
      border: 1px solid #673de6;

      &:hover {
        background-color: #673de6;
        color: white;
      }
    }
  }
}
.question-page {
  min-height: 100vh;
  background-color: $background-color;
  .questions-list {
    span {
      color: grey;
    }
    a {
      color: $greyDark;
      text-decoration: none;
      &:hover {
        color: grey;
      }
    }
    .question-card {
      border: 1px solid #ccc;
      border-radius: 8px;
      background-color: #fff;
      padding: 1rem;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
      margin-bottom: 1rem;

      &:hover {
        box-shadow: 0 8px 12px rgba(0, 0, 0, 0.1);
      }

      .level-badge {
        padding: 0px 16px;
        font-size: 14px;
        font-weight: bold;
        border-radius: 5px;
        color: #fff;
        text-transform: capitalize;

        &.level-easy {
          color: #28a745;
          border: 2px solid #28a745;
          border-radius: 5px;
        }

        &.level-medium {
          color: #ffc107;
          border: 2px solid #ffc107;
          border-radius: 5px;
        }

        &.level-hard {
          color: #dc3545;
          border: 2px solid #dc3545;
          border-radius: 5px;
        }

        &.level-default {
          color: #6c757d;
          border: 2px solid #6c757d;
          border-radius: 5px;
        }
      }

      .score-badge {
        background-color: #757575;
        color: #fff;
        padding: 8px 16px;
        font-size: 14px;
        font-weight: bold;
        border-radius: 5px;
        text-transform: uppercase;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        transition: background-color 0.3s ease;

        &:hover {
          background-color: #343442;
        }
      }
    }
  }
}
.q-type-sidebar {
  span {
    color: $grey;
    padding: 2px;
    font-size: 16px;
  }
}
.question-view {
  background-color: $background-color;
  text-align: left;
  overflow: hidden;
  .question-view-header {
    background-color: $lightcolor;
    box-shadow: 0 4px 6px -4px rgba(0, 0, 0, 0.1);
    border: none;
    z-index: 1;
    .buttons {
      text-align: right;
    }
    button {
      border: 1px solid $sidebarlinkcolor;
      border-radius: 5px;
      padding: 4px 15px;
      margin: 10px;
    }
    .edit-que-btn {
      color: $lightcolor;
      background-color: #673de6;
      border: none;
      &:hover {
        background-color: #6c5ce7cc;
      }
    }
    .try-que-btn {
      background-color: #737980;
      color: $lightcolor;
      &:hover {
        background-color: #434344cc;
      }
    }
  }
  .hero-section {
    background-color: $lightcolor;
    border-right: 1px solid $border-color;
    text-align: left;
    min-height: 73vh;
    margin-right: 15px;
    li {
      padding: 4px;
    }
    .correct-field {
      background-color: rgba(132, 226, 132, 0.5);
      color: green;
      padding-left: 3px;
      width: 100px;
      margin: 5px;
    }
  }
  .side-bar {
    background-color: #fff;
    padding: 20px;
    color: #495057;
    height: 100%;

    .difficulty-level {
      margin-bottom: 20px;
      h4 {
        font-size: 1.25rem;
        color: #343a40;
      }
      .difficulty-indicator {
        display: inline-block;
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 1rem;
        font-weight: bold;
        color: white;
        text-transform: capitalize;
      }
    }

    .score-section {
      .score-item {
        // margin-bottom: 15px;

        h5 {
          font-size: 1rem;
          color: #495057;
        }

        p {
          font-size: 1.125rem;
          color: #6c757d;
        }

        .text-center {
          text-align: center;
        }
      }
    }

    .tags-section {
      margin-top: 10px;
      h5 {
        font-size: 1.125rem;
        color: #343a40;
      }
      span {
        font-size: 1rem;
        color: #6c757d;
      }
    }

    .bi-exclamation-circle {
      color: #673de6;
      font-size: 1rem;
    }
  }

  @media (max-width: 992px) {
    .side-bar {
      padding: 15px;
    }

    .difficulty-level {
      margin-bottom: 15px;
    }

    .score-item {
      margin-bottom: 10px;
    }
  }
}
.question-edit {
  background-color: $background-color;
  .question-edit-header {
    background-color: $lightcolor;
    box-shadow: 0 4px 6px -4px rgba(0, 0, 0, 0.1);
    border-bottom: 1px solid $border-color;
    z-index: 1;
  }
  .edit-footer {
    background-color: $lightcolor;
    border: 1px solid $border-color;
    button {
      margin: 3px 7px;
      padding: 3px 8px;
      font-size: 14px;
      border-radius: 5px;
    }
    .publish-btn {
      color: $lightcolor;
      background-color: $sidebarlinkcolor;
      border: 1px solid $sidebarlinkcolor;
    }
    .discard-btn {
      border: 1px solid #fafa4d;
      background-color: #fafa4d;
      color: $greyDark;
    }
  }
}

.q-type-sidebar {
  background-color: #f8f9fa;
  height: 100%;
  overflow-y: auto;

  h4 {
    font-size: 1.25rem;
    font-weight: bold;
    color: #343a40;
  }

  h5 {
    font-size: 1rem;
    color: #35383b;
    font-weight: 800;
    margin-right: 10px;
  }

  .bi-exclamation-circle {
    color: #6666d7;
    font-size: 1.2rem;
    cursor: pointer;
  }

  span {
    display: block;
    padding: 10px;
    font-size: 1rem;
    color: #495057;
    font-weight: 400;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 0.5rem;

    &:hover {
      background-color: #6666d7;
      color: white;
    }

    &:active {
      transform: scale(0.98);
    }
  }

  .btn {
    background-color: #6666d7;
    color: white;
    padding: 8px 16px;
    border-radius: 5px;
    cursor: pointer;
    text-align: center;
    transition: background-color 0.3s;

    &:hover {
      background-color: #6666d7;
    }
  }
}

@media (max-width: 768px) {
  .q-type-sidebar {
    padding: 1rem;
  }

  span {
    font-size: 0.9rem;
  }
}

// questions-header file code

@media (max-width: 600px) {
  .questions-header {
    font-size: $font-size;
  }
  .Question-overview-A {
    display: flex;
    flex-direction: row-reverse;
  }
  .questionViewDrawer {
    width: 100%;
  }
}

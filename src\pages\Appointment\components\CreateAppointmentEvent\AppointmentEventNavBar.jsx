import React from "react";

import ToolTip from "../../../../components/sharedComponents/ToolTip";

const AppointmentEventNavBar = ({ activeTab, setActiveTab }) => {
  return (
    <>
      <div className="row m-0">
        <div className="col-12">
          <div className="event-dashboard-side-nav w-100 px-0 table-responsive table">
            <ul>
              <ToolTip title="Edit meeting details" placement="right">
                <li>
                  <span
                    onClick={() => setActiveTab("settings")}
                    className={`text-nowrap ${
                      activeTab === "settings" ? "active" : ""
                    }`}
                  >
                    <i className="fal fa-sliders-v mr-2"></i>Settings
                  </span>
                </li>
              </ToolTip>
              <ToolTip title="Add new mentor" placement="right">
                <li>
                  <span
                    onClick={() => setActiveTab("addmentor")}
                    className={`text-nowrap ${
                      activeTab === "addmentor" ? "active" : ""
                    }`}
                  >
                    <i className="fal fa-sliders-v mr-2"></i>Add Mentor
                  </span>
                </li>
              </ToolTip>
              <ToolTip title="Availablle mentors" placement="right">
                <li>
                  <span
                    onClick={() => setActiveTab("availability")}
                    className={`text-nowrap ${
                      activeTab === "availability" ? "active" : ""
                    }`}
                  >
                    <i className="fal fa-sliders-v mr-2"></i>Availability
                  </span>
                </li>
              </ToolTip>
            </ul>
          </div>
        </div>
      </div>
    </>
  );
};

export default AppointmentEventNavBar;

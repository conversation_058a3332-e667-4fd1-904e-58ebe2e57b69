import Parse from "html-react-parser";
import { FaCheckCircle } from "react-icons/fa";

const QuestionViewHero = ({ quizQuestionDetails }) => {
  const renderChoice = (option, optionKey) => {
    return (
      <div className='d-flex align-items-center'>
        <li>{quizQuestionDetails?.[option]}</li>
        {quizQuestionDetails?.correct_answer === optionKey && (
          <span className='correct-field  rounded-lg'>
            {" "}
            <FaCheckCircle className='text-success mx-2' />
            Correct
          </span>
        )}
      </div>
    );
  };

  return (
    <>
      {quizQuestionDetails && (
        <div className='col-lg-12 p-4 text-left pb-sm-0 hero-section'>
          <h5 className='font-weight-bold'>Problem Statement</h5>
          <h5>
            {quizQuestionDetails?.title && Parse(quizQuestionDetails?.title)}
          </h5>
          <h5 className='font-weight-bold mt-3'>Choices</h5>
          <ul className='pl-3'>
            {renderChoice("option1", "option1")}
            {renderChoice("option2", "option2")}
            {renderChoice("option3", "option3")}
            {renderChoice("option4", "option4")}
          </ul>
        </div>
      )}
    </>
  );
};

export default QuestionViewHero;

import React, { useEffect } from "react";

import { CiSettings } from "react-icons/ci";
import { Dropdown, Space } from "antd";
import { LuClipboardCopy } from "react-icons/lu";
import { MdDeleteOutline } from "react-icons/md";

import githubImg from "./FileTypeImages/github.png";
import driveImg from "./FileTypeImages/drive.png";
import tutorialImg from "./FileTypeImages/tutorial.png";
import docxImg from "./FileTypeImages/docx.png";
import figmaImg from "./FileTypeImages/figma.png";

const LinkPreviewCard = ({
  resourceURL,
  resourceType,
  handleDeleteResource,
}) => {
  const urlImageType = [
    {
      type: "github",
      imageUrl: githubImg,
    },
    {
      type: "drive",
      imageUrl: driveImg,
    },
    {
      type: "tutorial",
      imageUrl: tutorialImg,
    },
    {
      type: "docx",
      imageUrl: docxImg,
    },
    {
      type: "figma",
      imageUrl: figmaImg,
    },
  ];

  const items = [
    {
      label: (
        <span
          onClick={() => {
            navigator.clipboard.writeText(resourceURL);
          }}
        >
          <LuClipboardCopy className='mr-1' />
          Copy
        </span>
      ),
      key: "0",
    },
    {
      label: (
        <span
          className='text-danger'
          onClick={() => {
            handleDeleteResource(resourceURL);
          }}
        >
          <MdDeleteOutline className='mr-1' />
          Delete
        </span>
      ),
      key: "1",
    },
  ];

  const imageUrl = urlImageType.find(
    (item) => item.type === resourceType,
  )?.imageUrl;

  //   useEffect(() => {
  //     console.log(resourceType, "resourcetype");
  //     console.log(resourceURL, "resourceURL");
  //   }, []);

  return (
    <div className='link-preview-card shadow'>
      <div className='link-preview-card-content'>
        <img src={imageUrl} alt={"image"} />
        <div className='mt-2'>
          <span>{resourceURL}</span>
          <p>{resourceType}</p>
        </div>
      </div>
      <span className='link-preview-card-setting'>
        <Dropdown
          menu={{
            items,
          }}
          trigger={["click"]}
        >
          <a onClick={(e) => e.preventDefault()}>
            <Space>
              <CiSettings />
            </Space>
          </a>
        </Dropdown>
      </span>
    </div>
  );
};

export default LinkPreviewCard;

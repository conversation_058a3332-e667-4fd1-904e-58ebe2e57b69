import React, { useMemo } from 'react';
import { Line } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
);

const LineGraph = ({ userData, timeRange }) => {

  // Helper function to format the timestamp into a date string
  const formatDate = (timestamp) => {
    const date = new Date(timestamp);
    return `${date.getDate()}'${date.toLocaleString('en', { month: 'short' })} '${date.getFullYear().toString().slice(-2)}`;
  };

  // Helper function to filter data based on the provided time range
  const filterDataByTimeRange = (data, range) => {
    const now = new Date();
    let startDate;

    switch (range) {
      case 'lastWeek':
        startDate = new Date(now.setDate(now.getDate() - 7));
        break;
      case 'lastTwoWeeks':
        startDate = new Date(now.setDate(now.getDate() - 14));
        break;
      case 'lastThreeWeeks':
        startDate = new Date(now.setDate(now.getDate() - 21));
        break;
      case 'lastMonth':
        startDate = new Date(now.setMonth(now.getMonth() - 1));
        break;
      default :
        startDate = new Date(now.setMonth(now.getMonth() - 1));
        break;
    }

    return data.filter(({ timestamp }) => new Date(timestamp) >= startDate);
  };

  const processedData = useMemo(() => {
    const filteredData = filterDataByTimeRange(userData, timeRange);

    const result = {};
    filteredData.forEach(({ user, timestamp }) => {
      const date = formatDate(timestamp);
      const userType = user ? 'Member' : 'Anonymous';

      if (!result[date]) {
        result[date] = { Member: 0, Anonymous: 0 };
      }

      result[date][userType] += 1;
    });

    return result;
  }, [userData, timeRange]);

  const labels = Object.keys(processedData);
  const memberData = labels.map(label => processedData[label].Member);
  const anonymousData = labels.map(label => processedData[label].Anonymous);

  const data = {
    labels,
    datasets: [
      {
        label: 'Member',
        data: memberData,
        borderColor: 'rgba(75, 192, 192, 1)',
        backgroundColor: 'rgba(75, 192, 192, 0.2)',
      },
      {
        label: 'Anonymous',
        data: anonymousData,
        borderColor: 'rgba(255, 99, 132, 1)',
        backgroundColor: 'rgba(255, 99, 132, 0.2)',
      },
    ]
  };

  const options = {
    responsive: true,
    plugins: {
      legend: {
        position: 'bottom',
      },
      title: {
        display: false,
      },
    },
  };

  return <Line data={data} options={options} height={80} />;
};

export default LineGraph;

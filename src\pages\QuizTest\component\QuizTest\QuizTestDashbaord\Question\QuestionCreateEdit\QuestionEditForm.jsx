import { useState } from "react";
import QuestionEditor from "./QuestionEditor";

import TextEditor from "../../../../../../../components/sharedComponents/TextEditor";

const QuestionEditForm = ({
  setQuestion,
  setChoices,
  setCorrectChoice,
  choices,
  question,
  correctChoice,
}) => {
  const handleChoiceChange = (index, value) => {
    const newChoices = [...choices];
    newChoices[index] = value;
    setChoices(newChoices);
  };

  const handleCorrectChoiceChange = (index) => {
    setCorrectChoice(`option${index + 1}`);
  };

  return (
    <div className='col-lg-12 p-3  border rounded-lg shadow-sm question-edit-form'>
      <form>
        <div className='row mx-0'>
          <h5>Problem Statement</h5>
          <div className='col-12 p-0 border'>
            {/* <QuestionEditor text={question} handleTextEditor={setQuestion} /> */}
            <TextEditor handleTextEditor={setQuestion} text={question} />
          </div>
        </div>
        <div className='row px-2'>
          <div className='col-12 py-3 px-0'>
            <h6 className='p-2 m-0'>
              Enter your choices and select the correct option by marking the
              corresponding checkbox.
            </h6>
          </div>

          <div className='col-12 px-2'>
            <div className='d-flex flex-column '>
              <div className='form-check d-flex align-items-center'>
                <input
                  className='form-check-input'
                  type='checkbox'
                  id='multipleAnswers'
                />
                <label
                  className='form-check-label ms-2'
                  htmlFor='multipleAnswers'
                >
                  Allow Multiple Answers
                </label>
              </div>
              <div className='form-check d-flex align-items-center'>
                <input
                  className='form-check-input'
                  type='checkbox'
                  id='partialScoring'
                />
                <label
                  className='form-check-label ms-2'
                  htmlFor='partialScoring'
                >
                  Allow Partial Scoring
                </label>
              </div>
              <div className='form-check d-flex align-items-center'>
                <input
                  className='form-check-input'
                  type='checkbox'
                  id='shuffleOptions'
                />
                <label
                  className='form-check-label ms-2'
                  htmlFor='shuffleOptions'
                >
                  Allow Shuffling Options
                </label>
              </div>
            </div>
          </div>

          <div className='col-12 d-flex justify-content-end p-2'>
            <div className='form-check form-switch d-flex align-items-center'>
              <input
                className='form-check-input'
                type='checkbox'
                role='switch'
                id='richTextSwitch'
              />
              <label className='form-check-label' htmlFor='richTextSwitch'>
                Rich Text
              </label>
            </div>
          </div>

          <div className='col-12'>
            {choices.map((choice, index) => (
              <div
                className='col-12 p-2 d-flex align-items-center gap-3 border rounded mb-2'
                key={index}
              >
                <input
                  type='radio'
                  name='correctChoice'
                  value={index}
                  checked={correctChoice === `option${index + 1}`}
                  onChange={() => handleCorrectChoiceChange(index)}
                  className='mx-2'
                />
                <input
                  type='text'
                  className='form-control'
                  name={`option${index + 1}`}
                  value={choice}
                  onChange={(e) => handleChoiceChange(index, e.target.value)}
                  placeholder={`Option ${index + 1}`}
                />
                <button className='btn btn-outline-danger mx-2' type='button'>
                  ✕
                </button>
              </div>
            ))}
          </div>
        </div>
      </form>
    </div>
  );
};

export default QuestionEditForm;

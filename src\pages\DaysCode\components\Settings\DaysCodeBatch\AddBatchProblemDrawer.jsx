import React, { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { <PERSON><PERSON>, Drawer, Space } from "antd";
import { Combobox } from "react-widgets";

import { addProblemIntoBatch, getProblems } from "../../../actions";
import ProblemListCard from "../Problems/ProblemsList/ProblemListCard";
import { getTopicOptions } from "../../utils";
import { DSAContent } from "../../Constants/helper";

const AddBatchProblemDrawer = ({
  batch,
  open,
  setOpen,
  showDrawer,
  setOpenAddDay,
  openAddDay,
  onClose,
  tempDay
}) => {
  
  const dispatch = useDispatch();
  
  const { problemsList } = useSelector((state) => state.dayscode) || {};

  const [placement, setPlacement] = useState("right");
  const [topic, setTopic] = useState();

  const handleProblemTopic = (topic) => {
    setTopic(topic.name);
    dispatch(getProblems({ status: "public", topic: topic.name }));
  };

  const handleAddProblemIntoBatch = (data) => {
    dispatch(
      addProblemIntoBatch({
        batch: data.batch,
        problemDetails: data.id,
        day: data.day,
        status: data.status,
        problem_type: data.problem_type,
      }),
    );
  };

  return (
    <>
      <Space>
        <Button type='primary' onClick={showDrawer}>
          Add Problem
        </Button>
      </Space>
      <Drawer
        title='Add Problem Into Batch'
        placement={placement}
        setPlacement={setPlacement}
        closable={false}
        onClose={onClose}
        open={open}
        key={placement}
      >
        <Combobox
          data={getTopicOptions(DSAContent)}
          dataKey={"value"}
          textField='name'
          placeholder={"Select Topic Name"}
          value={topic}
          onChange={(value) => handleProblemTopic(value)}
        />
        {problemsList &&
          problemsList.map((problem, key) => (
            <div className='mt-3 mb-n2' key={key}>
              <ProblemListCard
                handleAddIntoBatch={handleAddProblemIntoBatch}
                showAddBatchIcon={true}
                item={problem}
                openAddDay={openAddDay}
                setOpenAddDay={setOpenAddDay}
                batch={batch}
                type='problem'
                tempDay={tempDay}
              />
            </div>
          ))}
      </Drawer>
    </>
  );
};

export default AddBatchProblemDrawer;

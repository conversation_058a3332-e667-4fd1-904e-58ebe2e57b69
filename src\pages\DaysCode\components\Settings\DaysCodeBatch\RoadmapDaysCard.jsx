import React, { useEffect, useState } from "react";

const RoadmapDaysCard = ({ day, findMissingItems, codeBatchDetails}) => {
  const[count,setCount] = useState();
  const [bgColor, setBgColor] = useState("#368A9F")
  useEffect(()=>{
    if(Object.keys(codeBatchDetails)){
        const missingItems = findMissingItems(codeBatchDetails,day) || []
        setCount(missingItems.length)
    }
  },[codeBatchDetails])

  useEffect(()=>{
    if(count){
        setBgColor(count===1 ? "#61DBFB" : count===2 ? "#8CD9ED": "#F6F6F6")
    }
  },[count])

  return (
    <>
     <div className="days-card p-4 d-flex justify-content-center align-items-center" style={{backgroundColor:`${bgColor}`}}> <span>{day} </span> </div>
    </>
  );
};

export default RoadmapDaysCard;

import React, {useState } from "react";
import { useParams } from "react-router";
import { useDispatch } from "react-redux";
import { Combobox } from "react-widgets";
import { MdRestore } from "react-icons/md";

import CustomFormModal from "../../sharedComponents/CustomFormModal";
import { speakerTypes } from "../../utils/selectOptions";
import BannerImageUpload from "../../sharedComponents/BannerImageUpload";
import ToolTip from "../../sharedComponents/ToolTip";
import { addEventSpeaker } from "../../../pages/Events/actions";

const AddSpeakerModal = ({ isOpenModal, setIsOpenModal, speakerId }) => {

  const dispatch = useDispatch();
  const { id } = useParams();

  const [speakerType, setSpeakerType] = useState("");
  const [talkTitle, setTalkTitle] = useState("");
  const [designation, setDesignation] = useState("");
  const [description, setDescription] = useState("");
  const [posterImage, setPosterImage] = useState("");
  // const [resourceType, setResourceType] = useState(null);
  // const [resourceURL, setResourceURL] = useState(null);
  // const [resources, setResources] = useState([]);

  const handleToggleModal = () => {
    setIsOpenModal(!isOpenModal);
  };
  // const addResources = () => {
  //   const newResource = { url: resourceURL, type: resourceType };
  //   setResources([...resources, newResource]);
  // };

  const onSubmit = () => {
    dispatch(
      addEventSpeaker({
        eventId: id,
        speaker: speakerId,
        talkTitle,
        type: speakerType,
        designation,
        posterImage,
        description,
      }),
    );
  };
  return (
    <div className='event-add-speaker-modal'>
      <CustomFormModal
        isOpen={isOpenModal}
        toggleModal={handleToggleModal}
        title={"Add Speaker"}
        onSubmitTitle={"Add"}
        onSubmit={onSubmit}
      >
        {posterImage === "" ? (
          <div className='event-add-speaker-modal'>
            <BannerImageUpload setUploadedImg={setPosterImage} />
          </div>
        ) : (
          <div className='event-add-speaker-modal'>
            <img src={posterImage} alt='speaker' />
            <ToolTip title='Reset Image' placement='right'>
              <button
                onClick={() => setPosterImage("")}
                className='btn text-secondary'
              >
                <MdRestore className='' size={"20px"} />
              </button>
            </ToolTip>
          </div>
        )}

        <div className='text-left form-set'>
          <label className='form-label'>Speaker Type</label>
          <Combobox
            data={speakerTypes}
            dataKey={"type"}
            textField={"type"}
            placeholder={"Select Speaker Type"}
            value={speakerType.type}
            onChange={(e) => setSpeakerType(e.value)}
          />
        </div>
        <div className='text-left form-set mt-2'>
          <label className='form-label'>Talk-Title</label>
          <input
            type={"text"}
            className={"form-control"}
            placeholder={"Enter Talk-Title"}
            value={talkTitle}
            onChange={(e) => setTalkTitle(e.target.value)}
          />
        </div>
        <div className='text-left form-set mt-2'>
          <label className='form-label'>Designation</label>
          <input
            type={"text"}
            className={"form-control"}
            placeholder={"Enter Designation"}
            value={designation}
            onChange={(e) => setDesignation(e.target.value)}
          />
        </div>
        <div className='text-left form-set mt-2'>
          <label className='form-label'>Description</label>
          <textarea
            className={"form-control"}
            placeholder={"Enter Description"}
            value={description}
            onChange={(e) => setDescription(e.target.value)}
          />
        </div>
        {/* <div className='d-flex flex-column mt-2 mb-0'>
          <label className='form-label'>Resource</label>
          <Space.Compact size='large'>
            <Select
              placeholder='Resource Type'
              options={resourceOptions}
              value={resourceType}
              onChange={(e) => {
                setResourceType(e);
              }}
            />
            <Input
              placeholder='URL'
              value={resourceURL}
              onChange={(e) => {
                setResourceURL(e.target.value);
              }}
            />
            <Button type='dashed' onClick={addResources}>
              Add
            </Button>
          </Space.Compact>
        </div> */}
      </CustomFormModal>
    </div>
  );
};

export default AddSpeakerModal;

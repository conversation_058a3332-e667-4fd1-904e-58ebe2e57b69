import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ooter } from "reactstrap";

const CourseUserEnrollModel = ({
  model,
  toggle,
  courseDetails,
  handleEnrollCourseByUser,
}) => {

  return (
    <div>
      <Modal centered isOpen={model} toggle={toggle} className="text-center">
        <ModalHeader
          className="d-flex justify-content-between align-items-center"
          toggle={toggle}
        ></ModalHeader>
        <ModalBody>
          <h4 className="modal-title mb-5">Course User Enrollment</h4>
          <p>Are you sure you want to Enroll This Course?</p>
        </ModalBody>
        <ModalFooter className="d-flex justify-content-center align-items-center">
          <Button
            color="success"
            onClick={() => handleEnrollCourseByUser(courseDetails._id)}
          >
            Enroll
          </Button>
        </ModalFooter>
      </Modal>
    </div>
  );
};

export default CourseUserEnrollModel;
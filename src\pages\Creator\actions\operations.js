import axios from "axios";
import * as actions from "./actionCreators";
import { GET_PROBLEMS_LOADING } from "../constants";
import { generateQueryParams } from "../../../components/utils";
import { triggerNotifier } from "../../../components/utils/notifier";

const baseURL = process.env.REACT_APP_BASE_URL;

export const getProblems = (status, problem_type, day) => (dispatch) => {
  dispatch({ type: GET_PROBLEMS_LOADING });
  return axios
    .get(
      `${baseURL}/dayscode/fetch_problems${generateQueryParams({
        day: day ? day : null,
        problem_type: problem_type ? problem_type : "assignment",
        status: status ? status : "public",
      })}`
    )
    .then((res) => {
      if (res.status === 200) {
        triggerNotifier({
          message: "Problems list loaded ",
          type: "success",
          duration: 1000,
          icon: "👏",
        });
        dispatch(actions.setProblemsList(res?.data?.problems));
        return { success: true, data:res?.data?.problems };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: GET_PROBLEMS_LOADING });
      console.log("problem List Error", error);
      triggerNotifier({
        message: "problem list Error",
        type: 'error',
        duration: 1000,
        icon: "⚠️",
      });
    });
};

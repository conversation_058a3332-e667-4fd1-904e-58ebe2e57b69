import { loadingHandlers } from './loadingHandlers'
import { dataHandlers } from './dataHandlers'
import { createReducer } from '../../../core/reduxUtils'

const initialState = {
  authorBlogsList: [],
  authorBlogsCount: 0,
  
  loginUserError: "",
  authorBlogsLoading: false,
}

const handlers = {
  ...loadingHandlers,
  ...dataHandlers,
};

const blog = createReducer(initialState, handlers);

export default blog;
import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import QuestionViewHeader from "./QuestionViewHeader";
import QuestionviewSidebar from "./QuestionViewSidebar";
import QuestionViewHero from "./QuestionViewHeroSection";
import { getQuizQuestionDetails } from "../../../../actions/operations";
import CustomLoader from "../../../../../../components/sharedComponents/CustomLoader";

const QuestionView = ({ setIsEditDrawer, questionId }) => {
  const dispatch = useDispatch();
  
  const { quizQuestionDetails, questionCreateLoading } = useSelector(
    (state) => state.quizTest,
  );

  useEffect(() => {
    dispatch(getQuizQuestionDetails(questionId));
  }, [questionId]);

  return (
    <>
      <div className='row mx-0 p-0 bg-white question-view'>
        <QuestionViewHeader setIsEditDrawer={setIsEditDrawer} />
        {questionCreateLoading ? (
          <div className='text-center my-5 w-100 '>
            <CustomLoader />
          </div>
        ) : (
          <div className='col-lg-12 p-0 justify-content-between Question-overview-A'>
            <div className='row mx-0 p-0'>
              <div className='col-lg-7 col-sm-12 px-0'>
                <QuestionViewHero quizQuestionDetails={quizQuestionDetails} />
              </div>
              <div className='col-lg-5 col-sm-12 px-0'>
                <QuestionviewSidebar
                  quizQuestionDetails={quizQuestionDetails}
                />
              </div>
            </div>
          </div>
        )}
      </div>
    </>
  );
};

export default QuestionView;

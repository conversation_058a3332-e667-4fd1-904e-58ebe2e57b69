import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from "antd";

import EventTimelineForm from "./EventTimelineForm";
import ToolTip from "../../../../../../components/sharedComponents/ToolTip";

const EventTimelineFormDrawer = ({
  tempEditField,
  isEdit,
  setEdit,
  formName,
}) => {
  
  const [open, setOpen] = useState(false);
  const [placement, setPlacement] = useState("right");
  const showDrawer = () => {
    setOpen(true);
  };
  const onClose = () => {
    setOpen(false);
  };
  // const onChange = (e) => {
  //   setPlacement(e.target.value);
  // };
  return (
    <>
      <Space>
        <ToolTip title="Add  Timeline" placement="bottom">
          <Button
            className="add-timeline-btn"
            type="primary"
            onClick={showDrawer}
          >
            Add New Timeline
          </Button>
        </ToolTip>
      </Space>
      <Drawer
        title="Add New Timeline"
        placement={placement}
        closable={false}
        onClose={onClose}
        open={open}
        key={placement}
      >
        <EventTimelineForm onCloseDrawer={onClose} />
      </Drawer>
    </>
  );
};
export default EventTimelineFormDrawer;

import React, { useState } from "react";
import { Field, reduxForm } from "redux-form";
import { Navigate, useNavigate } from "react-router-dom";

import { required } from "../../../../../../../components/utils/validators";
import {
  renderInputField,
  renderSelectField,
} from "../../../../../../../components/sharedComponents/ReduxFormFields";

const CreateQuiz = ({
  handleSubmit,
  submitting,
  reset,
  createQuizTest,
  userName,
  currentQuizTest,
  toggle,
  closeModal,
}) => {
  
  const navigate = useNavigate();

  const onSubmit = (values) => {
    const formValues = { ...values };
    let quizTest = formValues;
    quizTest["userName"] = userName;
    let quizTestId = Math.random().toString(36).substr(4, 9);
    quizTest["quizId"] = quizTestId;
    createQuizTest(quizTest).then((res) => {
      if (res) {
        navigate(`/quiz/test/${res.data.quizTest._id}`);
      }
    });
    reset("createQuizTest");
  };

  return (
    <>
      <div className='row p-3'>
        <div className='col-12'>
          <form className='w-100' onSubmit={handleSubmit(onSubmit)}>
            <div className='form-row mx-0'>
              <div className='form-group m-0 d-block'>
                <Field
                  name='quizTitle'
                  type='text'
                  component={renderInputField}
                  label='Enter Quiz Test Name'
                  placeholder='Enter QuizTest Title'
                  validate={[required]}
                />
              </div>
              <div className='form-group m-0  d-block'>
                <Field
                  name='duration'
                  type='text'
                  component={renderInputField}
                  label='Quiz Duration'
                  placeholder='Enter time in minutes'
                  validate={[required]}
                />
              </div>
              <div className='form-group m-0  d-block'>
                <Field
                  name='quizTechnology'
                  component={renderSelectField}
                  label='Technology'
                  placeholder='Select Technology'
                  validate={[required]}
                  options={[
                    { value: "react", label: "React" },
                    { value: "vue", label: "Vue" },
                    { value: "angular", label: "Angular" },
                    { value: "node", label: "Node.js" },
                    { value: "python", label: "Python" },
                    { value: "java", label: "Java" },
                  ]}
                  textField='label'
                />
              </div>
              <div className='form-group m-0  d-block'>
                <Field
                  name='quizCutOff'
                  type='text'
                  component={renderInputField}
                  label='Quiz CutOff'
                  placeholder='Enter Passing CutOff'
                  validate={[required]}
                />
              </div>
              <div className='form-group m-0  d-block'>
                <Field
                  name='quizCategory'
                  component={renderSelectField}
                  label='quizCategory'
                  placeholder='Select Quiz Category'
                  validate={[required]}
                  options={[
                    { value: "assessment", label: "Assessment" },
                    { value: "quizTest", label: "QuizTest" },
                  ]}
                  textField='label'
                />
              </div>
            </div>
            <div className='form-group row mt-5 mx-0'>
              <div className='col text-right px-0'>
                <button
                  type='submit'
                  className='btn btn-primary'
                  disabled={submitting}
                >
                  Create Quiz Test
                </button>
                <button className='btn btn-secondary ml-3' onClick={toggle}>
                  Cancel
                </button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </>
  );
};

export default reduxForm({
  form: "createQuizTest",
})(CreateQuiz);

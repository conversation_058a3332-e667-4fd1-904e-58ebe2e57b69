import React, { useRef } from 'react';
import PropTypes from 'prop-types'
import { Editor } from '@tinymce/tinymce-react';

const TinyTextEditor = ({
  handleTextEditor,
  text = '',
}) => {
  const editorRef = useRef(null);
/*   const log = () => {
    if (editorRef.current) {
      console.log(editorRef.current.getContent());
    }
  }; */
  return (
    <>
      <Editor
        onInit={(evt, editor) => editorRef.current = editor}
        value={text}
        init={{
          height: 500,
          menubar: false,
          plugins: [
            'a11ychecker', 'advlist', 'advcode', 'advtable', 'autolink', 'checklist', 'export',
            'lists', 'link', 'image', 'charmap', 'preview', 'anchor', 'searchreplace', 'visualblocks',
            'powerpaste', 'fullscreen', 'formatpainter', 'insertdatetime', 'media', 'table', 'help', 'wordcount'
          ],
          toolbar: 'undo redo | casechange blocks | bold italic backcolor | ' +
            'alignleft aligncenter alignright alignjustify | ' +
            'bullist numlist checklist outdent indent | removeformat | a11ycheck code table help',
          content_style: 'body { font-family:Helvetica,Arial,sans-serif; font-size:14px }'
        }}
        onEditorChange={(value) => handleTextEditor(value)}
      />
      {/* <button onClick={log}>Log editor content</button> */}
    </>
  );
}

TinyTextEditor.propTypes = {
  handleTextEditor : PropTypes.func.isRequired,
  text : PropTypes.string,
};

export default TinyTextEditor
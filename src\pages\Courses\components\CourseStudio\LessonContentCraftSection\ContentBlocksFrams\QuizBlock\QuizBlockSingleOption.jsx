import React, { useEffect } from "react";
import { Field, getFormValues, reduxForm } from "redux-form";
import { connect, useDispatch } from "react-redux";

import { required } from "../../../../../../../components/utils/validators";
import {
  renderInputField,
  renderRadioField,
} from "../../../../../../../components/sharedComponents/ReduxFormFields";
import ContentBlockLayout from "../ContentBlockLayout";
import { editContent } from "../../../../../actions";

const QuizBlockSingleOption = ({
  handleSubmit,
  formValues,
  item,
  initialize,
}) => {
  const dispatch = useDispatch();

  useEffect(() => {
    initialize(item.payload);
  }, []);
  
  const onSubmit = (value) => {
    const data = {
      id: item._id,
      payload: value,
    };
    dispatch(editContent(data));
  };

  const optionsCheckBoxRow = () => {
    var row = [];
    for (let i = 1; i <= 4; i++) {
      row.push(
        // <div className={ `my-2 ${(`option${i}` === (formValues && formValues[ 'correct_answer' ])) ? 'border border-success' : 'border'}` }>
        <div className="my-2">
          <div className="row mx-0 quiz-challenge-option type-checkbox d-flex align-items-center">
            <div className="col-1 px-0 d-flex justify-content-center align-items-center">
              <Field
                name="correct_answer"
                component={renderRadioField}
                value={`option${i}`}
                type="radio"
                label={i}
                validate={[required]}
              />
            </div>
            <div className="col-11 px-0 option-text d-flex align-items-center">
              <Field
                type="text"
                name={`option${i}`}
                placeholder=""
                component={renderInputField}
                validate={[required]}
                inputClass={"mt-0"}
              />
            </div>
          </div>
        </div>
      );
    }
    return row;
  };

  const headerHtml = () => {
    return (
      <>
        <div className="embeds-block-header d-flex justify-content-between">
          <div>
            <div className="d-flex embeds-block-title">
              <i className="fas fa-question" />

              <p className="mb-0">Single Option Quiz</p>
            </div>
            <p className="mb-0">
              Type your question and select the correct answer.
            </p>
          </div>
        </div>
      </>
    );
  };

  return (
    <>
      <ContentBlockLayout
        item={item}
        blockHeader={headerHtml()}
        previewVisibility={true}
      >
        <form onSubmit={handleSubmit(onSubmit)}>
          <Field
            type="text"
            name="question"
            label="Question"
            placeholder="Type your Question"
            component={renderInputField}
            validate={[required]}
          />
          {optionsCheckBoxRow()}
          <div className="d-flex justify-content-end">
            <button type="submit" className="btn btn-primary">
              Save
            </button>
          </div>
        </form>
      </ContentBlockLayout>
    </>
  );
};

export default connect((state) => ({
  formValues: getFormValues()(state),
}))(reduxForm()(QuizBlockSingleOption));

import { GoPlus } from "react-icons/go";

const SponsorsCard = ({  eventDetails ,setActiveTab}) => {
    return (
      <>
        <div className='card p-4 rounded-15'>
          <div className='mb-3 card-head'>
            Sponsors & Parteners <span className='rounded-icon ml-2' onClick={() => setActiveTab("sponsor")}><GoPlus /></span>
          </div>
          <div className='d-flex row'>{eventDetails?.sponsors?.map(sponsor => (
            <div key={sponsor._id} className='col-xl-3 col-lg-4 col-md-5 col-5 border-dashed-purple mx-3 p-3' style={{ backgroundColor: "#f1edfd" }}>
              <div className='d-flex row'>
                <div className='d-flex flex-column col-5'>
                  <img src={`${sponsor?.sponsor.imgUrl}`} className='rounded-image' alt=""></img>
                  <span className='mt-1 bio'>{sponsor?.bio}</span>
                </div>
                <div className='d-flex flex-column  col-7 px-0 name justify-content-center'><span>{sponsor?.sponsor.name}</span>{sponsor?.type}</div>
              </div>
            </div>
          ))}</div>
        </div>
      </>
    )
  }

  export default SponsorsCard;
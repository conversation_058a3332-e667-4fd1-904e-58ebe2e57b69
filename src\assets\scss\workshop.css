.btn-main {
  padding: 10px 30px;
  border-radius: 5px;
  background-color: #7848f4;
  color: white;
  border: none;
  font-size: 20px;
  font-weight: bold;
  display: block;
}

.gradient-text {
  background: linear-gradient(86.62deg, #f56c19 30.73%, #da1b60 63.23%);
  -webkit-background-clip: text;
          background-clip: text;
  width: -moz-fit-content;
  width: fit-content;
  -webkit-text-fill-color: transparent;
}

.padding-x {
  padding-left: 5%;
  padding-right: 5%;
}

.padding-y {
  padding-top: 7%;
  padding-bottom: 7%;
}

.heading {
  font-size: 40px;
  font-weight: 700;
}

.hero-section {
  position: relative;
}
.hero-section h1 {
  font-size: 50px;
  max-width: 500px;
}
.hero-section .shapes .square1 {
  width: 30px;
  height: 30px;
  position: absolute;
  z-index: -1;
  border: 5px solid rgb(255, 153, 0);
  top: 5%;
  left: 40%;
}
.hero-section .shapes .square2 {
  width: 15px;
  height: 15px;
  position: absolute;
  z-index: -1;
  border: 4px solid rgb(255, 0, 153);
  top: 9%;
  right: 15%;
}
.hero-section .shapes .circle {
  width: 30px;
  height: 30px;
  position: absolute;
  z-index: -1;
  border: 4px solid rgb(255, 213, 0);
  border-radius: 100%;
  bottom: 5px;
  left: 47%;
}
.hero-section .shapes .triangle {
  width: 0px;
  height: 0px;
  position: absolute;
  z-index: -1;
  border-left: 20px solid transparent;
  border-right: 30px solid transparent;
  border-top: 20px solid rgb(255, 0, 85);
  left: 5%;
  bottom: 20%;
}

.workshop-section .btn-main {
  width: 100%;
}
.workshop-section .details-card, .workshop-section .speaker-block, .workshop-section .detail-block {
  border-radius: 5px;
  background-color: #1d1b1c;
  color: #fff;
  padding: 20px;
}
.workshop-section .detail-block {
  align-items: center;
}
.workshop-section .detail-block span {
  font-size: 14px;
  margin-left: 10px;
}
.workshop-section .speaker-block p {
  font-size: 14px;
}
.workshop-section .speaker-block img {
  border-radius: 100%;
}
.workshop-section .speaker-block .participants-box span {
  color: #da1b60;
}
.workshop-section .speaker-block .participants-box img {
  width: 30px;
  height: 30px;
  position: relative;
}
.workshop-section .speaker-block .participants-box img:nth-child(2) {
  right: 10px;
}
.workshop-section .speaker-block .participants-box img:nth-child(3) {
  right: 20px;
}
.workshop-section .by {
  background: linear-gradient(86.62deg, #f56c19 30.73%, #da1b60 63.23%);
  padding: 5px;
  width: 50px;
  height: 50px;
  border-radius: 100%;
  position: relative;
  top: 20px;
}
.workshop-section .right .workshop-img {
  border-radius: 7px;
  background: linear-gradient(86.62deg, #f56c19 30.73%, #da1b60 63.23%);
  padding: 5px;
}
.workshop-section .right .workshop-img img {
  border-radius: 5px;
}
.workshop-section .right p {
  font-size: 14px;
  max-width: 500px;
  font-weight: 500;
}

.learn-section .btn-main {
  width: 50%;
}
.learn-section .learn-container .learn-card {
  position: relative;
  max-width: 300px;
  min-height: 300px;
  box-shadow: 0px 4px 15px 0px rgba(0, 0, 0, 0.1843137255);
  border-radius: 7px;
}
.learn-section .learn-container .learn-card .left,
.learn-section .learn-container .learn-card .right {
  position: absolute;
  z-index: -1;
  width: 50%;
  height: 100%;
}
.learn-section .learn-container .learn-card .left {
  left: 0;
  background-color: #f0f0f0;
  border-top-left-radius: 7px;
  border-bottom-left-radius: 7px;
}
.learn-section .learn-container .learn-card .right {
  right: 0;
  background-color: #f9f9f9;
  border-top-right-radius: 7px;
  border-bottom-right-radius: 7px;
}
.learn-section .learn-container .learn-card span {
  position: absolute;
  top: 5%;
  left: 30%;
  font-size: 130px;
  font-weight: bold;
  opacity: 0.15;
}
.learn-section .learn-container .learn-card .card-body {
  padding: 20px;
  padding-top: 40px;
}

.steps-learning-section {
  color: #000;
  min-height: 500px;
}
.steps-learning-section h1 {
  font-size: 50px;
  font-weight: 800;
  line-height: 64px;
}
.steps-learning-section p {
  font-size: 32px;
  color: #21272a;
  font-weight: 700;
}
.steps-learning-section .step-card h6 {
  margin-top: 20px;
  margin-bottom: 20px;
  font-size: 16px;
  font-weight: 700;
}
.steps-learning-section .step-card span {
  color: #8082a5;
  font-size: 16px;
  line-height: 2px;
}
.steps-learning-section .explore-button {
  padding: 15px 15px 15px 15px;
  background-color: #7848f4;
  color: #f9f9fd;
  border-radius: 5px;
  border: none;
  font-size: 13px;
}

.popular-workshop-section {
  margin-top: 50px;
  padding-top: 70px;
}
.popular-workshop-section .heading {
  font-size: 45px;
  font-weight: 800;
  letter-spacing: -1px;
}
.popular-workshop-section .workshop-name {
  color: #280a64;
}
.popular-workshop-section .explore-button {
  padding: 15px 15px 15px 15px;
  background-color: #7848f4;
  color: #f9f9fd;
  border-radius: 5px;
  border: none;
  font-size: 13px;
}

.learn-plan-section {
  margin: 7% 0;
  background-color: #f9f9fd;
  color: #8082a5;
}
.learn-plan-section .date-section span {
  cursor: pointer;
}
.learn-plan-section .date-section span:first-child {
  color: rgb(0, 169, 241);
  border-bottom: 1px solid rgb(0, 169, 241);
}
.learn-plan-section .learn-plan-details-section .learn-plan-item {
  margin-bottom: 30px;
}
.learn-plan-section .learn-plan-details-section .learn-plan-item:last-child {
  margin-bottom: 0;
}
.learn-plan-section .learn-plan-details-section .learn-plan-item h5 {
  color: black;
}
.learn-plan-section .learn-plan-details-section .short-break {
  position: relative;
}
.learn-plan-section .learn-plan-details-section .short-break .break-line {
  width: 100%;
  height: 1px;
  background-color: rgb(114, 192, 255);
  position: absolute;
  left: 0;
  top: 50%;
}
.learn-plan-section .learn-plan-details-section .short-break .short-break-btn {
  padding: 13px 28px;
  border-radius: 30px;
  color: white;
  width: -moz-fit-content;
  width: fit-content;
  background: linear-gradient(96.97deg, #F56C19 5.38%, #DA1B60 124.47%);
  position: relative;
  z-index: 1;
}

.workshop-filter-bar {
  color: #000 !important;
}
.workshop-filter-bar .filter-badge {
  border: 1px solid #000;
  background-color: #676767;
  color: #000;
  padding: 5px;
  text-align: center;
  border-radius: 5px;
}
.workshop-filter-bar .sort-border {
  border: 1px solid #c5c5c5;
  border-radius: 12px;
  color: #8082a5;
}
.workshop-filter-bar .filter-border {
  border: 1px solid #c5c5c5;
  border-radius: 12px;
  color: #8082a5;
}

.about-section {
  background-color: #f9f9fd;
  margin: 7% 0;
}
.about-section p {
  max-width: 1000px;
  line-height: 40px;
  color: #676767;
  font-weight: 600;
}

.what-you-learn-workshop .learn-card {
  border: 1px solid #676767;
  border-radius: 7px;
}

.testimonial-section {
  background-color: #f9f9fd;
}
.testimonial-section .testimonial-card {
  margin: 20px;
  border-radius: 10px;
  box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.0705882353);
  background-color: white;
}
.testimonial-section .testimonial-card .star-svg {
  height: 20px;
  width: 20px;
}
.testimonial-section .testimonial-card p {
  font-size: 90%;
}

.faq-section {
  color: #676767;
}
.faq-section h5 {
  line-height: 160%;
}
.faq-section .accordion .accordion-item {
  border: 1px solid #F3D1BF;
  border-radius: 5px;
}
.faq-section .accordion .accordion-item .accordion-header button {
  padding: 15px;
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border: none;
  outline: none;
  background-color: transparent;
  font-size: 16px;
  font-weight: bold;
  text-align: left;
}
.faq-section .accordion .accordion-item .accordion-header button span {
  color: #EF6D58;
  font-size: 24px;
}
.faq-section .accordion .accordion-item .accordion-body {
  padding: 15px;
  padding-top: 0;
}

@media (max-width: 767px) {
  .padding-x {
    padding-left: 5%;
    padding-right: 5%;
  }
  .padding-y {
    padding-top: 10%;
    padding-bottom: 10%;
  }
  .heading {
    font-size: 30px;
  }
  p {
    font-size: 16px;
  }
  .hero-section h1 {
    font-size: 30px;
  }
  .hero-section .shapes .square1 {
    width: 20px;
    height: 20px;
    position: absolute;
    z-index: -1;
    border: 4px solid rgb(255, 153, 0);
    left: 30%;
  }
  .hero-section .shapes .square2 {
    width: 10px;
    height: 10px;
    position: absolute;
    z-index: -1;
    border: 3px solid rgb(255, 0, 153);
  }
  .hero-section .shapes .circle {
    width: 20px;
    height: 20px;
    position: absolute;
    z-index: -1;
  }
  .hero-section .shapes .triangle {
    width: 0px;
    height: 0px;
    position: absolute;
    z-index: -1;
    border-left: 10px solid transparent;
    border-right: 15px solid transparent;
    border-top: 10px solid rgb(255, 0, 85);
    bottom: 10%;
  }
  .about-section p {
    line-height: 30px;
  }
  .learn-section .learn-container .learn-card {
    max-width: 250px;
  }
  .workshop-page-hero {
    min-height: 500px;
    align-items: center;
  }
  .workshop-page-hero h1 {
    font-size: 50px;
  }
  .workshop-page-hero p {
    font-size: 16px;
    margin-top: 30px;
  }
  .workshop-details-section .detail-block {
    border-radius: 5px;
    background-color: #000;
    color: #fff;
    font-size: 12px;
    padding: 20px;
    align-items: center;
    display: flex;
  }
  .workshop-details-section .detail-block span {
    font-size: 16px;
    margin-left: 10px;
  }
  .workshop-details-section .speaker-block {
    background-color: #000;
    border-radius: 5px;
  }
  .about-workshop {
    background-color: #f9f9fd;
    color: #000;
    min-height: 500px;
    align-items: center;
  }
  .about-workshop h1 {
    font-size: 45px;
  }
  .about-workshop p {
    font-size: 18px;
  }
  .learn-section .btn-main {
    width: 100%;
  }
}
@media (max-width: 480px) {
  .btn-main {
    padding: 10px 20px;
    font-size: 16px;
  }
  .padding-x {
    padding-left: 5%;
    padding-right: 5%;
  }
  .padding-y {
    padding-top: 15%;
    padding-bottom: 15%;
  }
  p {
    font-size: 14px;
  }
}
.card-body {
  box-shadow: 0 1px 6px 0 rgba(0, 0, 0, 0.1);
}
.card-body:hover {
  box-shadow: 0 4px 16px 4px rgba(0, 0, 0, 0.15);
  transition: transform 0ms linear 0s;
}
.card-body .workshopcard-body .item-days {
  color: #9672ff;
  font-size: 14px;
  font-weight: 400;
}
.card-body .workshopcard-body .item-category {
  color: #676767;
  background-color: #ff8fa5;
  font-size: 12px;
  font-weight: 400;
  border-radius: 6px;
  padding: 8px 20px 8px 20px;
}
.card-body .workshopcard-body .item-title {
  color: #081F32;
  font-size: 22px;
  font-weight: 600;
}
.card-body .workshopcard-body .enroll-now-button {
  background-color: #7848F4;
  color: #FFFFFF;
  font-size: 12px;
  font-weight: 400;
  border: none;
  padding: 8px 25px 8px 25px;
  border-radius: 5px;
}
.card-body .workshopcard-body .speaker-section-border {
  border: 1px solid #C5C5C5;
  border-left: none;
  border-right: none;
}
.card-body .workshopcard-body .speaker-section-border .speaker-section {
  padding: 10px 30px 10px 10px;
  border-right: 1px solid #C5C5C5;
}
.card-body .workshopcard-body .speaker-section-border .speaker-section .item-speaker-name {
  font-size: 16px;
  font-weight: 600;
  padding-bottom: 4px;
}
.card-body .workshopcard-body .speaker-section-border .speaker-section .item-speaker-bio {
  color: #C5C5C5;
  font-size: 12px;
  font-weight: 300;
}
.card-body .workshopcard-body .item-title {
  color: #081F32;
  font-size: 22px;
  font-weight: 600;
}
.card-body .workshopcard-body .enroll-now-button {
  background-color: #7848F4;
  color: #FFFFFF;
  font-size: 12px;
  font-weight: 400;
  border: none;
  padding: 8px 25px 8px 25px;
  border-radius: 5px;
}
.card-body .workshopcard-body .speaker-section-border {
  border: 1px solid #C5C5C5;
  border-left: none;
  border-right: none;
}
.card-body .workshopcard-body .speaker-section-border .speaker-section {
  padding: 10px 30px 10px 10px;
  border-right: 1px solid #C5C5C5;
}
.card-body .workshopcard-body .speaker-section-border .speaker-section .item-speaker-name {
  font-size: 16px;
  font-weight: 600;
  padding-bottom: 4px;
}
.card-body .workshopcard-body .speaker-section-border .speaker-section .item-speaker-bio {
  color: #C5C5C5;
  font-size: 12px;
  font-weight: 300;
}
.card-body .workshopcard-body .item-title {
  color: #081f32;
  font-size: 22px;
  font-weight: 600;
}
.card-body .workshopcard-body .enroll-now-button {
  background-color: #7848f4;
  color: #ffffff;
  font-size: 12px;
  font-weight: 400;
  border: none;
  padding: 8px 25px 8px 25px;
  border-radius: 5px;
}
.card-body .workshopcard-body .speaker-section-border {
  border: 1px solid #c5c5c5;
  border-left: none;
  border-right: none;
}
.card-body .workshopcard-body .speaker-section-border .speaker-section {
  padding: 10px 30px 10px 10px;
  border-right: 1px solid #c5c5c5;
}
.card-body .workshopcard-body .speaker-section-border .speaker-section .item-speaker-name {
  font-size: 16px;
  font-weight: 600;
  padding-bottom: 4px;
}
.card-body .workshopcard-body .speaker-section-border .speaker-section .item-speaker-bio {
  color: #c5c5c5;
  font-size: 12px;
  font-weight: 300;
}
.card-body .workshopcard-body .speaker-section-2 {
  padding-right: 20px;
  border-left: none;
}
.card-body .workshopcard-body .speaker-section-2 .item-students {
  color: #c5c5c5;
  font-size: 12px;
  font-weight: 300;
}
.card-body .workshopcard-body .rating .star-svg {
  height: 20px;
  width: 20px;
}

.explore-training-body h1 {
  font-size: 45px;
  font-weight: 600;
}
.explore-training-body .card-body {
  box-shadow: 0 1px 6px 0 rgba(0, 0, 0, 0.1);
}
.explore-training-body .card-body:hover {
  box-shadow: 0 4px 16px 4px rgba(0, 0, 0, 0.15);
  transition: transform 0ms linear 0s;
}
.explore-training-body .card-body .workshopcard-body .item-days {
  color: #9672FF;
  font-size: 14px;
  font-weight: 400;
}
.explore-training-body .card-body .workshopcard-body .item-category {
  color: #FFFFFF;
  background-color: rgba(150, 114, 255, 0.5098039216);
  font-size: 10px;
  font-weight: 400;
  border-radius: 6px;
  padding: 8px 16px 8px 16px;
}
.explore-training-body .card-body .workshopcard-body .item-title {
  color: #081F32;
  font-size: 18px;
  font-weight: 600;
}
.explore-training-body .card-body .workshopcard-body .enroll-now-button {
  background-color: #7848F4;
  color: #FFFFFF;
  font-size: 12px;
  font-weight: 400;
  border: none;
  padding: 6px 20px 6px 20px;
  border-radius: 5px;
}
.explore-training-body .card-body .workshopcard-body .speaker-section-border {
  border: 1px solid #C5C5C5;
  border-left: none;
  border-right: none;
}
.explore-training-body .card-body .workshopcard-body .speaker-section-border .speaker-section {
  padding: 10px 30px 10px 10px;
  border-right: 1px solid #C5C5C5;
  padding-bottom: 6px;
}
.explore-training-body .card-body .workshopcard-body .speaker-section-border .speaker-section .item-speaker-name {
  font-size: 11px;
  font-weight: 600;
  padding-bottom: 4px;
}
.explore-training-body .card-body .workshopcard-body .speaker-section-border .speaker-section .item-speaker-bio {
  color: #C5C5C5;
  font-size: 9px;
  font-weight: 300;
}
.explore-training-body .explore-button {
  padding: 15px 15px 15px 15px;
  background-color: #7848f4;
  color: #f9f9fd;
  border-radius: 5px;
  border: none;
  font-size: 13px;
}

.become-mentor-body {
  background-color: #F9F9FD;
  padding: 80px 0px 30px 0px;
  margin: 110px 0px 110px 0px;
}
.become-mentor-body .explore-button {
  padding: 15px 15px 15px 15px;
  background-color: #7848f4;
  color: #f9f9fd;
  border-radius: 5px;
  border: none;
  font-size: 13px;
}
.become-mentor-body .mentor-content {
  padding: 0px 30px 0px 50px;
}
.become-mentor-body .mentor-content h1 {
  color: #391400;
  letter-spacing: -1px;
  font-size: 52px;
  font-weight: 600;
}
.become-mentor-body .mentor-content p {
  color: #391400;
  font-size: 18px;
  font-weight: 400;
}

.skills-success-section {
  background-color: #f5f7fa;
  padding: 30px 0px 30px 0px;
  margin-top: 80px;
}
.skills-success-section .skills-success-body {
  padding-right: 40px;
}
.skills-success-section .skills-success-body h1 {
  font-size: 30px;
  font-weight: 700;
}
.skills-success-section .skills-success-body span {
  font-size: 16px;
  font-weight: 400;
  color: #21272a;
}
.skills-success-section h6 {
  font-size: 16px;
  font-weight: 700;
}
.skills-success-section .item-content {
  font-size: 13px;
  font-weight: 400;
  color: #21272a;
  line-height: 8.2px;
}
.skills-success-section .explore-button {
  padding: 15px 20px 15px 20px;
  background-color: #7848f4;
  color: #f9f9fd;
  border-radius: 5px;
  border: none;
  font-size: 13px;
}

.workshop-testimonial-body {
  margin: 40px 60px 60px 40px;
  padding: 40px 30px 40px 30px;
}
.workshop-testimonial-body .testimonial-header {
  margin: 0px 60px 0px 70px;
}
.workshop-testimonial-body h6 {
  color: #280a64;
  font-size: 18px;
  font-family: 400;
  letter-spacing: 3px;
}
.workshop-testimonial-body h2 {
  font-weight: 600;
}
.workshop-testimonial-body .speaker-section {
  margin: 20px 0px 70px 0px;
}
.workshop-testimonial-body .speaker-section .testimonial-border {
  border: 2px solid #f5f1f1;
  border-radius: 12px;
}
.workshop-testimonial-body .speaker-section .testimonial-border:hover {
  box-shadow: 1px 2px 6px 2px rgba(233, 231, 231, 0.8078431373);
  transition: transform 0ms linear 0s;
}
.workshop-testimonial-body .speaker-section .testimonial-border .item-speaker-name {
  font-size: 15px;
  font-weight: 600;
  padding-bottom: 4px;
}
.workshop-testimonial-body .speaker-section .testimonial-border .item-speaker-bio {
  font-size: 13px;
  font-weight: 400;
}
.workshop-testimonial-body .speaker-section .testimonial-border .workshop-title {
  font-size: 16px;
  font-weight: 700;
  padding-bottom: 10px;
}
.workshop-testimonial-body .speaker-section .testimonial-border .item-content {
  padding-top: 5px;
  font-size: 12px;
  font-weight: 400;
  color: #21272a;
}
.workshop-testimonial-body .speaker-section .testimonial-border .rating {
  padding-top: 20px;
}
.workshop-testimonial-body .speaker-section .testimonial-border .rating .star-svg {
  height: 20px;
  width: 20px;
}

.herosection-heading {
  position: absolute;
}
.herosection-heading h2 {
  padding: 100px 0px 20px 0px;
  font-size: 42px;
  font-weight: 700;
  line-height: 75px;
  background: -webkit-linear-gradient(87.81deg, #454DFD 15.76%, #280A64 56.8%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.herosection-heading p {
  color: #909090;
  font-size: 20px;
  font-weight: 400;
}
.herosection-heading .lightning {
  padding-top: 30px;
  margin-top: 60px;
}

.workshop-search-bar {
  position: relative;
  padding: 20px 60px 20px 60px;
  background-color: #280A64;
}
.workshop-search-bar .form-control {
  padding-right: 50px;
}/*# sourceMappingURL=workshop.css.map */
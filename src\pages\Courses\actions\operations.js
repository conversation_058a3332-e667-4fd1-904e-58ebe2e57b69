import axios from "axios";
import * as actions from "./actionCreators";
import {
  GET_COURSES_LIST_LOADING,
  GET_COURSE_DETAILS_LOADING,
  SET_CREATE_COURSE_LOADING,
  SET_CREATE_SECTION_LOADING,
  GET_SECTIONS_LIST_LOADING,
  SET_CREATE_LESSON_LOADING,
  GET_LESSONS_LIST_LOADING,
  GET_SECTION_DETAILS_LOAD,
  GET_LESSON_DETAILS_LOADING,
  SET_CREATE_CONTENT_LOADING,
  GET_CONTENTS_LIST_LOADING,
  GET_CONTENT_DETAILS_LOADING,
  GET_USER_COURSES_LIST_LOADING,
  GET_COURSE_PROGRESS_LOAD,
  GET_USER_PROGRESS_LOAD,
  GET_COURSE_CURRICULUM_LOAD,
  GET_COURSE_USER_FORM_FIELD_LOADING,
  GET_COURSE_USERS_LIST_LOADING,
  GET_USERS_BY_COURSE_LIST_LOADING,
} from "../constants";
import { generateQueryParams } from "../../../components/utils";
import { triggerNotifier } from "../../../components/utils/notifier";

const baseURL = process.env.REACT_APP_BASE_URL;

export const createCourse = (course) => (dispatch) => {
  dispatch({ type: SET_CREATE_COURSE_LOADING });
  return axios
    .post(`${baseURL}/course/create`, course)
    .then((res) => {
      if (res.status === 200) {
        triggerNotifier({
          message: "Course Created",
          type: "success",
          duration: 1000,
          icon: "👏",
        });
        dispatch({ type: SET_CREATE_COURSE_LOADING });
        return { success: true, course:  res?.data?.course };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: SET_CREATE_COURSE_LOADING });
      console.log("Create Course Error", error);
      triggerNotifier({
        message: "Create Course Error",
        type: "error",
        duration: 1000,
        icon: "⚠️",
      });
    });
};

export const getCourses =
  (...props) =>
    (dispatch) => {
      dispatch({ type: GET_COURSES_LIST_LOADING });
      return axios
        .get(
          `${baseURL}/course${generateQueryParams({
            page: props[0] || 0,
            limit: props[1] || 10,
          })}`
        )
        .then((res) => {
          if (res.status === 200) {
            triggerNotifier({
              message: "Courses List Loaded",
              type: "success",
              duration: 1000,
              icon: "👏",
            });
            dispatch(actions.setCoursesList(res && res.data));
            return { success: true, data: res && res.data };
          } else {
            return { success: false };
          }
        })
        .catch((error) => {
          dispatch({ type: GET_COURSES_LIST_LOADING });
          console.log("Courses List Error", error);
          triggerNotifier({
            message: "Courses List Error",
            type: "error",
            duration: 1000,
            icon: "⚠️",
          });
        });
    };

export const getCourseDetails = (id) => (dispatch) => {
  dispatch({ type: GET_COURSE_DETAILS_LOADING });
  return axios
    .get(`${baseURL}/course/${id}`)
    .then((res) => {
      if (res.status === 200 && res.data.success) {
        triggerNotifier({
          message: "Courses Details Loaded",
          type: "success",
          duration: 1000,
          icon: "👏",
        });
        dispatch(actions.setCourseDetails(res && res?.data?.course));
        dispatch({ type: GET_COURSE_DETAILS_LOADING });
        return { success: true, data: res && res.data };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: GET_COURSE_DETAILS_LOADING });
      console.log("Course Details Error", error);
      triggerNotifier({
        message: "Course Details Error",
        type: "error",
        duration: 1000,
        icon: "⚠️",
      });
    });
};

export const deleteCourse = (id) => (dispatch) => {
  return axios
    .delete(`${baseURL}/course/${id}`)
    .then((res) => {
      if (res.status === 200) {
        triggerNotifier({
          message: "Course Deteted",
          type: "success",
          duration: 1000,
          icon: "👏",
        });
        return { success: true };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      console.log("delete course Error", error);
      triggerNotifier({
        message: "Delete Course Error",
        type: "error",
        duration: 1000,
        icon: "⚠️",
      });
    });
};

export const editCourse = (course) => (dispatch) => {
  return axios
    .patch(`${baseURL}/course/${course._id}`, { data: course })
    .then((res) => {
      if (res.status === 200) {
        triggerNotifier({
          message: "Edit Course",
          type: "success",
          duration: 1000,
          icon: "👏",
        });
        dispatch(actions.setCourseDetails(res?.data?.course));
        return { success: true, data: res && res.data };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      console.log("Edit Course Error", error);
      triggerNotifier({
        message: "Edit Course Error",
        type: "error",
        duration: 1000,
        icon: "⚠️",
      });
    });
};

// ---------Section ------------------

export const createSection = (section) => (dispatch) => {
  dispatch({ type: SET_CREATE_SECTION_LOADING });
  return axios
    .post(`${baseURL}/section/create`, section)
    .then((res) => {
      if (res.status === 200) {
        triggerNotifier({
          message: "Section Created",
          type: "success",
          duration: 1000,
          icon: "👏",
        });
        dispatch({ type: SET_CREATE_SECTION_LOADING });
        return { success: true, section:  res?.data?.section };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: SET_CREATE_SECTION_LOADING });
      console.log("Add Section Error", error);
      triggerNotifier({
        message: "Add Section Error",
        type: "error",
        duration: 1000,
        icon: "⚠️",
      });
    });
};

export const getSections =
  (...props) =>
    (dispatch) => {
      dispatch({ type: GET_SECTIONS_LIST_LOADING });
      return axios
        .get(
          `${baseURL}/section${generateQueryParams({
            course: props[0],
          })}`
        )
        .then((res) => {
          if (res.status === 200) {
            triggerNotifier({
              message: "Section List",
              type: "success",
              duration: 1000,
              icon: "👏",
            });
            dispatch(actions.setSectionsList(res && res.data));
            return { success: true, data: res && res.data };
          } else {
            return { success: false };
          }
        })
        .catch((error) => {
          dispatch({ type: GET_SECTIONS_LIST_LOADING });
          console.log("Sections List Error", error);
          triggerNotifier({
            message: "Section List Error",
            type: "error",
            duration: 1000,
            icon: "⚠️",
          });
        });
    };

export const getSectionDetails = (sectionId) => (dispatch) => {
  dispatch({ type: GET_SECTION_DETAILS_LOAD });
  return axios
    .get(`${baseURL}/section/${sectionId}`)
    .then((res) => {
      if (res.status === 200) {
        triggerNotifier({
          message: "Section Details",
          type: "success",
          duration: 1000,
          icon: "👏",
        });
        dispatch(actions.setSectionDetails(res?.data?.section));
        dispatch({ type: GET_SECTION_DETAILS_LOAD });
        return { success: true, data: res && res.data };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: GET_SECTION_DETAILS_LOAD });
      console.log("Sections Details Error", error);
      triggerNotifier({
        message: "Sections Details Error",
        type: "error",
        duration: 1000,
        icon: "⚠️",
      });
    });
};

export const editSection = (section) => (dispatch) => {
  return axios
    .patch(`${baseURL}/section/${section.id}`, { data: section })
    .then((res) => {
      if (res.status === 200) {
        triggerNotifier({
          message: "Edit Section",
          type: "success",
          duration: 1000,
          icon: "👏",
        });
        dispatch(actions.setSectionDetails( res?.data?.section));
        return { success: true, data: res && res.data };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      console.log("Edit Section Error", error);
      triggerNotifier({
        message: "Edit Section Error",
        type: "error",
        duration: 1000,
        icon: "⚠️",
      });
    });
};

export const deleteSection = (id) => (dispatch) => {
  return axios
    .delete(`${baseURL}/section/${id}`)
    .then((res) => {
      if (res.status === 200) {
        triggerNotifier({
          message: "Section Deleted",
          type: "success",
          duration: 1000,
          icon: "👏",
        });
        return { success: true };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      console.log("delete section Error", error);
      triggerNotifier({
        message: "Delete Section Error",
        type: "error",
        duration: 1000,
        icon: "⚠️",
      });
    });
};

// ------------Lesson -----------

export const createLesson = (lesson) => (dispatch) => {
  dispatch({ type: SET_CREATE_LESSON_LOADING });
  return axios
    .post(`${baseURL}/lesson/create`, lesson)
    .then((res) => {
      if (res.status === 200) {
        triggerNotifier({
          message: "Lesson Created",
          type: "success",
          duration: 1000,
          icon: "👏",
        });
        dispatch({ type: SET_CREATE_LESSON_LOADING });
        return { success: true, lesson:res?.data?.lesson };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: SET_CREATE_LESSON_LOADING });
      console.log("Add Lesson Error", error);
      triggerNotifier({
        message: "Add Lesson Error",
        type: "error",
        duration: 1000,
        icon: "⚠️",
      });
    });
};

/* export const getLessons = ({ author, category, status }) => (dispatch) => {
  dispatch({ type: GET_LESSONS_LIST_LOADING });
  return axios
    .get(
      `${baseURL}/lesson${generateQueryParams({
        author: author ?? null,
        category: category ?? null,
        status: status ?? null,
      })}`
    )
    .then((res) => {
      if (res.status === 200) {
        triggerNotifier({
          message: "Lesson Details",
          type: "success",
          duration: 1000,
          icon: "👏",
        });
        dispatch(actions.setLessonsList(res && res.data));
        return { success: true, data: res && res.data };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: GET_LESSONS_LIST_LOADING });
      console.log("Lessons List Error", error);
      triggerNotifier({
        message: "Lesson list Error",
        type: "error",
        duration: 1000,
        icon: "⚠️",
      });
    });
}; */

export const getLessons = () => (dispatch) => {
  dispatch({ type: GET_LESSONS_LIST_LOADING });
  return axios
    .get(
      `${baseURL}/lesson`
    )
    .then((res) => {
      if (res.status === 200) {
        triggerNotifier({
          message: "Lesson Details",
          type: "success",
          duration: 1000,
          icon: "👏",
        });
        dispatch(actions.setLessonsList(res && res.data));
        return { success: true, data: res && res.data };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: GET_LESSONS_LIST_LOADING });
      console.log("Lessons List Error", error);
      triggerNotifier({
        message: "Lesson list Error",
        type: "error",
        duration: 1000,
        icon: "⚠️",
      });
    });
  }

export const getLessonDetails = (lessonId) => (dispatch) => {
  dispatch({ type: GET_LESSON_DETAILS_LOADING });
  return axios
    .get(`${baseURL}/lesson/${lessonId}`)
    .then((res) => {
      if (res.status === 200) {
        triggerNotifier({
          message: "Lesson Details Loaded",
          type: "success",
          duration: 1000,
          icon: "👏",
        });
        dispatch(actions.setLessonDetails(res?.data?.lesson));
        dispatch({ type: GET_LESSON_DETAILS_LOADING });
        return { success: true, data: res && res.data };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: GET_LESSON_DETAILS_LOADING });
      console.log("Lesson Details Error", error);
      triggerNotifier({
        message: "Lesson Details Error",
        type: "error",
        duration: 1000,
        icon: "⚠️",
      });
    });
};

export const deleteLesson = (id) => (dispatch) => {
  return axios
    .delete(`${baseURL}/lesson/${id}`)
    .then((res) => {
      if (res.status === 200) {
        triggerNotifier({
          message: "Lesson Deleted",
          type: "success",
          duration: 1000,
          icon: "👏",
        });
        return { success: true };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      console.log("delete lession Error", error);
      triggerNotifier({
        message: "Delete Lesson Error",
        type: "error",
        duration: 1000,
        icon: "⚠️",
      });
    });
};

export const editLesson = (lesson) => (dispatch) => {
  return axios
    .patch(`${baseURL}/lesson/${lesson._id}`, { data: lesson })
    .then((res) => {
      if (res.status === 200) {
        triggerNotifier({
          message: "Edit Lesson",
          type: "success",
          duration: 1000,
          icon: "👏",
        });
        dispatch(actions.setLessonDetails(res?.data?.lesson));
        return { success: true, data: res && res.data };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      console.log("Edit Lesson Error", error);
      triggerNotifier({
        message: "Edit Lesson Error",
        type: "error",
        duration: 1000,
        icon: "⚠️",
      });
    });
};

// ---------Content --------------------

export const createContent = (content) => (dispatch) => {
  dispatch({ type: SET_CREATE_CONTENT_LOADING });
  return axios
    .post(`${baseURL}/content/create`, content)
    .then((res) => {
      if (res.status === 200) {
        triggerNotifier({
          message: "Content Created",
          type: "success",
          duration: 1000,
          icon: "👏",
        });
        dispatch({ type: SET_CREATE_CONTENT_LOADING });
        return { success: true, content: res?.data?.content };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: SET_CREATE_CONTENT_LOADING });
      console.log("Add Content Error", error);
      triggerNotifier({
        message: "Add Content Error",
        type: "error",
        duration: 1000,
        icon: "⚠️",
      });
    });
};

export const getContents =
  (...props) =>
    (dispatch) => {
      dispatch({ type: GET_CONTENTS_LIST_LOADING });
      return axios
        .get(
          `${baseURL}/content${generateQueryParams({
            lesson: props[0],
          })}`
        )
        .then((res) => {
          if (res.status === 200) {
            triggerNotifier({
              message: "Content List Loaded",
              type: "success",
              duration: 1000,
              icon: "👏",
            });
            dispatch(actions.setContentsList(res && res.data));
            return { success: true, data: res && res.data };
          } else {
            return { success: false };
          }
        })
        .catch((error) => {
          dispatch({ type: GET_CONTENTS_LIST_LOADING });
          console.log("Contents List Error", error);
          triggerNotifier({
            message: "Content List Error",
            type: "error",
            duration: 1000,
            icon: "⚠️",
          });
        });
    };

export const getContentDetails = (contentId) => (dispatch) => {
  dispatch({ type: GET_CONTENT_DETAILS_LOADING });
  return axios
    .get(`${baseURL}/content/${contentId}`)
    .then((res) => {
      if (res.status === 200) {
        triggerNotifier({
          message: "Content Details Loaded",
          type: "success",
          duration: 1000,
          icon: "👏",
        });
        dispatch({ type: GET_CONTENT_DETAILS_LOADING });
        return { success: true, data: res && res.data };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: GET_CONTENT_DETAILS_LOADING });
      console.log("Content Details Error", error);
      triggerNotifier({
        message: "Content Details Error",
        type: "error",
        duration: 1000,
        icon: "⚠️",
      });
    });
};

export const deleteContent = (id) => (dispatch) => {
  return axios
    .delete(`${baseURL}/content/${id}`)
    .then((res) => {
      if (res.status === 200) {
        triggerNotifier({
          message: "Content Deleted",
          type: "success",
          duration: 1000,
          icon: "👏",
        });
        return { success: true };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      console.log("delete lesson Error", error);
      triggerNotifier({
        message: "Delete lesson Error",
        type: "error",
        duration: 1000,
        icon: "⚠️",
      });
    });
};

export const editContent = (content) => (dispatch) => {
  return axios
    .patch(`${baseURL}/content/${content.id}`, { data: content })
    .then((res) => {
      if (res.status === 200) {
        triggerNotifier({
          message: "Edit Content",
          type: "success",
          duration: 1000,
          icon: "👏",
        });
        return { success: true, data: res && res.data };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      console.log("Edit Content Error", error);
      triggerNotifier({
        message: "Edit Content Error",
        type: "error",
        duration: 1000,
        icon: "⚠️",
      });
    });
};

// =-===========Complete Lesson -----=============================================
export const completeLesson =
  ({ lessonId, userId }) =>
    (dispatch) => {
      return axios
        .patch(`${baseURL}/lesson/${lessonId}/complete`, { data: { userId } })
        .then((res) => {
          if (res.status === 200) {
            triggerNotifier({
              message: "complete Lesson",
              type: "success",
              duration: 1000,
              icon: "👏",
            });
            return { success: true, data: res && res.data };
          } else {
            return { success: false };
          }
        })
        .catch((error) => {
          console.log("Complete Lesson Error", error);
          triggerNotifier({
            message: "Complete Lesson Error",
            type: "error",
            duration: 1000,
            icon: "⚠️",
          });
        });
    };

// =-===========User Course -----=============================================
export const getUserCourses = (courseUserId) => (dispatch) => {
  dispatch({ type: GET_USER_COURSES_LIST_LOADING });
  return axios
    .get(`${baseURL}/course/user/${courseUserId}/enrolled`)
    .then((res) => {
      if (res.status === 200) {
        triggerNotifier({
          message: "Courses List Loaded",
          type: "success",
          duration: 1000,
          icon: "👏",
        });
        dispatch(actions.setUserCoursesList(res && res.data));
        return { success: true, data: res && res.data };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: GET_USER_COURSES_LIST_LOADING });
      console.log("Courses List Error", error);
      triggerNotifier({
        message: "Courses List Error",
        type: "error",
        duration: 1000,
        icon: "⚠️",
      });
    });
};

//-----------  Enroll Course By User--------------
export const enrollCoursebyuser =
  ({ courseId }) =>
    (dispatch) => {
      return axios
        .patch(`${baseURL}/course/${courseId}/enroll`)
        .then((res) => {
          if (res.status === 200) {
            triggerNotifier({
              message: "Enrol Course",
              type: "success",
              duration: 1000,
              icon: "👏",
            });
            return { success: true, data: res && res.data };
          } else {
            return { success: false };
          }
        })
        .catch((error) => {
          console.log("Enroll Course Error", error);
          triggerNotifier({
            message: "Enroll Course Error",
            type: "error",
            duration: 1000,
            icon: "⚠️",
          });
        });
    };
//-------------------Course Progress-------------
export const getCourseProgress = (courseId) => (dispatch) => {
  dispatch({ type: GET_COURSE_PROGRESS_LOAD });
  return axios
    .get(`${baseURL}/course/${courseId}/progress`)
    .then((res) => {
      if (res.status === 200) {
        triggerNotifier({
          message: "Course progress",
          type: "success",
          duration: 1000,
          icon: "👏",
        });
        dispatch(actions.setCourseProgress(res && res.data));
        return { success: true, data: res && res.data };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: GET_COURSE_PROGRESS_LOAD });
      console.log("Course Progress Error", error);
      triggerNotifier({
        message: "Course Progress Error",
        type: "error",
        duration: 1000,
        icon: "⚠️",
      });
    });
};
//-------------------User Progress-------------
export const getUserProgress = (courseUserId) => (dispatch) => {
  dispatch({ type: GET_USER_PROGRESS_LOAD });
  return axios
    .get(`${baseURL}/course/user/${courseUserId}/progress`)
    .then((res) => {
      if (res.status === 200) {
        triggerNotifier({
          message: "User progress",
          type: "success",
          duration: 1000,
          icon: "👏",
        });
        dispatch(actions.setUserProgress(res && res.data));
        return { success: true, data: res && res.data };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: GET_USER_PROGRESS_LOAD });
      console.log("User Progress Error", error);
      triggerNotifier({
        message: "User Progress Error",
        type: "error",
        duration: 1000,
        icon: "⚠️",
      });
    });
};
//-------------Course Curriculum--------------
export const getCourseCurriculum = (courseId) => (dispatch) => {
  dispatch({ type: GET_COURSE_CURRICULUM_LOAD });
  return axios
    .get(`${baseURL}/course/${courseId}/curriculum`)
    .then((res) => {
      if (res.status === 200) {
        triggerNotifier({
          message: "Course Curriculum",
          type: "success",
          duration: 1000,
          icon: "👏",
        });
        dispatch(actions.setCourseCurriculum(res && res.data));
        return { success: true, data: res && res.data };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: GET_COURSE_CURRICULUM_LOAD });
      console.log("Course Curriculum Error", error);
      triggerNotifier({
        message: "Course Curriculum Error",
        type: "error",
        duration: 1000,
        icon: "⚠️",
      });
    });
};
//---------------Create Course User------------------

export const createCourseUser = (user) => (dispatch) => {
  console.log(user);
  dispatch({ type: GET_COURSE_USER_FORM_FIELD_LOADING });
  return axios
    .post(`${baseURL}/course/user`, user)
    .then((res) => {
      if (res && res.status === 200) {
        triggerNotifier({
          message: "Course User Created",
          type: "success",
          duration: 1000,
          icon: "👏",
        });
        dispatch(actions.setCourseUserId(res && res.data));
        dispatch({ type: GET_COURSE_USER_FORM_FIELD_LOADING });
        return { success: true, data:  res?.data?.user };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: GET_COURSE_USER_FORM_FIELD_LOADING });
      console.log("Create Course User Error", error);
      triggerNotifier({
        message: "Create Course User Error",
        type: "error",
        duration: 1000,
        icon: "⚠️",
      });
    });
};
//----------------get course users-------------------
export const getCourseUsers = () => (dispatch) => {
  dispatch({ type: GET_COURSE_USERS_LIST_LOADING });
  return axios
    .get(`${baseURL}/course/user`)
    .then((res) => {
      if (res.status === 200) {
        triggerNotifier({
          message: "Course users List Loaded",
          type: "success",
          duration: 1000,
          icon: "👏",
        });
        dispatch(actions.setCourseUsersList(res && res.data));
        return { success: true, data: res && res.data };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: GET_COURSE_USERS_LIST_LOADING });
      console.log("Course users List Error", error);
      triggerNotifier({
        message: "Course users List Error",
        type: "error",
        duration: 1000,
        icon: "⚠️",
      });
    });
};

//---------------Asign course to user -----------------
export const enrollCourseToUser =
  ({ courseId, courseUserId }) =>
    (dispatch) => {
      return axios
        .patch(`${baseURL}/course/${courseId}/enroll/${courseUserId}`)
        .then((res) => {
          if (res.status === 200) {
            triggerNotifier({
              message: "Enrol Course",
              type: "success",
              duration: 1000,
              icon: "👏",
            });
            return { success: true, data: res && res.data };
          } else {
            return { success: false };
          }
        })
        .catch((error) => {
          console.log("Enroll Course Error", error);
          triggerNotifier({
            message: "Enroll Course Error",
            type: "error",
            duration: 1000,
            icon: "⚠️",
          });
        });
    };
//-----------------Delete Asigned Course-------------------
export const deleteAsignedCourse =
  ({ courseId, courseUserId }) =>
    (dispatch) => {
      return axios
        .delete(`${baseURL}/course/${courseId}/enroll/${courseUserId}`)
        .then((res) => {
          if (res.status === 200) {
            triggerNotifier({
              message: "Course Removed",
              type: "success",
              duration: 1000,
              icon: "👏",
            });
            return { success: true };
          } else {
            return { success: false };
          }
        })
        .catch((error) => {
          console.log("delete course Error", error);
          triggerNotifier({
            message: "Delete Course Error",
            type: "error",
            duration: 1000,
            icon: "⚠️",
          });
        });
    };
//----------------- User List of the perticular Course--------------------
export const getUsersByCourse = (courseId) => (dispatch) => {
  dispatch({ type: GET_USERS_BY_COURSE_LIST_LOADING });
  return axios
    .get(`${baseURL}/course/${courseId}/enrollments`)
    .then((res) => {
      if (res.status === 200) {
        triggerNotifier({
          message: "Users List Loaded",
          type: "success",
          duration: 1000,
          icon: "👏",
        });
        dispatch(actions.setUsersByCourseList(res && res.data));
        return { success: true, data: res && res.data };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: GET_USERS_BY_COURSE_LIST_LOADING });
      console.log("Users List Error", error);
      triggerNotifier({
        message: "Users List Error",
        type: "error",
        duration: 1000,
        icon: "⚠️",
      });
    });
};
//-------------------Delete Course Users--------------
export const deleteCourseUsers = (id) => (dispatch) => {
  dispatch({ type: GET_COURSE_USERS_LIST_LOADING });
  return axios
    .delete(`${baseURL}/course/user/${id}`)
    .then((res) => {
      if (res.status === 200) {
        triggerNotifier({
          message: "Course users List Loaded",
          type: "success",
          duration: 1000,
          icon: "👏",
        });
        dispatch(actions.setCourseUsersList(res && res.data));
        return { success: true, data: res && res.data };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: GET_COURSE_USERS_LIST_LOADING });
      console.log("Course users List Error", error);
      triggerNotifier({
        message: "Course users List Error",
        type: "error",
        duration: 1000,
        icon: "⚠️",
      });
    });
};
import { useDispatch } from "react-redux"

import EventTimelineCard from "./EventTimelineCard"

const EventTimelineList = ({
  
  deleteEventTimeline,
  eventId,
  eventDetails,
}) => {
  
  const dispatch = useDispatch()

  const handleEventTimelineDelete = (timelineId) => {
    dispatch(deleteEventTimeline({
      eventId,
      timelineId,
    }))
  }

  return (
    <>
     {
           eventDetails?.timelines?.map((timeline, i) => (
            <EventTimelineCard classname='event-card' key={timeline._id} timeline={timeline} handleEventTimelineDelete={handleEventTimelineDelete}  eventId={eventId}  eventDetails={eventDetails}/>
          ))
      }
    </>
  )
}

export default EventTimelineList
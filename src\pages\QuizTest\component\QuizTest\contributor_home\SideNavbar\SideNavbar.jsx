import { Checkbox, Radio, Select } from "antd";
import { FaCheckCircle, FaDraftingCompass, FaListAlt } from "react-icons/fa";

const SideNavbar = ({ setQuizTestCategory }) => {
  const handleCategoryChange = (e) => {
    setQuizTestCategory(e.target.value);
  };

  return (
    <div className='side-navbar'>
      <div className='section-header'>
        <h5>Test Status</h5>
      </div>
      <ul className='status-list'>
        <li>
          <FaListAlt className='icon' />
          Ongoing (4)
        </li>
        <li>
          <FaCheckCircle className='icon' />
          Completed (4)
        </li>
        <li>
          <FaDraftingCompass className='icon' />
          Draft (2)
        </li>
      </ul>

      <hr />

      <div className='filter-section'>
        <div className='section-header'>
          <h5>Filters</h5>
        </div>
        <div className='filter-options'>
          <p className='filter-title'>Test Category</p>
          <div className='d-flex flex-column'>
            <Radio.Group
              onChange={handleCategoryChange}
              // value={selectedCategory} 
              defaultValue={""}
              className='d-flex flex-column'
            >
              <Radio value={""}>All</Radio>
              <Radio value={"quizTest"}>Quiz</Radio>
              <Radio value={"assessment"}>Assessment</Radio>
            </Radio.Group>
          </div>
        </div>

        <div className='dropdown-section'>
          <label>Role</label>
          <Select
            onChange={(value) => console.log(value)}
            className='w-100'
            placeholder='Search to Select'
            optionFilterProp='label'
            options={[
              {
                value: "admin",
                label: "Admin",
              },
              {
                value: "user",
                label: "User",
              },
            ]}
          />
        </div>

        <div className='dropdown-section'>
          <label>Creation Date</label>
          <Select
            onChange={(value) => console.log(value)}
            className='w-100'
            placeholder='Search to Select'
            optionFilterProp='label'
            options={[
              {
                value: "lastWeek",
                label: "Last Week",
              },
              {
                value: "lastMonth",
                label: "Last Month",
              },
            ]}
          />
        </div>

        <div className='filter-options'>
          <p className='filter-title'>Test Type</p>
          <div className='d-flex flex-column'>
            <Checkbox>Public</Checkbox>
            <Checkbox>Invited Only</Checkbox>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SideNavbar;

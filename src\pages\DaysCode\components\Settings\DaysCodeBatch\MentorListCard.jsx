import React, { useState } from "react";
import { useDispatch } from "react-redux";

import {
  deleteMentorFromBatch,
  editMentorIntoBatch,
  addParticipantTomentor,
} from "../../../actions";
import AddContentToBatchModal from "../../../../../components/sharedComponents/AddContentToBatchModal";
import DeleteModal from "../../../../../components/sharedComponents/DeleteModal";
import AddParticipantsToMentorModal from "./AddParticipantsToMentorModal";
import usePermissions from "../../../../../hooks/userPermission";

const MentorListCard = ({ mentor, batch }) => {

  const dispatch = useDispatch();
  const { hasPermission } = usePermissions();

  const [showModal, setShowModal] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [openDeleteModal, setOpenDeleteModal] = useState(false);
  const [showAddParticipantsModal, setShowAddParticipantsModal] =
    useState(false);

  const handleEditMentorIntoBatch = (mentorData) => {
    dispatch(editMentorIntoBatch({ id: mentorData.id, role: mentorData.role }));
  };

  const toggleDeleteModal = () => {
    setOpenDeleteModal(!openDeleteModal);
  };

  const handleDeleteFromBatch = () => {
    dispatch(deleteMentorFromBatch({ batch: batch, mentor: mentor._id }));
  };

  const handleModal = () => {
    setShowModal(true);
    setIsEdit(true);
  };

  const toggleModal = () => {
    setShowAddParticipantsModal(!showAddParticipantsModal);
  };

  const handleAssignParticipantsToMentor = (participant) => {
    console.log(participant.value);
    dispatch(
      addParticipantTomentor({
        mentor: mentor._id,
        batchParticipant: participant.value,
      }),
    );
  };
  return (
    <>
      <div className='row mx-0 my-2 border rounded-lg d-flex justify-content-between p-2 card-schadow'>
        <div className='col-9 px-1'>
          <div className='row mx-0'>
            {mentor?.mentor.name && (
              <p className=''>
                Name: <b>{mentor?.mentor.name}</b>
              </p>
            )}
          </div>
          <div className='row mx-0'>
            {mentor?.mentor.email && (
              <p className=''>
                Email: <b>{mentor?.mentor.email}</b>
              </p>
            )}
          </div>
          <div className='row mx-0'>
            {mentor?.role && (
              <p className=''>
                Role:{" "}
                <b>
                  {mentor?.role.charAt([0]).toUpperCase() +
                    mentor?.role.slice(1)}
                </b>
              </p>
            )}
          </div>
        </div>
        <div className='col-3 px-0 align-items-center d-flex justify-content-around'>
        { hasPermission("batch", "edit") && <i className='fas fa-edit  ml-md-5' onClick={() => handleModal()} />}
          <i
            className='fa-sharp fa-solid fa-user-plus'
            onClick={() => toggleModal()}
          />
         {hasPermission("batch", "delete") && <i className='fas fa-trash mr-md-5' onClick={() => setOpenDeleteModal(true)}/>}
        </div>
      </div>
      {showModal && (
        <AddContentToBatchModal
          showModal={showModal}
          setshowModal={setShowModal}
          batchContent={mentor}
          handleAddIntoBatch={handleEditMentorIntoBatch}
          type={"mentor"}
          isEdit={isEdit}
        />
      )}
      <AddParticipantsToMentorModal
        model={showAddParticipantsModal}
        toggle={toggleModal}
        mentorId={mentor._id}
        handleAssignParticipantsToMentor={handleAssignParticipantsToMentor}
        batch={batch}
        mentorParticipantsList={mentor?.participants}
      />
      <DeleteModal
        open={openDeleteModal}
        toggle={toggleDeleteModal}
        onSubmit={handleDeleteFromBatch}
        submitButtonName={"Delete Mentor"}
        message={" Are you sure want to delete this Mentor ?"}
        title={"Delete Mentor"}
      />
    </>
  );
};

export default MentorListCard;

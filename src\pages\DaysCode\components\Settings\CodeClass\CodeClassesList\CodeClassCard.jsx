import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Link } from "react-router-dom";
import moment from "moment";
import { HiOutlineDotsVertical } from "react-icons/hi";
import {
  DropdownItem,
  DropdownMenu,
  DropdownToggle,
  UncontrolledDropdown,
} from "reactstrap";

import {
  deleteCodeClass,
  getCodeBatches,
  addBatchInToClass,
  getDaysCodeMentors,
  addMentorInToClass,
  getCodeClasses,
  addAttendanceInToClass,
} from "../../../../actions";

import ToolTip from "../../../../../../components/sharedComponents/ToolTip";
import DeleteModal from "../../../../../../components/sharedComponents/DeleteModal";
import { timeConvert } from "../../../../../../components/utils";
import AssignBatchMenterModal from "./AssignBatchMenterModal";
import AttendanceModel from "./AttendanceModel";
const CodeClassCard = ({ codeClass }) => {
  const dispatch = useDispatch();
  const { codeBatches, mentorList} =
    useSelector((state) => state.dayscode) || {};

  const [openDeleteModal, setOpenDeleteModal] = useState(false);
  const [codeClassId, setCodeClassId] = useState(null);
  const [showModel, setShowModel] = useState(false);
  const [modelFor, setModelFor] = useState("");
  const [openAttendanceModal, setOpenAttendanceModal] = useState(false);

  useEffect(() => {
    dispatch(getCodeBatches({schedule:"all"}));
    dispatch(getDaysCodeMentors());
  }, [dispatch]);

  const toggleModal = (classId) => {
    setOpenDeleteModal(!openDeleteModal);
    setCodeClassId(classId);
  };

  const handleDeleteCodeClass = () => {
    dispatch(deleteCodeClass(codeClassId)).then((res) => {
      if (res) {
        setOpenDeleteModal(!openDeleteModal);
      }
    });
  };

  const openModal = (id, typestr) => {
    setModelFor(typestr);
    setCodeClassId(id); // Store the Class ID in state
    setShowModel(true); // Open the modal
  };
  // Function to close the modal
  const closeModal = () => {
    setShowModel(false);
    setCodeClassId(null); // Reset the Class ID
  };
  const handleAssignBatchesToClass = (codeClass, batch) => {
    dispatch(addBatchInToClass({ codeClass, batch })).then((res) => {
      if (res && res.success) {
        dispatch(getCodeClasses());
        closeModal();
      }
    });
  };

  const handleAssignMentorInToClass = (codeClass, mentor) => {
    dispatch(addMentorInToClass({ codeClass, mentor })).then((res) => {
      if (res && res.success) {
        dispatch(getCodeClasses());
        closeModal();
      }
    });
  };
  const toggleAttendanceModal = (codeClass) => {
    setCodeClassId(() => codeClass );
    setOpenAttendanceModal(!openAttendanceModal);
  };

  const handleAttendanceSubmission = (codeClass, presentStudents) => {
    alert(codeClass)
    dispatch(
      addAttendanceInToClass( codeClass, {participants: presentStudents })
    ).then((res) => {
      if (res && res.success) {
        setOpenAttendanceModal(!openAttendanceModal);
      }
    });
  };
  return (
    <>
      <div className="col-lg-3 col-md-4 col-sm-6 col-12 p-3">
        <div id="event-card" className="text-decoration-none event-card">
          <div className="mb-3">
            <img
              height="auto"
              width="auto"
              src={`https://gw.alipayobjects.com/zos/rmsportal/JiqGstEfoWAOHiTxclqi.png`}
              alt="avatar"
              loading="lazy"
              className="img-fluid mx-auto"
            />

            {
              <>
                <UncontrolledDropdown
                  setActiveFromChild
                  className="event-host-action-toggle bg-light"
                >
                  <DropdownToggle tag="a" className="">
                    <ToolTip title="Code Class action" placement="top">
                      <HiOutlineDotsVertical />
                    </ToolTip>
                  </DropdownToggle>
                  <DropdownMenu
                    className="dropdown-menu mt-3 card-schadow"
                    left='true'
                  >
                    <DropdownItem header>
                      <p className="m-0 p-0">Manage Class</p>
                    </DropdownItem>
                    <hr></hr>
                    <DropdownItem>
                      <Link to={`/admin/days_code/codeclass/${codeClass._id}`}>
                        <i className="far fa-edit" />
                        <span className="ml-2 ">Edit Class</span>
                      </Link>
                    </DropdownItem>
                    <DropdownItem>
                      <i className="fas fa-plus"></i>
                      <span
                        className="ml-2 "
                        onClick={() => openModal(codeClass._id, "batch")}
                      >
                        Add Batches
                      </span>
                    </DropdownItem>
                    <DropdownItem>
                      <i className="fas fa-plus"></i>
                      <span
                        className="ml-2 "
                        onClick={() => openModal(codeClass._id, "mentor")}
                      >
                        Add Mentors
                      </span>
                    </DropdownItem>
                    <DropdownItem>
                      <i className="fas fa-plus"></i>
                      <span
                        className="ml-2 "
                        onClick={() => toggleAttendanceModal(codeClass._id)}
                      >
                        Add Attendance
                      </span>
                    </DropdownItem>
                    <hr></hr>
                    <DropdownItem onClick={() => toggleModal(codeClass._id)}>
                      <span>
                        <i className="fas fa-trash delete-icon mr-2" />
                        Delete Class
                      </span>
                    </DropdownItem>
                  </DropdownMenu>
                </UncontrolledDropdown>
              </>
            }
            <span className="date-block">
              <h1>{moment(codeClass.dateTime).format("DD")}</h1>
              <small>{moment(codeClass.dateTime).format("MMMM")}</small>
            </span>
          </div>
          <h1 className="event-card-title px-3 pt-4 text-truncate">
            {codeClass.title}
          </h1>
          <div className="row m-0 event-card-footer">
            <div className="col-12 px-3">
              {/* <p>
              <i className="fas fa-laptop-code mr-2 icon" />
              {codeClass.description}
            </p> */}
              <p className="">
                <i className="far fa-calendar-alt mr-2 icon" />
                {moment(codeClass.dateTime).format("LL")} |{" "}
                {`${timeConvert(moment(codeClass.dateTime).format("HH:mm"))} `}
              </p>
              {/* <div className="row m-0 my-4">
              <div className="col-12 px-0">
                <span className="event-tag px-2">Python</span>
                <span className="event-tag px-2">Begineers</span>
                <span className="event-tag px-2">Online</span>
              </div>
            </div> */}
            </div>
          </div>
        </div>
      </div>
      <DeleteModal
        open={openDeleteModal}
        toggle={toggleModal}
        onSubmit={handleDeleteCodeClass}
        submitButtonName={"Delete"}
        message={" Are you sure want to delete this Class"}
        title={"Delete Code Class"}
      />
      <AssignBatchMenterModal
        type={modelFor}
        model={showModel}
        openModal={openModal}
        closeModal={closeModal}
        codeClassId={codeClassId}
        batchesList={codeBatches}
        mentorList={mentorList}
        codeClassBatches={codeClass.batches}
        codeClassMentors={codeClass.mentors}
        handleAssignBatchesToClass={handleAssignBatchesToClass}
        handleAssignMentorInToClass={handleAssignMentorInToClass}
      />
      <AttendanceModel
        model={openAttendanceModal}
        toggle={toggleAttendanceModal}
        handleAttendanceSubmission={handleAttendanceSubmission}
        codeClassId={codeClassId}
        codeClassPresentList={codeClass.attendance}
      />
    </>
  );
};

export default CodeClassCard;

import React, { useState } from "react";
import { useDispatch } from "react-redux";
import { useParams } from "react-router";
import { Form, FormGroup, Label, Input } from "reactstrap";
import { Combobox } from "react-widgets";
import { TiUserAdd } from "react-icons/ti";

import "bootstrap/dist/css/bootstrap.min.css";
import { addGuestUser } from "../../../actions/operations";
import { getOptions } from "../../../../../components/utils";
import CustomFormModal from "../../../../../components/sharedComponents/CustomFormModal";

const AddGuestModal = ({ isOpen, toggleModal, handleAfterEdit }) => {

  const dispatch = useDispatch();
  const { id } = useParams();

  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [organization, setOrganization] = useState("");
  const [email, setEmail] = useState("");
  const [phone, setPhone] = useState();
  const [profession, setProfession] = useState("");
  const [gender, setGender] = useState("");

   const professionOptions = [
    { name: "Student", value: "student" },
    { name: "Developer", value: "developer" },
  ];
  const genderOptions = [
    { name: "Male", value: "male" },
    { name: "Female", value: "female" },
  ];

  const handleAddGuest = () => {
    dispatch(
      addGuestUser({
        firstName,
        lastName,
        organization,
        email,
        event: id,
        status: "guest",
        phone,
        profession,
        gender,
      }),
    ).then(() => {
      handleAfterEdit();
    });
    setFirstName("");
    setLastName("");
    setOrganization("");
    setEmail("");
    toggleModal();
    setProfession("");
    setGender("");
    setPhone("");
  };

 
  return (
    <>
      <CustomFormModal
        isOpen={isOpen}
        title={"Add Guest User"}
        titleIcon={<TiUserAdd className='mr-2' fontSize={"25px"} />}
        onSubmitTitle={"Add Guest"}
        toggleModal={toggleModal}
        onSubmit={handleAddGuest}
        width='600px'
      >
        <Form>
          <FormGroup>
            <Label for='name'>First Name</Label>
            <Input
              type='text'
              name='firstName'
              id='firstName'
              placeholder='Enter First name'
              value={firstName}
              onChange={(e) => setFirstName(e.target.value)}
            />
          </FormGroup>
          <FormGroup>
            <Label for='name'>Last Name</Label>
            <Input
              type='text'
              name='lastName'
              id='lastName'
              placeholder='Enter Last name'
              value={lastName}
              onChange={(e) => setLastName(e.target.value)}
            />
          </FormGroup>
          <FormGroup>
            <Label for='organization'>Organization</Label>
            <Input
              type='text'
              name='organization'
              id='organization'
              placeholder='Enter organization'
              value={organization}
              onChange={(e) => setOrganization(e.target.value)}
            />
          </FormGroup>
          <div className='row'>
            <FormGroup className='col-6'>
              <label className='form-label'>Profession:</label>
              <Combobox
                data={getOptions(professionOptions, "profession")}
                dataKey={"profession"}
                textField='profession'
                placeholder={"profession"}
                value={profession}
                onChange={(value) => {
                  setProfession(value.value);
                }}
              />
            </FormGroup>
            <FormGroup className='col-6'>
              <label className='form-label'>Gender:</label>
              <Combobox
                data={getOptions(genderOptions, "gender")}
                dataKey={"gender"}
                textField='gender'
                placeholder={"gender"}
                value={gender}
                onChange={(value) => {
                  setGender(value.value);
                }}
              />
            </FormGroup>
          </div>
          <FormGroup>
            <Label for='organization'>Phone No.</Label>
            <Input
              type='number'
              name='number'
              id='number'
              placeholder='Enter Phone No.'
              value={phone}
              onChange={(e) => setPhone(e.target.value)}
            />
          </FormGroup>
          <FormGroup>
            <Label for='email'>Email</Label>
            <Input
              type='email'
              name='email'
              id='email'
              placeholder='Enter email'
              value={email}
              onChange={(e) => setEmail(e.target.value)}
            />
          </FormGroup>
        </Form>
      </CustomFormModal>
    </>
  );
};

export default AddGuestModal;

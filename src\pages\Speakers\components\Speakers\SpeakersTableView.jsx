import BootstrapTable from 'react-bootstrap-table-next'

import { getColumns } from './helpers'

const SpeakersTableView = ({
  handleDeleteSpeaker,
  handleEditSpeaker,
  speakersList,
}) => {

  let columns = getColumns(handleEditSpeaker, handleDeleteSpeaker)

  return (
    <>
      <h1>Speakers Table</h1>
      {speakersList?.length ?
        <div className="table table-responsive">
          <BootstrapTable
            keyField='_id'
            bordered={false}
            data={speakersList}
            columns={columns}
          />
        </div>
        :
        <span>No Speaker Available</span>
      }
    </>
  )
}

export default SpeakersTableView
import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";

import {
  addParticipantToBatch,
  deleteParticipantFromBatch,
  getBatchParticipants,
  getBatchMentors,
  addParticipantTomentor,
} from "../../../actions";
import ParticipantListTable from "../participantList/participantListTable/ParticipantListTable.jsx";
import AddContentToBatchModal from "../../../../../components/sharedComponents/AddContentToBatchModal";
import DeleteModal from "../../../../../components/sharedComponents/DeleteModal.jsx";
import AddMentorToParticipant from "./AddMentorToParticipant.jsx";
import BatchContentHeader from "./BatchContentHeader.jsx";
import NoDataBlock from "../../../../../components/sharedComponents/NoDataBlock.jsx";
import CustomLoader from "../../../../../components/sharedComponents/CustomLoader.jsx";


const ParticipantList = ({ batch, ParticipantsList, setActiveTab }) => {
  
  const dispatch = useDispatch();
  const { batchParticipantsList, batchMentorList, problemsListLoading } =
    useSelector((state) => state.dayscode) || {};

  const [showModal, setShowModal] = useState(false);
  const [tempParticipantDetail, setTempParticipantDetail] = useState();
  const [isEdit, setIsEdit] = useState(false);
  const [delid, setDelid] = useState();
  const [partId, setPartId] = useState(null);
  const [filterStatus, setFilterStatus] = useState(null);
  const [filterLimit, setFilterLimit] = useState(null);
  const [openModal, setOpenModal] = useState(false);
  const [openAssignMentorModal, setOpenAssignMentorModal] = useState(false);
  const [filterQuery, setFilterQuery] = useState("");
  const [participant, setParticipant] = useState([]);

  // useEffect(() => {
  //   const debounceTimeout = setTimeout(() => {
  //     dispatch(
  //       getBatchParticipants({
  //         batch: batch,
  //         limit: filterLimit,
  //         status: filterStatus,
  //         search: filterQuery,
  //       }),
  //     );
  //   }, 800);

  //   return () => clearTimeout(debounceTimeout);
  // }, [filterQuery, getBatchParticipants, filterLimit, filterStatus]);

  useEffect(() => {
    dispatch(
      getBatchParticipants({
        batch: batch,
        limit: filterLimit,
        status: filterStatus,
        search: filterQuery,
      }),
    );
  }, [filterQuery, getBatchParticipants, filterLimit, filterStatus]);

  const handleSelectedParticipant = (participantId) => {
    dispatch(
      addParticipantToBatch({ batch: batch, participant: participantId }),
    );
  };

  const toggleModal = () => {
    setOpenModal(!openModal);
  };

  const handleDeleteFromBatch = (participantId) => {
    setDelid(participantId);
    setOpenModal(!openModal);
  };
  const handleEventeDeleteModal = () => {
    toggleModal();
    dispatch(deleteParticipantFromBatch({ batch: batch, id: delid }));
  };

  const editAction = (row) => {
    setShowModal(true);
    setIsEdit(true);
    setTempParticipantDetail(row);
  };

  const toggleMentorAssignModal = (partId, participant) => {
    setPartId(partId);
    dispatch(getBatchMentors({ batch: batch }));
    setOpenAssignMentorModal(!openAssignMentorModal);
    setParticipant(participant);
  };

  const handleAssignMentorToParticipant = (mentor) => {
    dispatch(
      addParticipantTomentor({
        mentor: mentor.value,
        batchParticipant: partId,
      }),
    ).then(() => {
      setOpenAssignMentorModal(false);
    });
  };

  return (
    <>
      <BatchContentHeader
        batch={batch}
        handleSelectedParticipant={handleSelectedParticipant}
        tempParticipantDetail={tempParticipantDetail}
        type={"Participant"}
        filterLimit={filterLimit}
        filterQuery={filterQuery}
        filterStatus={filterStatus}
        setFilterStatus={setFilterStatus}
        setFilterLimit={setFilterLimit}
        setFilterQuery={setFilterQuery}
        setActiveTab={setActiveTab}
      />
      <>
        {problemsListLoading ? (
          <div className='row d-flex justify-items-center'>
            <div className='col-12 align-items-center text-center '>
              <CustomLoader />
            </div>
          </div>
        ) : batchParticipantsList.length > 0 ? (
          <div className='mx-4 mt-3 border-top'>
            <ParticipantListTable
              participants={batchParticipantsList}
              editAction={editAction}
              participantDeleteAction={handleDeleteFromBatch}
              toggleMentorAssignModal={toggleMentorAssignModal}
            />
          </div>
        ) : (
          <NoDataBlock />
        )}
      </>

      {showModal && (
        <AddContentToBatchModal
          batch={batch}
          type='participant'
          setshowModal={setShowModal}
          showModal={showModal}
          handleAddIntoBatch={editAction}
          batchContent={tempParticipantDetail}
          isEdit={isEdit}
          filterLimit={filterLimit}
          filterStatus={filterStatus}
        />
      )}

      <DeleteModal
        open={openModal}
        toggle={toggleModal}
        onSubmit={handleEventeDeleteModal}
        submitButtonName={"Delete"}
        message={" Are you sure want to delete this participant data"}
        title={"Delete Event"}
      />
      {openAssignMentorModal && (
        <AddMentorToParticipant
          model={openAssignMentorModal}
          toggle={toggleMentorAssignModal}
          handleAssignMentorToParticipant={handleAssignMentorToParticipant}
          batch={batch}
          batchMentorList={batchMentorList}
          participantId={partId}
          batchParticipantsMentorList={participant}
        />
      )}
    </>
  );
};

export default ParticipantList;

import React, { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Progress } from "reactstrap";
import { useParams } from "react-router-dom";

import {
  getUsersByCourse,
  deleteAsignedCourse,
} from "../../../actions/operations";
import CustomLoader from "../../../../../components/sharedComponents/CustomLoader";
import teacherProfile from "../../../../../assets/images/svg/workshop-page-testimonial/profile.png";

const CourseUserList = () => {
  const dispatch = useDispatch();
  const { id } = useParams();

  const { usersByCourseList, usersByCourseListLoading } = useSelector(
    (state) => state.course
  );

  useEffect(() => {
    dispatch(getUsersByCourse(id));
  }, [dispatch, id]);

  const removeCourse = (courseId, courseUserId) => {
    dispatch(deleteAsignedCourse({ courseId, courseUserId })).then((res) => {
      if (res && res.success) {
        alert("Course Removed");
        dispatch(getUsersByCourse(id));
      }
    });
  };

  return (
    <>
      <h3 className="my-3 text-center">Course Users</h3>
      <div className="container-fluid">
        {usersByCourseListLoading ? (
          <CustomLoader />
        ) : (
          <>
            {usersByCourseList &&
              usersByCourseList.map((courseUser, index) => {
                return (
                  <>
                    <div
                      className="row user_list mx-0 bg-light rounded p-3 mb-2"
                      key={courseUser?._id}
                    >
                      <div className="col-12 col-md-2 col-lg-2 col-sm-12">
                        {courseUser.user?.imgUrl ? (
                          <img
                            src={courseUser.user?.imgUrl}
                            alt="avtar"
                            className="img-fluid user-img rounded-circle shadow"
                            width="50"
                            height="50"
                          />
                        ) : (
                          <img
                            src={teacherProfile}
                            alt="logo"
                            width="50"
                            height="50"
                          />
                        )}
                      </div>
                      <div className="col-12 col-md-3 col-lg-3 col-sm-12 font-monospace mt-2">
                        {courseUser?.user?.firstName}
                      </div>
                      <div className="col-12 col-md-4 col-lg-4 col-sm-12 ">
                        45%
                        <Progress
                          style={{
                            height: "8px",
                            backgroundColor: "#c3c3c3",
                            width: "304px",
                          }}
                          value={45}
                        />
                      </div>
                      <div className="col-12 col-md-3 col-lg-3 col-sm-12">
                        <button
                          className="course_denie_button mb-3 bg-danger"
                          onClick={() => removeCourse(id, courseUser?._id)}
                        >
                          Remove
                        </button>
                      </div>
                    </div>
                  </>
                );
              })}
          </>
        )}
      </div>
    </>
  );
};

export default CourseUserList;

import React, { useState } from "react";
import SettingsDashboardNav from "./SettingsDashboardNav";

const LayoutContainer = ({ children, activeTab, setActiveTab }) => {
  const [isOpen, setIsOpen] = useState(true);
  return (
    <>
      <div className='d-flex days-setting-layout'>
        <div
          className={`${
            isOpen ? "mobile-reponsive-close" : "mobile-responsive-open"
          } d-flex m-0 p-0 side-nav align-item-center border-right border-bottom side-nav ${
            isOpen
              ? "text-center justify-content-center"
              : "justify-content-start"
          }`}
          style={{
            width: !isOpen ? "16%" : "6%",
            transition: "width 0.3s ease-in-out ",
          }}
        >
          <SettingsDashboardNav
            activeTab={activeTab}
            setActiveTab={setActiveTab}
            isOpen={isOpen}
            setIsOpen={setIsOpen}
          />
        </div>
        <div
          className={`w-100 children-props`}
          style={{
            paddingLeft: !isOpen ? "16%" : "6%",
            transition: "padding-left 0.3s ease-in-out",
          }}
        >
          {children}
        </div>
      </div>
    </>
  );
};

export default LayoutContainer;

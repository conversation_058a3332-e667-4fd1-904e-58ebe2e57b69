import { useSelector, useDispatch } from "react-redux";
import { useEffect } from "react";
import { getQuizTestInvitesList } from "../../../actions/operations";

const QuizTestSideNavbar = ({ setActiveTab, activeTab }) => {
  const dispatch = useDispatch();
  const { currentQuizTest, quizTestTakens, quizTestInvitesList } = useSelector(
    (state) => state.quizTest
  );

  return (
    <div className="quiz-test-sidenav">
      <div className="section">
        <h5>Candidates</h5>
        <ul>
          <li
            className={activeTab === "testTaken" ? "active-tab" : ""}
            onClick={() => setActiveTab("testTaken")}
          >
            Test Taken (
            {currentQuizTest?.completedBy?.length || quizTestTakens?.length || 0})
          </li>
          <li
            className={activeTab === "reviewPending" ? "active-tab" : ""}
            onClick={() => setActiveTab("reviewPending")}
          >
            Review Pending (0)
          </li>
          <li
            className={activeTab === "invited" ? "active-tab" : ""}
            onClick={() => setActiveTab("invited")}
          >
            Invited ({quizTestInvitesList?.length || 0})
          </li>
        </ul>
      </div>
      <div className="section">
        <h5>Analytics</h5>
        <ul>
          <li
            className={activeTab === "TestAnalytics" ? "active-tab" : ""}
            onClick={() => setActiveTab("TestAnalytics")}
          >
            Test Analytics
          </li>
          <li
            className={activeTab === "QuestionAnalytics" ? "active-tab" : ""}
            onClick={() => setActiveTab("QuestionAnalytics")}
          >
            Question Analytics
          </li>
          <li
            className={activeTab === "CandidateFeedback" ? "active-tab" : ""}
            onClick={() => setActiveTab("CandidateFeedback")}
          >
            Candidates Feedback
          </li>
        </ul>
      </div>
      <div className="section">
        <h5>Test Details</h5>
        <ul>
          <li
            className={activeTab === "overview" ? "active-tab" : ""}
            onClick={() => setActiveTab("overview")}
          >
            Overview
          </li>
          <li
            className={activeTab === "questions" ? "active-tab" : ""}
            onClick={() => setActiveTab("questions")}
          >
            Questions
          </li>
          <li
            className={activeTab === "emailTab" ? "active-tab" : ""}
            onClick={() => setActiveTab("emailTab")}
          >
            Email Templates
          </li>
        </ul>
      </div>
    </div>
  );
};

export default QuizTestSideNavbar;

import React from "react";
import { useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import { useFormik } from "formik";

import ToolTip from "../../../../../components/sharedComponents/ToolTip";
// import authImg from "../../../../../assets/images/svg/login.svg"
import { createCourseUser } from "../../../actions/operations";

const CourseUserWelcomDashboard = () => {

  const dispatch = useDispatch();
  const navigate = useNavigate();
  
  const currentUserInfo = JSON.parse(sessionStorage.getItem("currentUser") || localStorage.getItem("currentUser"));
  const formik = useFormik({
    initialValues: {
      userId: "",
    },
    onSubmit: (values) => {
      dispatch(
        createCourseUser({
          userId: currentUserInfo._id,   
        })
      ).then((res) => {
        if (res && res.success) {
          navigate("/courses");
        }

      });
    },
  });

  return (
    <>
      <div className="row mx-0">
        <div className="col-md-7 px-0 col-12 d-none d-md-flex justify-content-center align-items-center auth-left">
          {/* <img src={authImg} alt="" className="w-75 h-75"/> */}
        </div> 
        <div className="col-md-5 col-12 px-0 d-flex justify-content-center align-items-center auth-right"> 
          <div className="auth-container">
            <div>
              <h1 className="text-center">WELCOME TO DATACODE</h1>
              <p className="text-center">Course Discription</p>
            </div> 
            <form onSubmit={formik.handleSubmit}>
            <div className="form-group">
                  <input
                    type="hidden"
                    className="form-control"
                    id="userId"
                    name="userId"
                    value={currentUserInfo._id}
                  />
                </div>
              {/* course join button */}
              <div className="mt-4">
              <ToolTip title="Join For Courses" placement="top">
                <button type="submit" className="btn-login">
                 <span>Join Course</span>
                </button>
                </ToolTip>         
              </div>
            </form>
          </div>
        </div>
      </div>
    </>
  );
};

export default CourseUserWelcomDashboard;
const QuestionEditSidebar = ({
  maxScore,
  setMaxScore,
  negativeScore,
  setNegativeScore,
  difficultyLevel,
  setdifficultyLevel,
  isTimedQuestion,
  setisTimedQuestion,
  questionTag,
  setQuestionTag,
  duration,
  setDuration,
}) => {
  const handleDifficultyChange = (event) => {
    setdifficultyLevel(event.target.value);
  };
  return (
    <>
      <div className='row px-2 py-3 mx-0  border rounded-lg shadow-sm edit-sidebar'>
        <form>
          <div className='col-12'>
            <h6 className='font-weight-bold mb-0'>Difficulty Level</h6>
            <div className='form-group mt-0'>
              <div className='d-flex flex-column w-100'>
                <div className='custom-control custom-radio'>
                  <input
                    type='radio'
                    id='difficultyEasy'
                    name='difficulty'
                    value='easy'
                    className='custom-control-input'
                    checked={difficultyLevel === "easy"}
                    onChange={handleDifficultyChange}
                  />
                  <label
                    className='custom-control-label'
                    htmlFor='difficultyEasy'
                  >
                    Easy
                  </label>
                </div>

                <div className='custom-control custom-radio'>
                  <input
                    type='radio'
                    id='difficultyMedium'
                    name='difficulty'
                    value='medium'
                    className='custom-control-input'
                    checked={difficultyLevel === "medium"}
                    onChange={handleDifficultyChange}
                  />
                  <label
                    className='custom-control-label'
                    htmlFor='difficultyMedium'
                  >
                    Medium
                  </label>
                </div>

                <div className='custom-control custom-radio'>
                  <input
                    type='radio'
                    id='difficultyHard'
                    name='difficulty'
                    value='hard'
                    className='custom-control-input'
                    checked={difficultyLevel === "hard"}
                    onChange={handleDifficultyChange}
                  />
                  <label
                    className='custom-control-label'
                    htmlFor='difficultyHard'
                  >
                    Hard
                  </label>
                </div>
              </div>
            </div>
          </div>

          <div className='col-12 my-1'>
            <h6 className='font-weight-bold mb-0 '>Maximum Score</h6>
            <input
              type='text'
              value={maxScore}
              className='form-control p-3 text-center border rounded-lg'
              onChange={(e) => {
                setMaxScore(e.target.value);
              }}
              placeholder='Enter Maximum Score'
            />
          </div>

          <div className='col-12 my-3'>
            <h6 className='font-weight-bold mb-2'>Negative Score</h6>
            <input
              type='text'
              value={negativeScore}
              className='form-control p-3 text-center border rounded-lg'
              onChange={(e) => {
                setNegativeScore(e.target.value);
              }}
              placeholder='Enter Negative Score'
            />
            <p className='mt-2 text-muted '>
              This score will be deducted from the total score if the answer is
              incorrect.
            </p>
          </div>

          <div className='col-12 my-3'>
            <div className='form-check mx-0'>
              <input
                type='checkbox'
                checked={isTimedQuestion === true}
                onChange={() => setisTimedQuestion((prev) => !prev)}
                className='form-check-input'
                id='timedQuestionCheckbox'
              />
              <label
                className='form-check-label'
                htmlFor='timedQuestionCheckbox'
              >
                Make this a timed question
              </label>
            </div>

            {isTimedQuestion && (
              <div className='mt-3'>
                <input
                  type='text'
                  name='duration'
                  value={duration}
                  placeholder='Time duration (in min)'
                  onChange={(e) => setDuration(e.target.value)}
                  className='form-control text-center p-3 border rounded-lg'
                />
              </div>
            )}
          </div>

          <div className='col-12 my-3'>
            <h6 className='font-weight-bold mb-2'>Tags</h6>
            <input
              type='text'
              name='tags'
              value={questionTag}
              onChange={(e) => setQuestionTag(e.target.value)}
              className='form-control p-3 border rounded-lg'
              placeholder='Enter tags (comma separated)'
            />
          </div>
        </form>
      </div>
    </>
  );
};

export default QuestionEditSidebar;

import React, { useState } from "react";
import { But<PERSON>, Drawer } from "antd";
import { IoMenuOutline } from "react-icons/io5";

import SideNavbar from "../../../contributor_home/SideNavbar/SideNavbar";

const SideNavbar_Drawer = ({ setQuizTestCategory }) => {
  
  const [openMenu, setOpenMenu] = useState(false);

  return (
    <>
      <div className=''>
        <div className='menuIcon justify-content-center align-items-center text-center pt-4 mt-5 pb-0'>
          <Button
            onClick={() => setOpenMenu(true)}
            className='border-none align-items-center justify-content-center'
          >
            <IoMenuOutline />
          </Button>
        </div>
        <span className='headerMenu'>
          <SideNavbar setQuizTestCategory={setQuizTestCategory}></SideNavbar>
        </span>
        <Drawer
          placement='left'
          open={openMenu}
          width={"55%"}
          onClose={() => {
            setOpenMenu(false);
          }}
        >
          {/* <div className='pt-3 pe-1 text-end'>
            <Button className='btn' onClick={() => setOpenMenu(false)}>
              X
            </Button>
          </div> */}
          <SideNavbar></SideNavbar>
        </Drawer>
      </div>
    </>
  );
};

export default SideNavbar_Drawer;

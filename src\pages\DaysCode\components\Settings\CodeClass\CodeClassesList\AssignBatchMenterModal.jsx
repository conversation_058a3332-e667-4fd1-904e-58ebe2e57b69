import React, { useState } from "react";
import { useDispatch } from "react-redux";
import Combobox from "react-widgets/Combobox";

import {
  deleteBatchFromClass,
  deleteMentorFromClass,
} from "../../../../actions";
import CustomFormModal from "../../../../../../components/sharedComponents/CustomFormModal";

const AssignBatchMenterModal = ({
  type,
  model,
  openModal,
  closeModal,
  codeClassId,
  batchesList,
  mentorList,
  codeClassBatches,
  codeClassMentors,
  handleAssignBatchesToClass,
  handleAssignMentorInToClass,
}) => {
  const dispatch = useDispatch();

  const [batchId, setBatchId] = useState("");
  const [mentorId, setMentorId] = useState("");

  const getBatchesOptions = (batches) => {
    const options = [];
    batches.forEach((batch, i) => {
      options.push({ batch: batch.title, value: batch._id });
    });
    return options;
  };

  const getMentorsOptions = (mentorList) => {
    const options = [];
    mentorList &&
      mentorList.forEach((item, i) => {
        options.push({ mentor: item.mentor.name, value: item._id });
      });
    return options;
  };

  const onDeleteBatch = (batch) => {
    dispatch(deleteBatchFromClass({ batch, codeClass: codeClassId })).then(
      (res) => {
        if (res && res.success) {
          closeModal();
          window.location.reload();
        }
      },
    );
  };

  const onDeleteMentor = (mentor) => {
    dispatch(deleteMentorFromClass({ mentor, codeClass: codeClassId })).then(
      (res) => {
        if (res && res.success) {
          closeModal();
          window.location.reload();
        }
      },
    );
  };

  if (type === "batch") {
    return (
      <>
        <CustomFormModal
          isOpen={model}
          toggleModal={openModal}
          title={"Assign Batches To Class:"}
          onSubmit={() =>
            handleAssignBatchesToClass(codeClassId, batchId?.value)
          }
          onSubmitTitle={"Assign Batch"}
        >
          <div>
            {/* <label className='mr-4  form-label'>Assign Batches To Class:</label> */}
            <div className='border mt-3 rounded-lg card-schadow p-3'>
              {codeClassBatches &&
                codeClassBatches.map((batch) => (
                  <div className='p-2 d-flex align-items-center justify-content-between'>
                    <h6 className='mb-0' key={batch?._id}>
                      {batch?.title}
                    </h6>
                    <i
                      className='fas fa-times'
                      onClick={() => onDeleteBatch(batch?._id)}
                    />
                  </div>
                ))}
              <div className='mt-1'>
                <Combobox
                  data={getBatchesOptions(batchesList)}
                  dataKey={"value"}
                  textField='batch'
                  placeholder={"Select Batch"}
                  value={batchId}
                  onChange={(value) => setBatchId(value)}
                />
              </div>
            </div>
          </div>
        </CustomFormModal>
        {/* <Modal
          centered
          isOpen={model}
          toggle={openModal}
          className='text-center'
        >
          <ModalHeader
            className='d-flex justify-content-between align-items-center'
            toggle={closeModal}
          ></ModalHeader>
          <ModalBody>
            <div>
              <label className='mr-4  form-label'>
                Assign Batches To Class:
              </label>
              <div className='border mt-3 rounded-lg card-schadow p-3'>
                {codeClassBatches &&
                  codeClassBatches.map((batch, i) => (
                    <div className='p-2 d-flex align-items-center justify-content-between'>
                      <h6 className='mb-0' key={batch?._id}>
                        {batch?.title}
                      </h6>
                      <i
                        className='fas fa-times'
                        onClick={() => onDeleteBatch(batch?._id)}
                      />
                    </div>
                  ))}
                <div className='mt-1'>
                  <Combobox
                    data={getBatchesOptions(batchesList)}
                    dataKey={"value"}
                    textField='batch'
                    placeholder={"Select Batch"}
                    value={batchId}
                    onChange={(value) => setBatchId(value)}
                  />
                </div>
              </div>
            </div>
          </ModalBody>
          <ModalFooter className='d-flex justify-content-center align-items-center'>
            <Button
              color='success'
              onClick={() =>
                handleAssignBatchesToClass(codeClassId, batchId?.value)
              }
            >
              Asign Batch
            </Button>
          </ModalFooter>
        </Modal> */}
      </>
    );
  } else if (type === "mentor") {
    return (
      <>
        <CustomFormModal
          isOpen={model}
          toggleModal={openModal}
          title={"Assign Mentors To Class:"}
          onSubmit={() =>
            handleAssignMentorInToClass(codeClassId, mentorId?.value)
          }
          onSubmitTitle={"Assign Mentor"}
        >
          <div>
            {/* <label className='mr-4  form-label'>Assign Mentors To Class:</label> */}
            <div className='border mt-3 rounded-lg card-schadow p-3'>
              {codeClassMentors &&
                codeClassMentors.map((mentor) => (
                  <div className='p-2 d-flex align-items-center justify-content-between'>
                    <h6 className='mb-0' key={mentor.mentor?._id}>
                      {mentor.mentor?.name}
                    </h6>
                    <i
                      className='fas fa-times'
                      onClick={() => onDeleteMentor(mentor?._id)}
                    />
                  </div>
                ))}
              <div className='mt-1'>
                <Combobox
                  data={getMentorsOptions(mentorList)}
                  dataKey={"value"}
                  textField='mentor'
                  placeholder={"Select mentor"}
                  value={mentorId}
                  onChange={(value) => setMentorId(value)}
                />
              </div>
            </div>
          </div>
        </CustomFormModal>
        {/* <Modal
          centered
          isOpen={model}
          toggle={openModal}
          className='text-center'
        >
          <ModalHeader
            className='d-flex justify-content-between align-items-center'
            toggle={closeModal}
          ></ModalHeader>
          <ModalBody>
            <div>
              <label className='mr-4  form-label'>
                Assign Mentors To Class:
              </label>
              <div className='border mt-3 rounded-lg card-schadow p-3'>
                {codeClassMentors &&
                  codeClassMentors.map((mentor, i) => (
                    <div className='p-2 d-flex align-items-center justify-content-between'>
                      <h6 className='mb-0' key={mentor.mentor?._id}>
                        {mentor.mentor?.name}
                      </h6>
                      <i
                        className='fas fa-times'
                        onClick={() => onDeleteMentor(mentor?._id)}
                      />
                    </div>
                  ))}
                <div className='mt-1'>
                  <Combobox
                    data={getMentorsOptions(mentorList)}
                    dataKey={"value"}
                    textField='mentor'
                    placeholder={"Select mentor"}
                    value={mentorId}
                    onChange={(value) => setMentorId(value)}
                  />
                </div>
              </div>
            </div>
          </ModalBody>
          <ModalFooter className='d-flex justify-content-center align-items-center'>
            <Button
              color='success'
              onClick={() =>
                handleAssignMentorInToClass(codeClassId, mentorId?.value)
              }
            >
              Asign mentor
            </Button>
          </ModalFooter>
        </Modal> */}
      </>
    );
  }
};

export default AssignBatchMenterModal;

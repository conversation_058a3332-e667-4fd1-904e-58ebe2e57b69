
import React from "react";
import { FaCopy } from "react-icons/fa6";
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { vscDarkPlus } from "react-syntax-highlighter/dist/esm/styles/prism";

import { copyToClipboard } from "../../../../../../../components/utils/copyToClipboard";

const CodeBlockPreview = ({ item }) => {
  return (
    <div>
      {!item.hasOwnProperty("payload") || item.payload.code === "" ? (
        <div className="d-flex justify-content-center py-2">
          <p>There is nothing to preview yet.</p>
        </div>
      ) : (
        <div className="code-container">
          <SyntaxHighlighter
            language={item.payload.language}
            style={vscDarkPlus}
            className="syntax-highlighter">
            {item.payload.code}
          </SyntaxHighlighter>
          <button
            className="copy-button"
            onClick={() => copyToClipboard(item.payload.code)}
          >
            <FaCopy />
          </button>
        </div>
      )}
    </div>
  );
};

export default CodeBlockPreview;



import React, { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import { RiArrowDropDownLine, RiArrowDropUpLine } from "react-icons/ri";

import EventCard from "../EventsList/EventCard";
import { getEventsList } from "../../actions";
import EventStudioSideNav from "./EventStudioSideNav";
import CustomLoader from "../../../../components/sharedComponents/CustomLoader";

const Events = () => {
  const dispatch = useDispatch();

  const { eventsList = [], eventsListLoading = false, } = useSelector(({ event }) => event) || {};

  const [showUpcoming, setShowUpcoming] = useState(true);
  const [showPast, setShowPast] = useState(true);

  useEffect(() => {
    dispatch(getEventsList());
  }, [dispatch]);

  const toggleUpcoming = () => {
    setShowUpcoming(!showUpcoming);
  };
  const togglePast = () => setShowPast(!showPast);

  return (
    <>
      <div className="row mx-0">
        <div className="col-2 border-right">
          <EventStudioSideNav />
        </div>

        <div className="col-10 event-main">
          <div className="text-center m-3 p-5 eventstudiocard">
            <h1>Event Studio Page</h1>
            <p>Events are community power</p>
          </div>
          {eventsListLoading ? (
            <div
              className="w-100 d-flex justify-content-center align-items-center"
              
            >
              <CustomLoader />
            </div>
          ) : (
            <>
              <div
                className="d-flex justify-content-between align-items-center m-3 p-4 border text-dark"
                onClick={toggleUpcoming}
                style={{ cursor: "pointer" }}
              >
                <h2 className="mb-0">Upcoming Events</h2>
                {showUpcoming ? (
                  <RiArrowDropUpLine size={30} />
                ) : (
                  <RiArrowDropDownLine size={30} />
                )}
              </div>
              {showUpcoming && (
                <div className="d-flex flex-wrap justify-content-left mx-4">
                  {eventsList &&
                    eventsList
                      ?.slice(0, 3)
                      .map((eventdata, i) => (
                        <EventCard key={i} data={eventdata} />
                      ))}
                </div>
              )}

              {/* past  Event cards */}
              <div
                className="d-flex justify-content-between align-items-center m-3 p-4 border text-dark"
                onClick={togglePast}
                style={{ cursor: "pointer" }}
              >
                <h2 className="mb-0">Past Events</h2>
                {showPast ? (
                  <RiArrowDropUpLine size={30} />
                ) : (
                  <RiArrowDropDownLine size={30} />
                )}
              </div>

              {showPast && (
                <div className="d-flex flex-wrap justify-content-left mx-4  ">
                  {eventsList?.slice(3, 6).map((eventdata, i) => (
                    <EventCard key={i} data={eventdata} />
                  ))}
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </>
  );
};

export default Events;

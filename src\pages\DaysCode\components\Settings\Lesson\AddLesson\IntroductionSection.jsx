import React, { useEffect, useState } from 'react'
import { reduxForm } from 'redux-form'

import TextEditor from '../../../../../../components/sharedComponents/TextEditor'

const IntroductionSection = ({
  reset, handleSubmit, submitting,
  id, lessonDetails, editLesson
}) => {

  const [introductionText, getIntroductionText] = useState('<p> </p>')
  const [isEdit, setEdit] = useState(false)

  useEffect(() => {
    setEdit(true)
    getIntroductionText(lessonDetails && lessonDetails.introduction)
  }, [])

  const handleIntroductionText = (text) => {
    getIntroductionText(text)
  }

  const onSubmit = (values) => {
    const lesson = { ...values }
    lesson['introduction'] = introductionText
    if (id) {
      lesson['id'] = id
      editLesson(lesson)
    }
    reset('edit-lesson')
  }

  return (
    <>
      <form className="p-3 px-1" onSubmit={handleSubmit(onSubmit)}>
        <small>
          Lorazepam, sold under the brand name <PERSON>ivan among others, is a benzodiazepine medication. It is used to treat anxiety disorders, trouble sleeping, severe agitation, active seizures including status epilepticus, alcohol withdrawal, and chemotherapy-induced nausea and vomiting
        </small>
        <div className="my-3 lesson-texteditor">
          <label className="form-label text-left pt-5">Introduction {`(${introductionText && introductionText.length - 8})`}</label>
          <div className="mb-3 border ">
            {(isEdit && lessonDetails && lessonDetails.introduction) &&
              <TextEditor handleTextEditor={handleIntroductionText} text={lessonDetails.introduction ? introductionText : ''} />
            }
            {(!isEdit) &&
              <TextEditor handleTextEditor={handleIntroductionText} text={lessonDetails.introduction ? introductionText : ''} />
            }
          </div>
        </div>
        <div className="row my-4">
          <div className="col-12 text-right">
            <button type="submit" className="btn custom-border-capsule-button" disabled={submitting}>
              <span>Save</span>
            </button>
          </div>
        </div>
      </form>
    </>
  )
}

export default reduxForm({
  form: 'edit-lesson',
})(IntroductionSection);

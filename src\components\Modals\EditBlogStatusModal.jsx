import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";

import { editLesson } from "../../pages/Courses/actions/operations";
import CustomFormModal from "../sharedComponents/CustomFormModal";

const EditBlogStatusModal = ({ setshowModal, showModal, type }) => {

  const dispatch = useDispatch();
  const [selectedOptions, setSelectedOptions] = useState({
    approve: false,
    disapprove: false,
  });
  const { lessonDetails } = useSelector(
    (state) => state.course,
  );
  
  const [status, setStatus] = useState();

  const handleRadioChange = (event) => {
    setStatus(event.target.value);
  };

  const handleCheckboxChange = (e) => {
    const { value } = e.target;
    setSelectedOptions({
      approve: value === "approve",
      disapprove: value === "disapprove",
    });
  };

  useEffect(() => {
    if (type === "changeStatus") {
      setStatus(lessonDetails?.status);
    } else {
      if (lessonDetails?.isApproved) {
        setSelectedOptions({
          approve: true,
          disapprove: false,
        });
      } else {
        setSelectedOptions({
          approve: false,
          disapprove: true,
        });
      }
    }
  }, [lessonDetails, type]);

  const onclick = () => {
    if (type === "changeStatus") {
      const data = {
        ...lessonDetails,
        status: status,
      };
      dispatch(editLesson(data));
    } else if (type === "approve") {
      const isApproved = selectedOptions.approve;
      const data = {
        ...lessonDetails,
        isApproved: isApproved,
      };
      dispatch(editLesson(data));
    }
    setshowModal(false);
  };

  return (
    <div>
      <CustomFormModal
        isOpen={showModal}
        toggleModal={() => setshowModal(false)}
        onSubmit={onclick}
        title={"Lesson Name"}
        onSubmitTitle={"Save"}
      >
        <>
          {type === "changeStatus" ? (
            <div className='mt-3 mx-3'>
              <label className='form-label'>Change Status:</label>
              <div className='d-flex'>
                <div className='form-check'>
                  <input
                    className='form-check-input'
                    type='radio'
                    id='public'
                    value='public'
                    checked={status === "public"}
                    onChange={handleRadioChange}
                  />
                  <label className='form-check-label' htmlFor='public'>
                    Public
                  </label>
                </div>
                <div className='form-check'>
                  <input
                    className='form-check-input'
                    type='radio'
                    id='draft'
                    value='draft'
                    checked={status === "draft"}
                    onChange={handleRadioChange}
                  />
                  <label className='form-check-label' htmlFor='draft'>
                    Draft
                  </label>
                </div>
              </div>
            </div>
          ) : (
            <div className='mt-3 mx-3'>
              <label className='form-label'>Change Status:</label>
              <div className='d-flex'>
                <div className='form-check'>
                  <input
                    className='form-check-input'
                    type='radio'
                    id='approve'
                    value='approve'
                    checked={selectedOptions.approve}
                    onChange={handleCheckboxChange}
                  />
                  <label className='form-check-label' htmlFor='approve'>
                    Approve
                  </label>
                </div>
                <div className='form-check'>
                  <input
                    className='form-check-input'
                    type='radio'
                    id='disapprove'
                    value='disapprove'
                    checked={selectedOptions.disapprove}
                    onChange={handleCheckboxChange}
                  />
                  <label className='form-check-label' htmlFor='disapprove'>
                    Disapprove
                  </label>
                </div>
              </div>
            </div>
          )}
        </>
      </CustomFormModal>

      {/* <Modal centered isOpen={showModal} toggle={() => setshowModal(false)}>
        <ModalHeader toggle={() => setshowModal(false)}>
          <h2>Lesson Name</h2>
        </ModalHeader>
        <ModalBody>
          {lessonDetailsLoading ? (
            <CustomLoader />
          ) : (
            <>
              {type === "changeStatus" ? (
                <div className='mt-3 mx-3'>
                  <label className='form-label'>Change Status:</label>
                  <div className='d-flex'>
                    <div className='form-check'>
                      <input
                        className='form-check-input'
                        type='radio'
                        id='public'
                        value='public'
                        checked={status === "public"}
                        onChange={handleRadioChange}
                      />
                      <label className='form-check-label' htmlFor='public'>
                        Public
                      </label>
                    </div>
                    <div className='form-check'>
                      <input
                        className='form-check-input'
                        type='radio'
                        id='draft'
                        value='draft'
                        checked={status === "draft"}
                        onChange={handleRadioChange}
                      />
                      <label className='form-check-label' htmlFor='draft'>
                        Draft
                      </label>
                    </div>
                  </div>
                </div>
              ) : (
                <div className='mt-3 mx-3'>
                  <label className='form-label'>Change Status:</label>
                  <div className='d-flex'>
                    <div className='form-check'>
                      <input
                        className='form-check-input'
                        type='radio'
                        id='approve'
                        value='approve'
                        checked={selectedOptions.approve}
                        onChange={handleCheckboxChange}
                      />
                      <label className='form-check-label' htmlFor='approve'>
                        Approve
                      </label>
                    </div>
                    <div className='form-check'>
                      <input
                        className='form-check-input'
                        type='radio'
                        id='disapprove'
                        value='disapprove'
                        checked={selectedOptions.disapprove}
                        onChange={handleCheckboxChange}
                      />
                      <label className='form-check-label' htmlFor='disapprove'>
                        Disapprove
                      </label>
                    </div>
                  </div>
                </div>
              )}
            </>
          )}
        </ModalBody>
        <ModalFooter className='d-flex justify-content-center align-items-center'>
          <Button color='success' onClick={onclick}>
            SAVE
          </Button>
        </ModalFooter>
      </Modal> */}
    </div>
  );
};

export default EditBlogStatusModal;

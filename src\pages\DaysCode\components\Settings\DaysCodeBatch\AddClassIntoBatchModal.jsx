import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON>, <PERSON>dalBody } from "reactstrap";
import { Combobox } from "react-widgets";
import { <PERSON><PERSON> } from "antd";
import { RiDeleteBack2Fill } from "react-icons/ri";

import usePermissions from "../../../../../hooks/userPermission";
import { addBatchInToClass, deleteBatchFromClass, getCodeBatchDetails, getCodeBatches} from "../../../actions";
import CustomLoader from "../../../../../components/sharedComponents/CustomLoader";


const AddClassIntoBatchModal = ({ showModal, setShowModal, toggleModal, id, cc }) => {
  
  const dispatch = useDispatch();
  const { codeBatchDetails, codeBatchesLoading, codeClassesLoading } = useSelector((state) => state.dayscode);
  const { hasPermission } = usePermissions();

  const [codeClassId, setCodeClassId] = useState(null);

  const getClassesOptions = (codeClasses) => {
    const options = codeClasses?.map((item, i) => ({
      codeClass: item.title,
      value: item._id,
    }));
    
    return options;
  };

  useEffect(() => {
    dispatch(getCodeBatchDetails(id));
  })

  const handleAddClass = (codeClass, batch) => {
    dispatch(addBatchInToClass({ codeClass, batch })).then((res) => {
      if (res && res.success) {
        getCodeBatchDetails(batch);
        getCodeBatches({
          schedule: 'all',
        })
        toggleModal();
      }
    });
  }

  const handleDeleteClass = (batch, codeClass) => {
    dispatch(deleteBatchFromClass({ batch, codeClass })).then((res) => {
      if (res) {
        toggleModal();
      }
    });
  };

  return (
    <div >
      <Modal centered isOpen={showModal} toggle={toggleModal}>
        <ModalHeader >
          <div className="modal1-header">Add Class Into Batch </div>
        </ModalHeader>
        <hr />
        <ModalBody>

          {codeBatchesLoading && codeClassesLoading ? (<><CustomLoader /></>) : (
            <>
              <ul>
                {codeBatchDetails && codeBatchDetails.codeClasses?.length ? (<>
                  <div className="mb-2">Already Existing Classes In The Batch</div> <hr />
                  {codeBatchDetails.codeClasses.map((item) => (
                    <div className="d-flex row ">
                      <li className="col-7 d-flex mx-1 justify-content-start">{item.title}</li>
                      <span className="col-3 d-flex justify-content-end">
                        {hasPermission("batch", "delete") && <button onClick={() => handleDeleteClass(codeBatchDetails._id, item._id)}><RiDeleteBack2Fill /> </button>}
                      </span>
                    </div>
                  )
                  )
                  }
                </>) :
                  (<><div>There are no Classes .Add classes from below</div></>)}
              </ul>
              <div className="mt-1">
                <Combobox
                  data={getClassesOptions(cc)}
                  dataKey={"value"}
                  textField="codeClass"
                  placeholder={"Select Code Class"}
                  value={codeClassId}
                  onChange={(value) => setCodeClassId(value)}
                />
              </div>
            </>
          )}

        </ModalBody>
        <ModalFooter className="d-flex justify-content-center align-items-center">
          <Button
            color="success"
            onClick={() => handleAddClass(codeClassId.value, id)}
          >
            Add Class
          </Button>
        </ModalFooter>
      </Modal>
    </div>
  );
}

export default AddClassIntoBatchModal;
import { useState } from "react";
import { useDispatch, useSelector } from "react-redux";

import CreateQuizModal from "../../contributor_home/create_quiz_header/CreateQuizModal";
import OtherDetailsForm from "./OtherDetailsForm";
import { EditQuizTest } from "../../../../actions";

const OtherDetailsSection = () => {

  const dispatch = useDispatch();

  const [openModal, setOpenModal] = useState(false);
  const [isEditable, setIsEditable] = useState(false);

  const toggleModal = () => {
    setOpenModal(!openModal);
  };

  const closeModel = () => {
    setOpenModal(false);
  };

  const handleOtherDeatils = (formData) => {
    dispatch(EditQuizTest(formData));
  };

  const OtherDetails = {
    test_access: "on",
    test_link: "https://goggle.com",
    practice_test_link: "http://facebook.com",
    _id: "12345",
  };

  return (
    <>
      <div className='col-12 p-3  other-section'>
        <div className='row mx-0'>
          <div className='col-lg-12 d-flex align-items-baseline justify-content-between'>
            <h5>OtherDetails</h5>
            <a
              className='px-2 d-flex align-items-center'
              onClick={() => setOpenModal(true)}
            >
              <i className='fa fa-pencil mr-2' aria-hidden='true'></i>
              <span className='fs-1'>Edit</span>
            </a>
          </div>
          <OtherDetailsForm
            isEditable={isEditable}
            OtherDetails={OtherDetails}
          />
        </div>
      </div>
      <CreateQuizModal
        open={openModal}
        toggle={toggleModal}
        onSubmit={handleOtherDeatils}
        type={"OtherDetails"}
      >
        <OtherDetailsForm
          isEditable={openModal}
          OtherDetails={OtherDetails}
          toggle={toggleModal}
          closeModel={closeModel}
        />
      </CreateQuizModal>
    </>
  );
};

export default OtherDetailsSection;

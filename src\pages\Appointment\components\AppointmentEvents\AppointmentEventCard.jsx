import { useState } from "react";
import { <PERSON> } from "react-router-dom";
import { Dropdown, Space } from "antd";
import { Switch } from "antd";
import { FaRegCopy } from "react-icons/fa";
import { CiSettings } from "react-icons/ci";
import { IoMdArrowDropdown } from "react-icons/io";
import { LuPen } from "react-icons/lu";
import { FaRegNoteSticky } from "react-icons/fa6";
import { PiCopySimpleBold } from "react-icons/pi";
import { RiDeleteBinLine } from "react-icons/ri";

import ToolTip from "../../../../components/sharedComponents/ToolTip";

const AppointmentEventCard = ({ appointment, handleDeleteEvent }) => {

  const [copySuccess, setCopySuccess] = useState(false);
  const items = [
    {
      label: <Link to={`/appointment/event/manage/${appointment._id}`}>Edit</Link>,
      key: "0",
      icon: <LuPen />,
    },
    {
      label: <div>Add internal note</div>,
      key: "1",
      icon: <FaRegNoteSticky />,
    },
    {
      label: <div>Clone</div>,
      key: "2",
      icon: <PiCopySimpleBold />,
    },
    {
      label: <div onClick={() => handleDeleteEvent(appointment._id)}>Delete</div>,
      key: "3",
      icon: <RiDeleteBinLine />,
    },
    {
      type: "divider",
    },
    {
      key: "4",
      label: (
        <div className="d-flex justify-content-between card-dropdown-footer">
          <span>On/Off</span>
          <Switch defaultChecked onChange={(checked) => console.log("working")} />
        </div>
      ),
    },
  ];

  const handleCopyLink = () => {
    navigator.clipboard.writeText(window.location.href)
      .then(() => {
        setCopySuccess(true); // Set copy success message
      })
      .catch(err => console.error('Failed to copy:', err));
  }

  return (
    <>
      <div className="row mx-0 appointment-event-card">
        <div className="col-12 p-0">
          <div className="appointment-card-header d-flex justify-content-end p-2">
            <ToolTip title="Setting" placement="bottom">
              <CiSettings className="settings-icon" />{" "}
            </ToolTip>
            <Dropdown
              menu={{
                items,
              }}
              placement="bottom"
              trigger={["click"]}
            >
              <div onClick={(e) => e.preventDefault()}>
                <Space>
                  <IoMdArrowDropdown className="settings-icon" />
                </Space>
              </div>
            </Dropdown>
          </div>
          <div className="appointment-card-body px-3">
            <h5 className="pb-1">{appointment.title}</h5>
            <Link to={`/appointment/${appointment._id}`}>
              <ToolTip title="Booking page" placement="left">
                <p className="pb-4 view-booking-link" id="book-page">
                  View Booking Page
                </p>
              </ToolTip>
            </Link>
          </div>
          <div className="footer-border"></div>
          <div className="appointment-card-footer p-3">
            <ToolTip title={copySuccess ? "Copied!" : "Copy link"} placement="bottom">
              <FaRegCopy className="footer-icon me-2" />
              <span className="footer-copy-button" id="copy-link" onClick={() => (handleCopyLink())}>
                Copy Link
              </span>
            </ToolTip>
            <ToolTip message="Copy Link" placement="bottom" id="copy-link" />
            <ToolTip title="Share" placement="bottom">
              <div className="footer-button px-4">Share</div>
            </ToolTip>
          </div>
        </div>
      </div>
    </>
  );
};

export default AppointmentEventCard;

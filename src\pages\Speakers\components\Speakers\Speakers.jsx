import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Link, useNavigate } from "react-router-dom";
import { FaThLarge } from "react-icons/fa";
import { FaList } from "react-icons/fa";

import { Pagination } from "@akshayDatacode/datacode-ui";
import { getSpeakers, deleteSpeaker } from "../../actions";
import CustomLoader from "../../../../components/sharedComponents/CustomLoader";
import DeleteModal from "../../../../components/sharedComponents/DeleteModal";
import ToolTip from "../../../../components/sharedComponents/ToolTip";
import SpeakersGridView from "./SpeakersGridView";
import SpeakersTableView from "./SpeakersTableView";

const Speakers = () => {

  const dispatch = useDispatch();
  const navigate = useNavigate();
  
  const { speakersList, speakersCount, speakersListLoading } =
    useSelector(({ speaker }) => speaker) || {};

  const [view, setView] = useState("grid");
  const [selected, setSelected] = useState(0);
  const [openModal, setOpenModal] = useState(false);
  const [tempDeleteId, setTempDeleteId] = useState();

  useEffect(() => {
    dispatch(getSpeakers({ page: selected, limit: speakersPerPage }));
    window.scrollTo(0, 0);
  }, []);

  // Pagination
  const speakersPerPage = 5;
  const pageCount = Math.ceil(speakersCount / speakersPerPage);
  const changePage = ({ selected }) => {
    setSelected(selected);
    dispatch(getSpeakers({ page: selected, limit: speakersPerPage }));
  };

  const handleEditSpeaker = (id) => {
    navigate(`/speaker/${id}`);
  };

  // Delete Speaker
  const toggleModal = () => {
    setOpenModal(!openModal);
  };

  const handleSpeakerDeleteModal = () => {
    dispatch(deleteSpeaker(tempDeleteId)).then((res) => {
      if (res) {
        setOpenModal(!openModal);
        dispatch(getSpeakers({ page: selected, limit: speakersPerPage }));
      }
    });
  };
  const handleDeleteSpeaker = (id) => {
    setTempDeleteId(id);
    setOpenModal(!openModal);
  };

  return (
    <>
      <div className="row mx-0">
        <div className="col-12 nav-speaker-header">
          <h3 className="py-2 mb-0">Community Speakers</h3>
          <h3 className="mb-0">{speakersCount}</h3>
          <Link to="/speaker/new">
            <ToolTip title="Add New Speaker" placement="bottom">
              <button className="add-speaker-btn">Add Speaker</button>
            </ToolTip>
          </Link>
        </div>
      </div>
      <div className="row mx-0">
        <div className="col-12 nav-speaker-icon">
          <ToolTip title="Speaker Details" placement="bottom">
            <FaThLarge
              className={view === "grid" ? "active-icon" : "icon"}
              onClick={() => setView("grid")}
            />
          </ToolTip>
          <ToolTip title="Speaker List" placement="bottom">
            <FaList
              className={view === "list" ? "active-icon" : "icon"}
              onClick={() => setView("list")}
              id="Speaker-list"
            />
          </ToolTip>
        </div>
      </div>
      <div className="row mx-0">
        <div className="col-12 p-3">
          {speakersListLoading ? (
            <CustomLoader />
          ) : (
            <>
              {view === "list" ? (
                <SpeakersTableView
                  handleDeleteSpeaker={handleDeleteSpeaker}
                  handleEditSpeaker={handleEditSpeaker}
                  speakersList={speakersList}
                />
              ) : (
                <>
                  <SpeakersGridView
                    handleEditSpeaker={handleEditSpeaker}
                    handleDeleteSpeaker={handleDeleteSpeaker}
                    speakersList={speakersList}
                  />
                </>
              )}
            </>
          )}
        </div>
      </div>
      {speakersCount > 0 && (
        <div className="d-flex justify-content-center">
          {/* <ReactPaginate
            previousLabel={"Prev"}
            nextLabel={"Next"}
            pageCount={pageCount}
            onPageChange={changePage}
            containerClassName={"pagination"}
            pageLinkClassName={"page"}
            previousLinkClassName={"previousBtn"}
            nextLinkClassName={"nextBtn"}
            disabledClassName={"disabled"}
            activeClassName={"activePage"}
            activeLinkClassName={"activeClassLink"}
          /> */}
          <Pagination pageCount={pageCount} changePage={changePage} />
        </div>
      )}
      <DeleteModal
        open={openModal}
        toggle={toggleModal}
        onSubmit={handleSpeakerDeleteModal}
        submitButtonName={"Delete Speaker"}
        message={"Are you sure want to delete speaker"}
      />
    </>
  );
};

export default Speakers;

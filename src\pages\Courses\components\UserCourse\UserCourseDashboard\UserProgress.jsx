import React, { useEffect, useState } from "react";
import { TbBadge, TbBadges, TbBadgeOff } from "react-icons/tb";
import { FaClock } from "react-icons/fa";
import { IoMdCheckbox } from "react-icons/io";
import { FaRegCircleCheck } from "react-icons/fa6";
import { GoClock } from "react-icons/go";

const UserProgress = ({ userProgress, coursesList }) => {

  const [completedCoursesCount, setCompletedCoursesCount] = useState(0);

  useEffect(() => {
    // Filter courses with 100% progress
    const completedCourses =
      coursesList && coursesList?.filter((course) => course?.progress === 100);
    // Set the count to state
    setCompletedCoursesCount(completedCourses?.length);
  }, [coursesList]);

  return (
    <div className=" px-0 ">
      <div className="row  ">
        <div className="col-lg-7 col-md-7 col-sm-12 col-12 px-0 border border-light  rounded-3 user-progress   ">
          <div className="row mx-0 progress-detail h-auto w-100">
            <div className="col-lg-6 col-md-6 col-sm-12  col-12 p-3">
              <div className="progress-badge h-100  w-100">
                <div className="row ">
                  <div className="col-md-2 col-lg-2 col-12 col-sm-12">
                    <TbBadges className=" text-warning  bg-dark badge-icon mt-0 mb-3" />
                  </div>
                  <div className="col-md-10 col-lg-10 col-12 col-sm-12">
                    <div className="badge-detail">
                      <h5 className="f-wfw-bold m-0">
                        {Math.round(userProgress?.progressPercent) < 30
                          ? "Earn Badge "
                          : Math.round(userProgress?.progressPercent) < 50
                          ? "Bronze Badge"
                          : Math.round(userProgress?.progressPercent) < 75
                          ? "Silver Badge"
                          : Math.round(userProgress?.progressPercent) < 100
                          ? "Gold Badge"
                          : "Platinum Badge"}
                      </h5>
                      <p className="text-break new-text-color fw-normal ">
                        {Math.round(userProgress?.progressPercent) < 30
                          ? " Learn 30% of your learning  plan to reach Bronze badge Badge "
                          : Math.round(userProgress?.progressPercent) < 50
                          ? "  Learn 50% of your learning  plan to reach silver badge"
                          : Math.round(userProgress?.progressPercent) < 75
                          ? "  Learn 75% of your learning  plan to reach Gold badge"
                          : Math.round(userProgress?.progressPercent) < 100
                          ? "Learn 100% of your learning  plan to reach Platinum badge"
                          : " Congratulations!"}
                      </p>
                      <div className="list-icon">
                        {Math.round(userProgress?.progressPercent) < 30 ? (
                          <>
                            <TbBadge />-
                            <TbBadge />-
                            <TbBadge />-
                            <TbBadge />
                          </>
                        ) : Math.round(userProgress?.progressPercent) <
                          50 ? (
                          <>
                            <TbBadgeOff className=" text-dark bg-bronze" />-
                            <TbBadge />-
                            <TbBadge />-
                            <TbBadge />
                          </>
                        ) : Math.round(userProgress?.progressPercent) <
                          75 ? (
                          <>
                            <TbBadgeOff className=" text-dark  bg-bronze" />-
                            <TbBadgeOff className=" text-dark  bg-silver" />-
                            <TbBadge />-
                            <TbBadge />
                          </>
                        ) : Math.round(userProgress?.progressPercent) <
                          100 ? (
                          <>
                            <TbBadgeOff className=" text-dark bg-bronze" />-
                            <TbBadgeOff className=" text-dark  bg-silver" />-
                            <TbBadgeOff className=" text-dark bg-gold" />-
                            <TbBadge />
                          </>
                        ) : Math.round(userProgress?.progressPercent) ===
                          100 ? (
                          <>
                            <TbBadgeOff className=" text-dark bg-bronze" />-
                            <TbBadgeOff className=" text-dark bg-silver" />-
                            <TbBadgeOff className=" text-dark bg-gold" />-
                            <TbBadgeOff className=" text-dark bg-platinum" />
                          </>
                        ) : (
                          <>
                            <TbBadge />-
                            <TbBadge />-
                            <TbBadge />-
                            <TbBadge />
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="col-lg-3 col-md-3 col-sm-12  col-12 p-3 text-break ">
              <div className="total-time">
                <FaClock className="clock" />
                <div>
                  <h5 className="f-wfw-bold m-0">1h 3m</h5>
                  <small className=" new-text-color fw-normal">
                    Learning time <GoClock />
                  </small>
                </div>
              </div>
            </div>
            <div className="col-lg-3 col-md-3 col-sm-12 p-3">
              <div className="total-score">
                <IoMdCheckbox className="checkbox" />
                <div>
                  <h5 className="f-wfw-bold m-0">68%</h5>
                  <small className=" new-text-color fw-normal">
                    Average Score <GoClock />
                  </small>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="col-lg-5 col-md-5 col-sm-12 col-12  user-progress">
          <div className="p-3 learning   ">
            <div className="learning-container">
              <h5 className="f-wfw-bold ">
                <FaRegCircleCheck className="learning-progress-icon" />{" "}
                {Math.round(userProgress?.progressPercent)}% Learning path
                complete!
              </h5>
              <div className="learn-progress">
                <div
                  className="learn-progress-bar"
                  style={{
                    width: `${Math.round(userProgress?.progressPercent)}%`,
                  }}
                />
              </div>
              <hr />
              <p className=" new-text-color fw-normal">
                You have completed {completedCoursesCount} out of{" "}
                {coursesList?.length} courses
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
export default UserProgress;
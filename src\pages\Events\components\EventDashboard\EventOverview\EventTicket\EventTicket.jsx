import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Link, useParams } from "react-router-dom";
import { Label } from "reactstrap";
import { Input, InputNumber } from "antd";
import TextArea from "antd/es/input/TextArea";
import { Combobox } from "react-widgets";
import { MdDeleteForever } from "react-icons/md";
import { IoCloseOutline } from "react-icons/io5";

import {
  createEventTicket,
  deleteEventTicket,
  editEventTicket,
  getEventTicket,
} from "../../../../actions";
import CustomLoader from "../../../../../../components/sharedComponents/CustomLoader";
import DeleteModal from "../../../../../../components/sharedComponents/DeleteModal";
import { getOptions } from "../../../../../../components/utils";
import ToolTip from "../../../../../../components/sharedComponents/ToolTip";

const EventTicket = () => {
  
  const { id } = useParams();
  const dispatch = useDispatch();

  const { eventTicketList, eventRegistationLoading } = useSelector(
    (state) => state.event,
  );

  const [ticketName, setTicketName] = useState("");
  const [editTicketName, setEditTicketName] = useState("");
  const [count, setCount] = useState("");
  const [editCount, setEditCount] = useState(0);
  const [limit, setLimit] = useState(true);
  const [editLimit, setEditLimit] = useState();
  const [isEdit, setIsEdit] = useState(false);
  const [description, setDescription] = useState();
  const [editDescription, setEditDescription] = useState();
  const [editTicketId, setEditTicketId] = useState(null);
  const [showDelModal, setShowDelModal] = useState();
  const [delTicketId, setDelTicketId] = useState();
  const [type, setType] = useState("");
  const [editType, setEditType] = useState();
  const [amount, setAmount] = useState();
  const [editAmount, setEditAmount] = useState();


  useEffect(() => {
    dispatch(getEventTicket(id));
  }, [dispatch, id]);

  useEffect(() => {
    if (editTicketId) {
      const editedTicket = eventTicketList.find(
        (ticket) => ticket._id === editTicketId,
      );
      setEditTicketName(editedTicket.title);
      setEditCount(editedTicket.quantity);
      setEditLimit(editedTicket.limit);
      setEditDescription(editedTicket.description);
      setEditType(editedTicket.type);
      setEditAmount(editedTicket.price);
    }
  }, [editTicketId]);

  const handlecreateTicket = () => {
    dispatch(
      createEventTicket({
        eventId: id,
        limit: limit,
        title: ticketName,
        quantity: count,
        description: description,
        type: type,
        price: amount,
      }),
    );
    setCount("");
    setTicketName("");
    setAmount("");
    setDescription("");
    setType("");
    setLimit(true);
  };

  const handleDelteTicket = () => {
    dispatch(deleteEventTicket({ event: id, ticketId: delTicketId })).then(
      () => {
        setShowDelModal(false);
      },
    );
  };

  const handleSaveEdit = (ticketId) => {
    dispatch(
      editEventTicket({
        event: id,
        ticketId,
        limit: editLimit,
        title: editTicketName,
        quantity: editCount,
        description: editDescription,
        type: editType,
        price: editAmount,
      }),
    );
    setIsEdit(false);
    setEditTicketId("");
  };

  const toggleModal = () => {
    setShowDelModal(!showDelModal);
  };

  const typeOption = [
    { name: "Free", value: "free" },
    { name: "Swags", value: "swags" },
    { name: "Meet Up", value: "meetup" },
    { name: "Resource", value: "resource" },
    { name: "Workshop", value: "workshop" },
    { name: "Certificate", value: "certificate" },
  ];

  return (
    <>
      <div className='ticket-setting  border'>
        <div className='ticket-setting-content px-4'>
          <div className='title-section'>
            <h1 className='my-3'>You can edit the Ticket details here</h1>
            <h4 className='my-3'>Ticket Settings</h4>
            <span>
              Event settings can't be changed after the event is created.
            </span>
          </div>
          <div className='ticket-setting-form border form-shadow py-5 px-4 my-3 overflow-auto'>
            <div className='row'>
              <div className='col-sm-2 my-2 my-sm-0'>
                <span className=' label-name'>Manage Ticket</span>
              </div>
              {eventRegistationLoading ? (
                <div className=' col-8 d-flex justify-content-center'>
                  <CustomLoader />
                </div>
              ) : (
                <div className={`col-10 mb-3`}>
                  {eventTicketList.map((ticketData) => {
                    return (
                      <div className='mb-5'>
                        <div
                          className={` ${
                            isEdit &&
                            editTicketId === ticketData._id &&
                            "border p-4 rounded"
                          }`}
                          onChange={() => {
                            setEditTicketId(ticketData._id);
                            setIsEdit(true);
                          }}
                        >
                          <div
                            className={`edit-ticket-heading ${
                              editTicketId === ticketData._id && isEdit
                                ? ""
                                : "d-none"
                            }`}
                          >
                            <h5>Edit Ticket</h5>
                          </div>
                          <div className='d-flex w-100'>
                            <div className='w-50 pr-3'>
                              <Label>Ticket Name</Label>
                              <Input
                                showCount
                                maxLength={40}
                                size='large'
                                placeholder='Enter Ticket Name'
                                value={
                                  editTicketId === ticketData._id
                                    ? editTicketName
                                    : ticketData.title
                                }
                                onChange={(e) => {
                                  setEditTicketName(e.target.value);
                                }}
                              />
                            </div>
                            <div className=' d-flex flex-column px-3'>
                              <Label>Max Qty</Label>
                              <InputNumber
                                size='large'
                                min={0}
                                placeholder='0'
                                value={
                                  editTicketId === ticketData._id
                                    ? editCount
                                    : ticketData.quantity
                                }
                                onChange={(e) => {
                                  setEditTicketId(ticketData._id);
                                  setIsEdit(true);
                                  setEditCount(e);
                                }}
                              />
                            </div>
                            <div className=' d-flex flex-column px-3'>
                              <Label>Price</Label>
                              <InputNumber
                                size='large'
                                min={0}
                                placeholder='0'
                                value={
                                  editTicketId === ticketData._id
                                    ? editAmount
                                    : ticketData.price
                                }
                                onChange={(e) => {
                                  setEditTicketId(ticketData._id);
                                  setIsEdit(true);
                                  setEditAmount(e);
                                }}
                              />
                            </div>
                            <div className='px-3  d-flex flex-column'>
                              <Label>Sold</Label>
                              <div className='align-self-center'>
                                {ticketData?.registrations?.length}/
                                {editCount === ""
                                  ? editAmount
                                  : ticketData.quantity}
                              </div>
                            </div>
                            <div>
                              <ToolTip
                                title='Delete'
                                placement='right'
                                color='#D23232'
                              >
                                <span
                                  className='self-align-center close-icon text-danger w-100'
                                  onClick={() => {
                                    setDelTicketId(ticketData._id);
                                    setShowDelModal(true);
                                  }}
                                >
                                  <MdDeleteForever />
                                </span>
                              </ToolTip>
                            </div>
                          </div>
                          <div className='d-flex w-100'>
                            <div className=' mt-2 pr-3 w-50'>
                              <Label>Description</Label>
                              <TextArea
                                autoSize={{
                                  minRows: 3,
                                  maxRows: 6,
                                }}
                                showCount
                                maxLength={200}
                                placeholder='description'
                                value={
                                  editTicketId === ticketData._id
                                    ? editDescription
                                    : ticketData.description
                                }
                                onChange={(e) => {
                                  setEditDescription(e.target.value);
                                }}
                              ></TextArea>
                            </div>
                            <div className=' mt-2 px-3'>
                              <Label>Type</Label>
                              <Combobox
                                data={getOptions(typeOption, "type")}
                                dataKey={"type"}
                                textField='type'
                                value={
                                  editTicketId === ticketData._id
                                    ? editType
                                    : ticketData.type
                                }
                                onChange={(e) => {
                                  setEditTicketId(ticketData._id);
                                  setIsEdit(true);
                                  setEditType(e.value);
                                }}
                              />
                            </div>
                          </div>
                          {isEdit && editTicketId === ticketData._id && (
                            <>
                              <div className='row mt-3'>
                                <div className='mx-2 px-2'>
                                  <button
                                    className='save-btn'
                                    onClick={() => {
                                      handleSaveEdit(ticketData._id);
                                    }}
                                  >
                                    SAVE
                                  </button>
                                </div>
                                <div className=' mx-2 px-2'>
                                  <button
                                    className='save-btn'
                                    onClick={() => {
                                      setIsEdit(false);
                                      setEditTicketId("");
                                    }}
                                  >
                                    DISCARD
                                  </button>
                                </div>
                              </div>
                            </>
                          )}
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}
            </div>
            <div className='row'>
              <div className='col-sm-2'></div>
              <div className='col-10 '>
                <div className='d-flex w-100'>
                  <div className='pr-3 w-50'>
                    <Label>Ticket Name</Label>
                    <Input
                      showCount
                      size='large'
                      maxLength={40}
                      placeholder='Enter Ticket Name'
                      value={ticketName}
                      onChange={(e) => setTicketName(e.target.value)}
                    />
                  </div>
                  <div className=' d-flex flex-column px-3'>
                    <Label>Max Qty</Label>
                    <InputNumber
                      size='large'
                      min={0}
                      placeholder='0'
                      value={count}
                      onChange={(e) => {
                        setCount(e);
                      }}
                    />
                  </div>
                  <div className='px-3 d-flex flex-column'>
                    <Label>Price</Label>
                    <InputNumber
                      min={0}
                      size='large'
                      value={amount}
                      placeholder='₹ 100'
                      onChange={(e) => {
                        setAmount(e);
                      }}
                    />
                  </div>

                  <div className='px-3  d-flex flex-column'>
                    <Label>Sold</Label>
                    <div className='align-self-center'>
                      0/{count === "" ? 0 : count}
                    </div>
                  </div>
                  <div>
                    <ToolTip title='Reset' placement='right'>
                      <span
                        className='close-icon text-secondary'
                        onClick={() => {
                          setAmount();
                          setCount("");
                          setTicketName();
                          setDescription();
                          setType();
                        }}
                      >
                        <IoCloseOutline />
                      </span>
                    </ToolTip>
                  </div>
                </div>
                <div className='d-flex w-100'>
                  <div className=' mt-2 pr-3 w-50'>
                    <Label>Description</Label>
                    <TextArea
                      rows={3}
                      placeholder='Description'
                      value={description}
                      onChange={(e) => {
                        setDescription(e.target.value);
                      }}
                    ></TextArea>
                  </div>
                  <div className=' mt-2 px-3'>
                    <Label>Type</Label>
                    <Combobox
                      data={getOptions(typeOption, "type")}
                      dataKey={"value"}
                      textField='type'
                      placeholder={"Type"}
                      value={type}
                      onChange={(e) => {
                        setType(e.value);
                      }}
                    />
                  </div>
                </div>
                <div className='col-8 mt-4 px-0 mb-4 '>
                  <button
                    className='operation-btn'
                    onClick={handlecreateTicket}
                  >
                    ADD TICKET
                  </button>
                </div>
              </div>
              <div className='col-sm-2 label-name mt-5'>Event URL</div>
              <div className='col-sm-9 mt-4'>
                <Link to={"/"}>https://www.datacode.in/datacod-event-24</Link>
                <div>
                  <span>
                    Send your Event Participants directly to this Event Profile
                    URL.
                  </span>
                  <span>
                    <br />
                    Click on this URL to preview your Event Profile
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      {
        <DeleteModal
          onSubmit={handleDelteTicket}
          toggle={toggleModal}
          title='Delete Ticket'
          open={showDelModal}
        />
      }
    </>
  );
};

export default EventTicket;

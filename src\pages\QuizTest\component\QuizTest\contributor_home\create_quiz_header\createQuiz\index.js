import CreateQuiz from "./CreateQuiz";
import { connect } from "react-redux";

import { createQuizTest } from "../../../../../actions";

const mapStateToProps = ({ user={}, quizTest={}}) => ({
  userError: user.error || null,
  currentUser: user.currentUser || null,
  currentQuizTest: quizTest.currentQuizTest || null,
});

const mapDispatchToProps = {
  createQuizTest,
};

export default connect(mapStateToProps, mapDispatchToProps)(CreateQuiz);

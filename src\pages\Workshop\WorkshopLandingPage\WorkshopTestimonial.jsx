import React from 'react'
import Carousel from "react-multi-carousel";

import WorkshopTestimonialData from './WorkshopTestimonialData';

const WorkshopTestimonial = () => {

  const data = [
    {
      img: "http://res.cloudinary.com/datacode/image/upload/v1691170388/natghz9cxos2uyp4osxf.png",
      name: '<PERSON>',
      bio: 'Lead Designer',
      title: 'It was a very good experience',
      content: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Cursus nibh mauris, nec turpis orci lectus maecenas. Suspendisse sed magna eget nibh in turpis. Consequat duis diam lacus arcu. Faucibus venenatis felis id augue sit cursus pellentesque enim arcu. Elementum felis magna pretium in tincidunt. Suspendisse sed magna eget nibh in turpis. Consequat duis diam lacus arcu.',
      rating: '4',
    },
    {
      img: "http://res.cloudinary.com/datacode/image/upload/v1691170388/natghz9cxos2uyp4osxf.png",
      name: '<PERSON>',
      bio: 'Lead Designer',
      title: 'It was a very good experience',
      content: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Cursus nibh mauris, nec turpis orci lectus maecenas. Suspendisse sed magna eget nibh in turpis. Consequat duis diam lacus arcu. Faucibus venenatis felis id augue sit cursus pellentesque enim arcu. Elementum felis magna pretium in tincidunt. Suspendisse sed magna eget nibh in turpis. Consequat duis diam lacus arcu.',
      rating: '5',
    },
    {
      img: "http://res.cloudinary.com/datacode/image/upload/v1691170388/natghz9cxos2uyp4osxf.png",
      name: 'Leo',
      bio: 'Lead Designer',
      title: 'It was a very good experience',
      content: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Cursus nibh mauris, nec turpis orci lectus maecenas. Suspendisse sed magna eget nibh in turpis. Consequat duis diam lacus arcu. Faucibus venenatis felis id augue sit cursus pellentesque enim arcu. Elementum felis magna pretium in tincidunt. Suspendisse sed magna eget nibh in turpis. Consequat duis diam lacus arcu.',
      rating: '4',
    },
    {
      img: "http://res.cloudinary.com/datacode/image/upload/v1691170388/natghz9cxos2uyp4osxf.png",
      name: 'Leo',
      bio: 'Lead Designer',
      title: 'It was a very good experience',
      content: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Cursus nibh mauris, nec turpis orci lectus maecenas. Suspendisse sed magna eget nibh in turpis. Consequat duis diam lacus arcu. Faucibus venenatis felis id augue sit cursus pellentesque enim arcu. Elementum felis magna pretium in tincidunt. Suspendisse sed magna eget nibh in turpis. Consequat duis diam lacus arcu.',
      rating: '4',
    },
    {
      img: "http://res.cloudinary.com/datacode/image/upload/v1691170388/natghz9cxos2uyp4osxf.png",
      name: 'Leo',
      bio: 'Lead Designer',
      title: 'It was a very good experience',
      content: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Cursus nibh mauris, nec turpis orci lectus maecenas. Suspendisse sed magna eget nibh in turpis. Consequat duis diam lacus arcu. Faucibus venenatis felis id augue sit cursus pellentesque enim arcu. Elementum felis magna pretium in tincidunt. Suspendisse sed magna eget nibh in turpis. Consequat duis diam lacus arcu.',
      rating: '2',
    },
    {
      img: "http://res.cloudinary.com/datacode/image/upload/v1691170388/natghz9cxos2uyp4osxf.png",
      name: 'Leo',
      bio: 'Lead Designer',
      title: 'It was a very good experience',
      content: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Cursus nibh mauris, nec turpis orci lectus maecenas. Suspendisse sed magna eget nibh in turpis. Consequat duis diam lacus arcu. Faucibus venenatis felis id augue sit cursus pellentesque enim arcu. Elementum felis magna pretium in tincidunt. Suspendisse sed magna eget nibh in turpis. Consequat duis diam lacus arcu.',
      rating: '3',
    },
    {
      img: "http://res.cloudinary.com/datacode/image/upload/v1691170388/natghz9cxos2uyp4osxf.png",
      name: 'Leo',
      bio: 'Lead Designer',
      title: 'It was a very good experience',
      content: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Cursus nibh mauris, nec turpis orci lectus maecenas. Suspendisse sed magna eget nibh in turpis. Consequat duis diam lacus arcu. Faucibus venenatis felis id augue sit cursus pellentesque enim arcu. Elementum felis magna pretium in tincidunt. Suspendisse sed magna eget nibh in turpis. Consequat duis diam lacus arcu.',
      rating: '2',
    },
  ]

  const responsive = {
    desktop: {
      breakpoint: { max: 3000, min: 1024 },
      items: 3,
    },
    tablet: {
      breakpoint: { max: 1024, min: 464 },
      items: 2,
    },
    mobile: {
      breakpoint: { max: 464, min: 0 },
      items: 1,
    }
  };

  const workshopData = data.map((item, i) => (
    <WorkshopTestimonialData img={item.img} name={item.name} bio={item.bio} title={item.title} content={item.content}  rating = {item.rating} />
  ))

  return (
    <>
      <div className='workshop-testimonial-body'>
        <div className='text-center'>
          <h6>TESTIMONIALS</h6>
        </div>
        <div className='py-2 text-center'>
          <h2>What Our Learners have to say about us</h2>
        </div>
        <div className='pt-3 mt-3 testimonial-header'>
          <Carousel swipeable={true}
            draggable={true}
            showDots={true}
            responsive={responsive}
            autoPlaySpeed={1000}>
            {workshopData}
          </Carousel>
        </div>
      </div>
    </>
  )
}
export default WorkshopTestimonial;

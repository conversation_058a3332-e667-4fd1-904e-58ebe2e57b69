import React from "react";
import { useDispatch, useSelector } from "react-redux";
import { useParams } from "react-router";

import { createContent, getLessonDetails } from "../../../../actions";
import BlockCard from "./BlockCard";
import { blocksCardJson } from "./helper";

const BlocksListSection = () => {
  
  const dispatch = useDispatch()
  const { id } = useParams();

  const { lessonDetails } = useSelector((state) => state.course)

  const basicBlock = blocksCardJson.filter(item => item.category === "basic");
  const embedBlock = blocksCardJson.filter(item => item.category === "embeds");

  const handleCreateContentBlock = (type) => {
    const content = {
      author: process.env.REACT_APP_ADMIN_AUTHOR_ID,
      lesson: lessonDetails && lessonDetails._id,
      section: lessonDetails && lessonDetails.section && lessonDetails.section._id,
      course: id ?? null,
      type
    }
    dispatch(createContent(content)).then((res) => {
      if (res) {
        dispatch(getLessonDetails(lessonDetails && lessonDetails._id))
      }
    })
  }

  return (
    <>
      <div className='row mx-0 block-sections border'>
        <div className='col-12 px-0'>
          <div className='block-header'>
            <h4>Blocks</h4>
            <p>
              Choose a block below and drag and drop it onto the left panel to
              compose your lesson as you'd like.
            </p>
          </div>
          <div className="block-list">
            <h6 className='mx-2 my-2'>BASIC BLOCKS</h6>
            {basicBlock.map((item,index) => (
              <BlockCard
                icon={item.icon}
                title={item.title}
                description={item.description}
                type={item.type}
                handleCreateContentBlock={handleCreateContentBlock}
                key={`${index}${item._id}`}
              />
            ))}
            <h6 className='mx-2 my-2'>EMBEDS</h6>
            {embedBlock.map(item => (
              <BlockCard
                icon={item.icon}
                title={item.title}
                description={item.description}
                type={item.type}
                handleCreateContentBlock={handleCreateContentBlock}
                key={`embed${item._id}`}
              />
            ))}
          </div>
        </div>
      </div>
    </>
  );
};
export default BlocksListSection;

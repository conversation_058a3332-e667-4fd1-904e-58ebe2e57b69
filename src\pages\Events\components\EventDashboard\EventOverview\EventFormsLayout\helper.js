export const fieldTypesOptions = [
  { name: "Text", value: "text" },
  { name: "Textarea", value: "textarea" },
  { name: "Radio", value: "radio" },
  { name: "Checkbox", value: "checkbox" },
  { name: 'Linear Scale', value: 'linearScale' },
]

export const formTypeOptions = [
  { name: "Feedback Form", value: "feedback" },
  { name: "Registration Form", value: "registration" },
]

export const generateFieldName = (item) => {
  return item.toLowerCase().replaceAll(" ", "_");
}
import axios from "axios";
import * as actions from "./actionCreators";
import {
  ADD_SPONSOR_LOADING,
  GET_SPONSORS_LIST_LOADING,
  GET_SPONSOR_DETAILS_LOADING,
  SET_SPONSOR_DETAILS,
} from "../constants";
import { generateQueryParams } from "../../../components/utils";
import { triggerNotifier } from "../../../components/utils/notifier";

const baseURL = process.env.REACT_APP_BASE_URL;

export const addSponsor = (speaker) => (dispatch) => {
  dispatch({ type: ADD_SPONSOR_LOADING });
  return axios
    .post(`${baseURL}/user/sponsor/create`, speaker)
    .then((res) => {
      if (res.status === 200) {
        triggerNotifier({
          message: "Sponsers Details Added ",
          type: "success",
          duration: 1000,
          icon: "👏",
        });
        dispatch({ type: ADD_SPONSOR_LOADING });
        return { success: true, sponsor: res?.data?.sponsor };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: ADD_SPONSOR_LOADING });
      console.log("Add Sponsor Error", error);
      triggerNotifier({
        message: "Add Sponser Error",
        type: "error",
        duration: 1000,
        icon: "⚠️",
      });
    });
};

export const getSponsors = (data) => (dispatch) => {
  dispatch({ type: GET_SPONSORS_LIST_LOADING });
  return axios
    .get(
      `${baseURL}/user/sponsors${generateQueryParams({
        limit: data?.limit,
        page: data?.page,
        search: data?.search,
        technology: data?.technology,
      })}`
    )
    .then((res) => {
      if (res.status === 200) {
        triggerNotifier({
          message: "Sponsers List Loaded ",
          type: "success",
          duration: 1000,
          icon: "👏",
        });
        dispatch(actions.setSponsorsList(res.data));
        dispatch({ type: GET_SPONSORS_LIST_LOADING });
        return { success: true, Sponsors: res?.data?.Sponsors };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: GET_SPONSORS_LIST_LOADING });
      console.log("Get Sponsors Error", error);
      triggerNotifier({
        message: "Failed to Load Sponsers",
        type: "error",
        duration: 1000,
        icon: "⚠️",
      });
    });
};

export const getSponsorDetails = (id) => (dispatch) => {
  dispatch({ type: GET_SPONSOR_DETAILS_LOADING });
  return axios
    .get(`${baseURL}/user/sponsor/${id}`)
    .then((res) => {
      if (res.status === 200) {
        triggerNotifier({
          message: "Sponsers Details Loaded ",
          type: "success",
          duration: 1000,
          icon: "👏",
        });
        dispatch({ type: SET_SPONSOR_DETAILS, payload: res.data.Sponsor });
        dispatch({ type: GET_SPONSOR_DETAILS_LOADING });
        return { success: true, Sponsor: res?.data?.Sponsor };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: GET_SPONSOR_DETAILS_LOADING });
      console.log("Get Sponsor Details Error", error);
      triggerNotifier({
        message: "Failed to Load Sponsers Details",
        type: "error",
        duration: 1000,
        icon: "⚠️",
      });
    });
};

export const editSponsor = (sponsor) => (dispatch) => {
  dispatch({ type: ADD_SPONSOR_LOADING });
  return axios
    .patch(`${baseURL}/user/sponsor/${sponsor._id}`, { data: sponsor })
    .then((res) => {
      if (res.status === 200) {
        triggerNotifier({
          message: "Edit Sponser Details ",
          type: "success",
          duration: 1000,
          icon: "👏",
        });
        dispatch(getSponsors());
        dispatch({ type: ADD_SPONSOR_LOADING });
        return { success: true, sponsor: res?.data?.sponsor };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: ADD_SPONSOR_LOADING });
      console.log("Edit Sponsor Details Error", error);
      triggerNotifier({
        message: "Edit Sponsers Details Error",
        type: "error",
        duration: 1000,
        icon: "⚠️",
      });
    });
};

export const deleteSponsor = (id) => (dispatch) => {
  return axios
    .delete(`${baseURL}/user/sponsor/${id}`)
    .then((res) => {
      if (res.status === 200) {
        triggerNotifier({
          message: "Delete Sponser Details ",
          type: "success",
          duration: 1000,
          icon: "👏",
        });
        return { success: true, sponsor: res?.data?.sponsor };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      console.log("Delete Sponsor Details Error", error);
      triggerNotifier({
        message: "Delete Sponsers Details Error",
        type: "error",
        duration: 1000,
        icon: "⚠️",
      });
    });
};

import { useEffect, useState } from "react";
import { use<PERSON>arams } from "react-router";
import { useDispatch, useSelector } from "react-redux";
import { Button, Space } from "antd";
import { Combobox } from "react-widgets";
import BootstrapTable from "react-bootstrap-table-next";
import export<PERSON>romJSO<PERSON> from "export-from-json";
import ForwardToInboxRoundedIcon from "@mui/icons-material/ForwardToInboxRounded";
import DownloadSharpIcon from "@mui/icons-material/DownloadSharp";
import { RiCloseFill, RiGift2Line } from "react-icons/ri";
import { IoFilterSharp } from "react-icons/io5";
import { GoIssueClosed } from "react-icons/go";
import { FiPlus } from "react-icons/fi";
import { SlClose } from "react-icons/sl";
import { BiSortAlt2 } from "react-icons/bi";
import { FaUserClock } from "react-icons/fa";
import { LiaUserCheckSolid } from "react-icons/lia";
import { TiUserAdd } from "react-icons/ti";
import AddGuestModal from "./AddGuestModal";
import { TbCertificate } from "react-icons/tb";
import { GrResources } from "react-icons/gr";
import { FaPeopleLine } from "react-icons/fa6";
import { TfiBlackboard } from "react-icons/tfi";
import { FaRegMoneyBill1 } from "react-icons/fa6";
import { FaRegClock, } from "react-icons/fa";

import usePermissions from "../../../../../hooks/userPermission";
import { getOptions } from "../../../../../components/utils";
import { getColumns } from "./helper";
import { LimitOptions } from "../../../../DaysCode/components/Settings/helpers";
import AddTaskRoundedIcon from "@mui/icons-material/AddTaskRounded";
import NoDataBlock from "../../../../../components/sharedComponents/NoDataBlock";
import CustomLoader from "../../../../../components/sharedComponents/CustomLoader";
import DeleteModal from "../../../../../components/sharedComponents/DeleteModal";
import {
  deleteEventRegistration,
  editEventRegistrations,
  editEventTicket,
  getEventRegistrations,
} from "../../../actions/operations";

const EventAttendees = () => {

  const dispatch = useDispatch();
  const { id } = useParams();

  const { eventRegistrationsList, eventRegistrationsLoading } = useSelector(
    (state) => state.event,
  );

  const [gender, setGender] = useState();
  const [isOpen, setIsOpen] = useState(false);
  const [emailBtn, setEmailBtn] = useState(false);
  const [filterStatus, setFilterStatus] = useState("");
  const [filterLimit, setFilterLimit] = useState();
  const [profession, setProfession] = useState("");
  const [selectedIds, setSelectedIds] = useState([]);
  const [delUserDetail, setDelUserDetail] = useState("");
  const [showDelModal, setShowDelModal] = useState(false);
  const [organization, setOrganization] = useState();
  const [reRender, setReRender] = useState();
  const [organizationOption, setOraganizationOption] = useState([]);
  const [showGuestModal, setShowGuestModal] = useState(false);
  const [ticketType, setTicketType] = useState();
  const { hasPermission } = usePermissions();


  const handleDelete = (attendeesDetail) => {
    setDelUserDetail(attendeesDetail);
    setShowDelModal(true);
  };
  
  const deleteUser = () => {
    dispatch(deleteEventRegistration(delUserDetail)).then(
      setShowDelModal(false),
      dispatch(
        getEventRegistrations({
          event: id ?? null,
          status: filterStatus ?? null,
          limit: filterLimit ?? null,
          profession: profession ?? null,
          organization: organization ?? null,
          gender: gender ?? null,
        }),
      ),
    );
  };

  const professionOptions = [
    { name: "Student", value: "student" },
    { name: "Developer", value: "developer" },
  ];

  const StatusOptions = [
    { name: "Pending", value: "pending" },
    { name: "Approve", value: "approve" },
    { name: "Reject", value: "reject" },
    { name: "Attended", value: "attended" },
    { name: "Guest", value: "guest" },
  ];

  const TicketTypeOption = [
    { name: "Free", value: "free" },
    { name: "Swags", value: "swags" },
    { name: "Meet Up", value: "meetup" },
    { name: "Resource", value: "resource" },
    { name: "Workshop", value: "workshop" },
    { name: "Certificate", value: "certificate" },
  ];

  const getOrganizationoptions = () => {
    const uniqueOrganizations = [
      ...new Set(eventRegistrationsList.map((item) => item.organization)),
    ];
    const options = uniqueOrganizations.map((organization) => ({
      name: organization.toUpperCase(),
      value: organization.replace(/\s+/g, "_"),
    }));
    setOraganizationOption(options);
  };

  const handleAfterEdit = () => {
    dispatch(
      getEventRegistrations({
        event: id ?? null,
        status: filterStatus ?? null,
        limit: filterLimit ?? null,
        profession: profession ?? null,
        organization: organization ?? null,
        gender: gender ?? null,
        ticketType: ticketType ?? null,
      }),
    );
  };

  const statusItems = (row) => [
    {
      label: (
        <span className='text-warning'>
          <FaRegClock className='mr-1' />
          Pending
        </span>
      ),
      key: "pending",
      onClick: () =>
        dispatch(
          editEventRegistrations({
            event: id,
            userId: row._id,
            status: "pending",
          }),
        ).then(() => {
          handleAfterEdit();
        }),
    },
    {
      label: (
        <span className='text-success'>
          {" "}
          <GoIssueClosed className='mr-1' />
          Approve
        </span>
      ),
      key: "approve",
      onClick: () =>
        dispatch(
          editEventRegistrations({
            event: id,
            userId: row._id,
            status: "approve",
          }),
        ).then(() => {
          handleAfterEdit();
        }),
    },
    {
      label: (
        <span className='text-danger'>
          {" "}
          <SlClose className='mr-1' />
          Reject
        </span>
      ),
      key: "reject",
      onClick: () =>
        dispatch(
          editEventRegistrations({
            event: id,
            userId: row._id,
            status: "reject",
          }),
        ).then(() => {
          handleAfterEdit();
        }),
    },
    {
      label: (
        <span className='text-primary'>
          <LiaUserCheckSolid className='mr-1' />
          Attended
        </span>
      ),
      key: "attended",
      onClick: () =>
        dispatch(
          editEventRegistrations({
            event: id,
            userId: row._id,
            status: "attended",
          }),
        ).then(() => {
          handleAfterEdit();
        }),
    },
    {
      label: (
        <span className='text-info'>
          <FaUserClock className='mr-1' />
          Guest
        </span>
      ),
      key: "guest",
      onClick: () =>
        dispatch(
          editEventRegistrations({
            event: id,
            userId: row._id,
            status: "guest",
          }),
        ).then(() => {
          handleAfterEdit();
        }),
    },
  ];

  const ticketTypeItems = (row) => [
    {
      label: (
        <span className='text-orange'>
          <TfiBlackboard className='mr-1' />
          Workshop
        </span>
      ),
      key: "workshop",
      onClick: () => {
        dispatch(
          editEventTicket({
            event: id,
            ticketId: row.ticket._id,
            type: "workshop",
          }),
        ).then(() => {
          handleAfterEdit();
        });
      },
    },
    {
      label: (
        <span className='text-warning'>
          <FaPeopleLine className='mr-1' />
          MeetUp
        </span>
      ),
      key: "meetup",
      onClick: () => {
        dispatch(
          editEventTicket({
            event: id,
            ticketId: row.ticket._id,
            type: "meetup",
          }),
        ).then(() => {
          handleAfterEdit();
        });
      },
    },
    {
      label: (
        <span className='text-green'>
          <FaRegMoneyBill1 className='mr-1' />
          Free
        </span>
      ),
      key: "free",
      onClick: () => {
        dispatch(
          editEventTicket({
            event: id,
            ticketId: row.ticket._id,
            type: "free",
          }),
        ).then(() => {
          handleAfterEdit();
        });
      },
    },
    {
      label: (
        <span className='text-danger'>
          <RiGift2Line className='mr-1' />
          Swags
        </span>
      ),
      key: "swags",
      onClick: () => {
        dispatch(
          editEventTicket({
            event: id,
            ticketId: row.ticket._id,
            type: "swags",
          }),
        ).then(() => {
          handleAfterEdit();
        });
      },
    },
    {
      label: (
        <span className='text-info'>
          <GrResources className='mr-1' />
          Resource
        </span>
      ),
      key: "resource",
      onClick: () => {
        dispatch(
          editEventTicket({
            event: id,
            ticketId: row.ticket._id,
            type: "resource",
          }),
        ).then(() => {
          handleAfterEdit();
        });
      },
    },
    {
      label: (
        <span className='text-primary'>
          <TbCertificate className='mr-1' />
          Certificate
        </span>
      ),
      key: "certificate",
      onClick: () => {
        dispatch(
          editEventTicket({
            event: id,
            ticketId: row.ticket._id,
            type: "certificate",
          }),
        ).then(() => {
          handleAfterEdit();
        });
      },
    },
  ];

  let columns = getColumns({ handleDelete, id, statusItems, ticketTypeItems });

  const selectRow = {
    mode: "checkbox",
    clickToSelect: false,
    selected: selectedIds,
    onSelect: (row, isSelect) => {
      if (isSelect) {
        setSelectedIds((prevSelectedIds) => [...prevSelectedIds, row._id]);
      } else {
        setSelectedIds((prevSelectedIds) =>
          prevSelectedIds.filter((selectedId) => selectedId !== row._id),
        );
      }
    },
    onSelectAll: (isSelect, rows) => {
      if (isSelect) {
        setSelectedIds(rows.map((row) => row._id));
        setEmailBtn(true);
      } else {
        setSelectedIds([]);
      }
    },
  };

  const handleOutsideClick = (event) => {
    if (!event.target.closest(".filter")) {
      setIsOpen(false);
    }
  };

  const toggleModal = () => {
    setShowDelModal(!showDelModal);
  };

  const handleRadioChange = (e) => {
    setGender(e.target.value);
  };

  const data = eventRegistrationsList;
  const fileName = "Event-Participants-Registration";
  const exportType = exportFromJSON.types.xls;
  const downloadExel = () => {
    exportFromJSON({ data, fileName, exportType });
  };

  const handleApply = () => {
    dispatch(
      getEventRegistrations({
        event: id,
        status: filterStatus,
        limit: filterLimit,
        profession,
        organization,
        gender,
        ticketType,
      }),
    ).then(() => {
      setIsOpen(false);
      // setGender();
      // setFilterLimit();
      // setFilterStatus();
      // setProfession();
      // setOrganization();
    });
  };

  const handleReset = () => {
    setGender();
    setFilterLimit();
    setFilterStatus();
    setProfession();
    setOrganization();
    setTicketType();
    dispatch(getEventRegistrations({ event: id }));
  };

  const closeGuestModal = () => {
    setShowGuestModal(false);
  };

  useEffect(() => {
    dispatch(getEventRegistrations({ event: id }));
  }, [dispatch, id]);

  useEffect(() => {
    reRender && handleApply();
  }, [filterLimit, profession, organization, gender, filterStatus]);

  useEffect(() => {
    selectedIds.length === 0 ? setEmailBtn(false) : setEmailBtn(true);
  }, [selectedIds, organization]);

  useEffect(() => {
    getOrganizationoptions();
    if (isOpen) {
      document.addEventListener("click", handleOutsideClick);
    }
    return () => {
      document.removeEventListener("click", handleOutsideClick);
    };
  }, [isOpen]);

  return (
    <>
      <div className='event-attendees'>
        <div className='d-flex flex-wrap justify-content-between align-items-center px-3 py-3 border-bottom'>
          <div className='event-attendees-title'>
            Event Attendees <span>Dasboard</span>
          </div>
          <div className='filter-Modal-content'>
            <Space className="d-flex flex-wrap">
              {emailBtn ? (
                <>
                  <Button
                    className='event-send-mail-btn btn-success '
                    onClick={() => {
                      // setIsOpen(!isOpen);
                    }}
                  >
                    <AddTaskRoundedIcon fontSize='small' className='' />
                    Approve
                  </Button>
                  <Button
                    className='event-send-mail-btn btn-success '
                    onClick={() => {
                      // setIsOpen(!isOpen);
                    }}
                  >
                    <ForwardToInboxRoundedIcon fontSize='small' className='' />
                    Send Email
                  </Button>
                </>
              ) : (
                ""
              )}
              {hasPermission("event", "create") && (
                <Button
                  className='event-send-mail-btn btn-success '
                  onClick={() => {
                    setShowGuestModal(true);
                  }}
                >
                  <TiUserAdd fontSize={"small"} />
                  Add Guest
                </Button>
              )}
              <Button
                className='event-downlaod-xlsx-btn btn-info '
                onClick={() => {
                  downloadExel();
                }}
              >
                <DownloadSharpIcon fontSize='small' className='' />
                Download xlsx
              </Button>
            </Space>
          </div>
        </div>
        <div className='table table-responsive mt-3'>
          <div
            className='w-100 border-top border-left border-right rounded d-flex flex-wrap justify-content-between align-items-center'
            style={{ backgroundColor: "#f0eeee" }}
          >
            <div className='table-top-options'>
              <span>
                <FiPlus />
              </span>
              <span
                onClick={(e) => {
                  e.stopPropagation();
                  setIsOpen(!isOpen);
                }}
              >
                <IoFilterSharp />
              </span>

              <span>
                <BiSortAlt2 />
              </span>

              {filterLimit && (
                <div className='filters'>
                  <div className='filters-list'>
                    Limit
                    <RiCloseFill
                      className='close-icon'
                      onClick={(e) => {
                        setFilterLimit();
                      }}
                    />
                  </div>
                </div>
              )}
              {gender && (
                <div className='filters'>
                  <div className='filters-list'>
                    Gender
                    <RiCloseFill
                      className='close-icon'
                      onClick={() => {
                        setGender();
                      }}
                    />
                  </div>
                </div>
              )}
              {ticketType && (
                <div className='filters'>
                  <div className='filters-list'>
                    Ticket Type
                    <RiCloseFill
                      className='close-icon'
                      onClick={() => {
                        setTicketType();
                      }}
                    />
                  </div>
                </div>
              )}
              {filterStatus && (
                <div className='filters'>
                  <div className='filters-list'>
                    Status
                    <RiCloseFill
                      className='close-icon'
                      onClick={() => {
                        setFilterStatus();
                      }}
                    />
                  </div>
                </div>
              )}
              {profession && (
                <div className='filters'>
                  <div className='filters-list'>
                    Profession
                    <RiCloseFill
                      className='close-icon'
                      onClick={() => {
                        setProfession();
                      }}
                    />
                  </div>
                </div>
              )}
              {organization && (
                <div className='filters'>
                  <div className='filters-list'>
                    Organization
                    <RiCloseFill
                      className='close-icon'
                      onClick={() => {
                        setOrganization();
                      }}
                    />
                  </div>
                </div>
              )}
            </div>
            <div className='table-options-search'>
              <input className='search' type='search' placeholder={"Search..."} />
            </div>
          </div>
          {isOpen ? (
            <>
              <div className='filter'>
                <div>
                  <span>Filter Options</span>
                </div>
                <div className='empty-line'></div>
                <div className='mb-2 '>
                  <label className='form-label'>Profession:</label>
                  <Combobox
                    data={getOptions(professionOptions, "profession")}
                    dataKey={"value"}
                    textField='profession'
                    placeholder={"profession"}
                    value={profession}
                    onChange={(value) => {
                      setProfession(value.value);
                    }}
                  />
                </div>
                <div className='mb-2 '>
                  <label className='form-label'>Ticket Type:</label>
                  <Combobox
                    data={getOptions(TicketTypeOption, "ticketType")}
                    dataKey={"value"}
                    textField='ticketType'
                    placeholder={"type"}
                    value={ticketType}
                    onChange={(value) => {
                      setTicketType(value.value);
                    }}
                  />
                </div>
                <div className='mb-2 '>
                  <label className='form-label'>Organization:</label>
                  <Combobox
                    data={getOptions(organizationOption, "organization")}
                    dataKey={"value"}
                    textField='organization'
                    placeholder={"organization"}
                    value={organization}
                    onChange={(value) => {
                      setOrganization(value.value);
                    }}
                  />
                </div>
                <div className='row'>
                  <div className='mb-2 col-6 status'>
                    <label className='form-label'>Status:</label>
                    <Combobox
                      data={getOptions(StatusOptions, "status")}
                      dataKey={"value"}
                      textField='status'
                      placeholder={"status"}
                      value={filterStatus}
                      onChange={(value) => {
                        setFilterStatus(value.value);
                      }}
                    />
                  </div>
                  <div className='mb-2 col-6'>
                    <label className='form-label'>Limit:</label>
                    <Combobox
                      data={getOptions(LimitOptions, "limit")}
                      dataKey={"value"}
                      textField='limit'
                      placeholder={"limit"}
                      value={filterLimit}
                      onChange={(e) => {
                        setFilterLimit(e.value);
                      }}
                    />
                  </div>
                </div>
                <div className='mt-3 '>
                  <label className='form-label'>Gender:</label>
                  <div className='d-flex'>
                    <div className='form-check d-flex  flex-column-reverse'>
                      <input
                        className='form-check-input'
                        type='radio'
                        id='male'
                        value='male'
                        checked={gender === "male"}
                        onChange={handleRadioChange}
                      />
                      <label className='form-check-label' htmlFor='male'>
                        Male
                      </label>
                    </div>
                    <div className='form-check flex-column-reverse'>
                      <input
                        className='form-check-input'
                        type='radio'
                        id='female'
                        value='female'
                        checked={gender === "female"}
                        onChange={handleRadioChange}
                      />
                      <label className='form-check-label' htmlFor='female'>
                        Female
                      </label>
                    </div>
                  </div>
                </div>
                <div className='mt-4 filter-buttons'>
                  <button
                    className='btn btn-secondary mx-2'
                    onClick={handleReset}
                  >
                    Reset
                  </button>
                  <button
                    className='btn btn-primary mx-2'
                    onClick={() => {
                      handleApply();
                      setReRender(true);
                    }}
                  >
                    Apply
                  </button>
                </div>
              </div>
            </>
          ) : (
            ""
          )}
          {eventRegistrationsLoading ? (
            <CustomLoader />
          ) : eventRegistrationsList.length === 0 ? (
            <NoDataBlock />
          ) : (
            <>
              <BootstrapTable
                keyField='_id'
                data={eventRegistrationsList}
                columns={columns}
                bordered={false}
                hover
                selectRow={selectRow}
                headerClasses='header-class'
              />
            </>
          )}
        </div>
      </div>

      <DeleteModal
        onSubmit={deleteUser}
        toggle={toggleModal}
        title='Delete Ticket'
        open={showDelModal}
      />
      <AddGuestModal
        isOpen={showGuestModal}
        toggleModal={closeGuestModal}
        handleAfterEdit={handleAfterEdit}
      />
    </>
  );
};

export default EventAttendees;

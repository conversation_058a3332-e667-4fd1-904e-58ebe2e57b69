export const getIncompleteLessonsList = (sections) => {
    const lessons = []
    let allComplete = false;
    sections && sections.forEach((section) => {
      section?.lessons && section.lessons.forEach((lesson) => {
        if(lesson?.isCompleted === false){
            lessons.push(lesson)
        }
        else{     
           allComplete = lessons.every(lesson => lesson?.isCompleted);
        }
      })
    }) 
    return lessons.length ? lessons : allComplete
  } 
{"name": "datacode-creator", "version": "0.1.0", "private": true, "homepage": "https://datacode-creator.azurewebsites.net", "dependencies": {"@akshayDatacode/datacode-ui": "^0.1.7", "@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@mui/icons-material": "^5.15.19", "@mui/material": "^5.15.19", "@mui/x-date-pickers": "^6.18.7", "@testing-library/jest-dom": "^5.16.4", "@testing-library/react": "^13.3.0", "@testing-library/user-event": "^13.5.0", "@tinymce/tinymce-react": "^4.3.0", "antd": "^5.2.0", "axios": "^1.6.7", "bootstrap": "^4.6.0", "bootstrap-icons": "^1.11.3", "chart.js": "^4.4.4", "draft-js": "^0.11.7", "draftjs-to-html": "^0.9.1", "export-from-json": "^1.6.0", "formik": "^2.4.5", "highcharts": "^11.4.6", "highcharts-funnel": "^0.1.7", "highcharts-react-official": "^3.2.1", "html-react-parser": "^3.0.4", "html-to-draftjs": "^1.5.0", "html2canvas": "^1.4.1", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "moment": "^2.29.4", "prism-react-renderer": "^1.0.2", "prismjs": "^1.19.0", "react": "^18.2.0", "react-bootstrap-table-next": "^1.1.0", "react-chartjs-2": "^5.2.0", "react-codepen-embed": "^1.1.0", "react-color": "^2.19.3", "react-device-detect": "^2.2.3", "react-dom": "^18.2.0", "react-draft-wysiwyg": "^1.15.0", "react-error-boundary": "^4.0.13", "react-gist": "^1.2.4", "react-grid-gallery": "^1.0.1-alpha.0", "react-hot-toast": "^2.4.1", "react-icons": "^4.12.0", "react-loader-spinner": "^5.1.7-beta.1", "react-multi-carousel": "^2.8.4", "react-paginate": "^8.1.3", "react-redux": "^8.0.2", "react-router": "^6.3.0", "react-router-dom": "^6.3.0", "react-scripts": "5.0.1", "react-scroll": "^1.8.7", "react-simple-code-editor": "^0.13.1", "react-simple-star-rating": "^5.1.7", "react-slick": "^0.30.1", "react-syntax-highlighter": "^15.5.0", "react-tooltip": "^5.26.3", "react-widgets": "^5.8.4", "reactstrap": "^9.1.3", "redux": "^4.2.0", "redux-form": "^8.3.8", "redux-thunk": "^2.4.1", "sass": "^1.77.4", "web-vitals": "^2.1.4", "webpack": "^5.74.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11"}}
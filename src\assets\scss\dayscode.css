@charset "UTF-8";
@keyframes rotate-s-loader {
  from {
    transform: rotate(0);
  }
  to {
    transform: rotate(360deg);
  }
}
.dark-bg-color {
  background-color: #ffffff;
  position: fixed !important;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

.upper-header {
  min-height: 300px;
  background-color: #30006d;
}
.upper-header h1 {
  color: #ffffff !important;
}

.body-section {
  position: relative;
  top: -161px;
}

.header-banner {
  min-height: 400px;
  background-image: url("../images/svg/dayscode-header.svg");
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
}
.header-banner h1 {
  font-size: 45px;
  color: #353833;
  letter-spacing: 0.9px;
}

.enroll-btn {
  border: solid 1px #673de6;
  border-radius: 50px;
  min-width: 40%;
}
.enroll-btn span {
  color: #673de6;
  font-size: 16px;
  font-weight: 300;
}
.enroll-btn:hover {
  box-shadow: 0 3px 6px 0 rgba(0, 0, 0, 0.2);
  transition: ease-in-out 1ms;
  border: solid 1px #673de6;
  background-color: #7968ce;
}
.enroll-btn:hover span {
  transition: ease-in-out 1ms;
  color: #ffffff;
  font-size: 17px;
  font-weight: 500;
}

.enroll-small-btn {
  border: solid 1px #673de6;
  border-radius: 50px;
  min-width: 40%;
}
.enroll-small-btn small {
  color: #673de6;
  font-size: 12px;
}
.enroll-small-btn:hover {
  box-shadow: 0 3px 6px 0 rgba(0, 0, 0, 0.2);
  transition: ease-in-out 1ms;
  border: solid 1px #673de6;
  background-color: #7968ce;
}
.enroll-small-btn:hover small {
  transition: ease-in-out 1ms;
  color: #ffffff;
  font-size: 12px;
}

.enroll-small-white-btn {
  border: solid 2px #ffffff;
  border-radius: 50px;
}
.enroll-small-white-btn small {
  color: #000000;
  font-weight: 600;
  letter-spacing: 1px;
  font-size: 12px;
}
.enroll-small-white-btn:hover {
  box-shadow: 0 3px 6px 0 rgba(0, 0, 0, 0.2);
  transition: ease-in-out 1ms;
  border: solid 2px #673de6;
  background-color: #ffffff;
}
.enroll-small-white-btn:hover small {
  color: #000000;
  font-weight: 600;
  letter-spacing: 1px;
  font-size: 12px;
}

.about-code-section {
  font-weight: 300;
}
.about-code-section h1 {
  font-size: 36px;
  color: #353833;
  letter-spacing: 0.9px;
}
.about-code-section span {
  font-size: 15px;
}
.about-code-section h6 {
  color: #353833;
  font-size: 20px;
  font-weight: 400 !important;
}
.about-code-section .icon-card {
  padding: 15px;
  border: solid 1px #cdd0cb;
  border-radius: 5px;
}
.about-code-section .icon-card i {
  font-size: 30px;
  color: #ffffff;
}
.about-code-section .icon-card p {
  font-size: 14px;
  color: #ffffff;
}
.about-code-section ul li {
  list-style: none;
}
.about-code-section ul li span {
  font-size: 16px;
}
.about-code-section ul {
  padding: 0;
  margin: 0;
  list-style: none;
}
.about-code-section ul li {
  margin: 20px 0 5px 25px;
}
.about-code-section ul li::before {
  content: "\f00c";
  font-family: "Font Awesome 5 Pro";
  margin-top: 2px;
  margin-left: -25px;
  display: table;
  float: left;
  color: #7968ce;
  font-weight: 500;
}

.learning-objective {
  background-color: #f9fbfc;
  font-weight: 300;
}
.learning-objective ul li {
  list-style: none;
}
.learning-objective ul li span {
  font-size: 16px;
}
.learning-objective ul {
  padding: 0;
  margin: 0;
  list-style: none;
}
.learning-objective ul li {
  margin: 20px 0 5px 25px;
}
.learning-objective ul li::before {
  content: "\f00c";
  font-family: "Font Awesome 5 Pro";
  margin-top: 2px;
  margin-left: -25px;
  display: table;
  float: left;
  color: #7968ce;
  font-weight: 500;
}

.why-join-us h1 {
  font-size: 36px;
  color: #353833;
  letter-spacing: 0.9px;
}
.why-join-us h6 {
  color: #353833;
  line-height: 20px;
  font-size: 20px;
  margin-top: 15px;
  letter-spacing: 1px;
}

.code-dashboard {
  background: #ffffff;
  min-height: 200px;
  border-radius: 7px;
}
.code-dashboard h1 {
  font-size: 36px;
  color: #353833;
  letter-spacing: 0.9px;
}
.code-dashboard .qoute {
  font-size: 15px;
  color: #7968ce;
}
.code-dashboard .qoute h6 {
  color: #726f77;
  font-weight: 500;
  line-height: 22px;
}
.code-dashboard .quote-icon {
  position: relative;
  font-size: 25px;
}

.dashboard-heading {
  font-size: 20px;
  letter-spacing: 1px;
  color: #353833;
  padding-bottom: 15px;
  border-bottom: solid 1px #dee2e6;
}

.code-dashboard-notification {
  background: #ffffff;
  min-height: 200px;
  border-radius: 7px;
}
.code-dashboard-notification h1 {
  font-size: 36px;
  color: #353833;
  letter-spacing: 0.9px;
}

.large-text {
  font-size: 70px;
  color: #353833;
  font-weight: 100;
}

.lesson-dashboard-progress h1 {
  font-size: 45px;
}

.problem-banner-box {
  align-items: center;
  border-radius: 7px;
  height: 100%;
  display: grid;
  text-decoration: none !important;
}
.problem-banner-box i {
  font-size: 25px;
  padding: 22px;
  color: #7968ce;
}
.problem-banner-box h2 {
  color: #7968ce;
  text-align: center;
  font-size: 20px;
}
.problem-banner-box h2 i {
  font-size: 20px;
  padding: 10px;
}
.problem-banner-box:hover {
  border: solid 2px #7968ce !important;
  border-radius: 7px;
  background-color: #ffffff;
}
.problem-banner-box:hover a {
  text-decoration: none !important;
}
.problem-banner-box:hover h2 {
  text-decoration: none !important;
  color: #7968ce !important;
}
.problem-banner-box:hover i {
  color: #7968ce;
}

.solution-nav {
  cursor: pointer;
  box-shadow: 1px 1px 5px 1px rgba(59, 73, 94, 0.2);
}
.solution-nav .solutiob-col:hover {
  background-color: #dfe7ff;
}

.nav-active {
  color: #353833;
  font-weight: 600;
  border-bottom: solid 4px #7968ce;
}

.problems-side-section {
  overflow-y: scroll;
  overflow-x: hidden;
  height: 100vh;
}
.problems-side-section .side-problems-card {
  border-radius: 7px;
  box-shadow: 1px 1px 5px 1px rgba(59, 73, 94, 0.2);
}
.problems-side-section .side-problems-card:hover {
  cursor: pointer;
  box-shadow: 1px 1px 4px 2px rgba(59, 73, 94, 0.2);
  background-color: #dfe7ff;
}
.problems-side-section .side-problems-card .side-problem-text {
  font-weight: 500;
  font-size: 12px;
  color: #121212;
}
.problems-side-section .side-problems-card .difficulty-level-text {
  font-size: 12px;
  color: #888888;
  font-style: oblique;
}
.problems-side-section .side-problems-card-content-collapse {
  padding: 10px !important;
  border-radius: 7px;
  border-top: none;
}
.problems-side-section .side-problems-card-content-collapse ul {
  padding: 0;
  margin: 0;
  list-style: none;
}
.problems-side-section .side-problems-card-content-collapse ul li {
  font-size: 14px;
  padding: 5px 0;
}

.side-section-drawer {
  width: 5% !important;
  left: 0;
  transition: 0.5s all ease-out;
  display: block;
}
.side-section-drawer .problems-side-section {
  height: 90vh;
}
.side-section-drawer .solution-nav span,
.side-section-drawer .solution-nav small {
  display: none;
}
.side-section-drawer .side-problems-card {
  text-align: center !important;
}
.side-section-drawer .side-problems-card .tooltip-inner {
  display: none;
}
.side-section-drawer .side-problems-card p,
.side-section-drawer .side-problems-card .side-problem-text,
.side-section-drawer .side-problems-card .day-text,
.side-section-drawer .side-problems-card .carousel-icon,
.side-section-drawer .side-problems-card .fa-lock-open-alt {
  display: none;
}
.side-section-drawer .side-problems-card .pr-4 {
  padding: 0;
}
.side-section-drawer .side-problems-card .progress-dot {
  display: inline-block;
  transition: 0.5s all ease-in-out;
}
.side-section-drawer .side-problems-card .inner-box {
  display: inline-block;
  transition: 0.5s all ease-in-out;
}
.side-section-drawer .side-problems-card .day-text-close-drawer,
.side-section-drawer .side-problems-card i,
.side-section-drawer .side-problems-card span {
  text-align: center;
  justify-content: center;
}

.side-section-drawer.open {
  transition: 0.5s all ease-in-out;
  width: 20% !important;
}
.side-section-drawer.open .problems-side-section span,
.side-section-drawer.open .problems-side-section .day-text,
.side-section-drawer.open .problems-side-section small,
.side-section-drawer.open .solution-nav span,
.side-section-drawer.open .solution-nav .day-text,
.side-section-drawer.open .solution-nav small {
  display: block;
}
.side-section-drawer.open .problems-side-section {
  height: 70vh;
}
.side-section-drawer.open .text-section {
  padding-right: 20px;
}
.side-section-drawer.open .side-problems-card {
  justify-content: space-between;
  padding: 10px !important;
}
.side-section-drawer.open .side-problems-card p {
  display: block;
}
.side-section-drawer.open .side-problems-card .carousel-icon {
  display: inline;
}
.side-section-drawer.open .side-problems-card .inner-box {
  display: flex;
}
.side-section-drawer.open .side-problems-card small,
.side-section-drawer.open .side-problems-card span,
.side-section-drawer.open .side-problems-card .difficulty-level-text {
  text-align: left;
}
.side-section-drawer.open .side-problems-card .day-text-close-drawer {
  display: none;
}

.page-content-wrapper {
  width: 95% !important;
  position: relative;
  left: 0%;
  right: 0;
  transition: all 0.5s ease-in-out;
}

.side-section-drawer.open + .page-content-wrapper {
  position: relative;
  left: 0%;
  right: 0;
  width: 80% !important;
  transition: all 0.5s ease-in-out;
}

.problems-side-section::-webkit-scrollbar-track,
.problem-section::-webkit-scrollbar-track,
.solution-section::-webkit-scrollbar-track,
.practice-section::-webkit-scrollbar-track,
.submissions-list-section::-webkit-scrollbar-track,
.leaderboard-section::-webkit-scrollbar-track,
.notification-section::-webkit-scrollbar-track,
.lesson-section::-webkit-scrollbar-track,
.challenge-section::-webkit-scrollbar-track,
.daily-task-card-section::-webkit-scrollbar-track {
  border-radius: 10px;
  background-color: #f1efef;
}
.problems-side-section::-webkit-scrollbar,
.problem-section::-webkit-scrollbar,
.solution-section::-webkit-scrollbar,
.practice-section::-webkit-scrollbar,
.submissions-list-section::-webkit-scrollbar,
.leaderboard-section::-webkit-scrollbar,
.notification-section::-webkit-scrollbar,
.lesson-section::-webkit-scrollbar,
.challenge-section::-webkit-scrollbar,
.daily-task-card-section::-webkit-scrollbar {
  width: 3px;
  height: 5px;
  background-color: #fff;
}
.problems-side-section::-webkit-scrollbar-thumb,
.problem-section::-webkit-scrollbar-thumb,
.solution-section::-webkit-scrollbar-thumb,
.practice-section::-webkit-scrollbar-thumb,
.submissions-list-section::-webkit-scrollbar-thumb,
.leaderboard-section::-webkit-scrollbar-thumb,
.notification-section::-webkit-scrollbar-thumb,
.lesson-section::-webkit-scrollbar-thumb,
.challenge-section::-webkit-scrollbar-thumb,
.daily-task-card-section::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background-color: #c3c3c3;
}

.leaderboard-section,
.notification-section {
  overflow-y: scroll;
  height: 400px;
}

.submissions-list-section {
  overflow-y: scroll;
  overflow-x: hidden;
  height: 500px;
}

.dashboard-btn {
  color: #7968ce;
}

.selected {
  background-color: #def2fd;
}

.problem-section {
  overflow-y: scroll;
  overflow-x: hidden;
  height: 80vh;
  min-height: 100%;
}
.problem-section p {
  color: #353833;
}
.problem-section ul li {
  color: #726f77;
}

.lesson-section {
  overflow-y: scroll;
  overflow-x: hidden;
  height: 90vh;
  min-height: 100%;
}

.challenge-section {
  overflow-y: scroll;
  overflow-x: hidden;
  height: 75vh;
  min-height: 100%;
}

.solution-section {
  overflow-y: scroll;
  overflow-x: hidden;
  height: 600px;
  min-height: 100%;
}

.practice-section {
  overflow-y: scroll;
  overflow-x: hidden;
  height: 600px;
  min-height: 100%;
}

.daily-task-card-section {
  overflow-y: scroll;
  overflow-x: hidden;
  height: 500px;
  min-height: 100%;
}

.days-heading {
  font-size: 15px;
  color: #353833;
  letter-spacing: 1px;
}

.problem-statment {
  font-size: 45px;
  font-family: "Dosis", sans-serif !important;
}

.solution-header {
  border: 1px solid #a6a9b6;
  border-radius: 5px;
}
.solution-header h1 {
  font-size: 36px;
  color: #353833;
}
.solution-header h2 {
  font-size: 20px;
  color: #353833;
}

h1 {
  font-size: 36px;
  color: #353833;
  letter-spacing: 1px;
}

.solution-counter {
  display: grid;
  justify-content: center !important;
  align-content: center !important;
}
.solution-counter h1 {
  font-size: 36px;
  color: #726f77;
}

.wait-timer-icon {
  font-size: 100px;
  color: #726f77;
  animation-name: rotate-s-loader;
  animation-iteration-count: infinite;
  animation-duration: 15s;
  animation-timing-function: linear;
  position: relative;
  padding: 20px;
  justify-content: center;
  display: flex;
}

.problem-wait i {
  font-size: 100px;
  color: #726f77;
  padding: 20px;
}
.problem-wait h1 {
  font-size: 20px;
  color: #726f77;
}

.code-setting-side-nav ul {
  margin-top: 20px;
  margin-bottom: 0;
  padding: 0;
}
.code-setting-side-nav ul li {
  list-style: none;
  text-decoration: none !important;
}
.code-setting-side-nav ul li span {
  background-color: #ffffff;
  cursor: pointer;
  font-weight: 500;
  display: block;
  padding: 10px 10px;
  color: #30006d;
  font-size: 14px;
}
.code-setting-side-nav ul li span.active {
  color: #30006d;
  border-radius: 5;
  border-right: 5px solid #30006d;
  background-color: #f5f5f5;
}
.code-setting-side-nav ul li span.active::before {
  width: 3px;
}
.code-setting-side-nav ul li span:hover, .code-setting-side-nav ul li span:focus {
  text-decoration: none !important;
  background-color: #f8f8f8;
  color: #30006d;
}

.solution-code-textarea {
  max-height: 100%;
  width: 100%;
  height: 300px;
  padding-left: 12px;
  font-size: 14px;
  font-weight: normal;
  font-family: "Akzidenz", "‘Helvetica Neue’", Helvetica, Arial, sans-serif;
  z-index: 1;
  color: rgb(33, 49, 60);
  background-color: rgb(255, 255, 255);
  padding-right: 12px;
  border-radius: 5px;
  border-width: 1px;
  border-style: solid;
  -o-border-image: initial;
     border-image: initial;
  transition: border-color 150ms ease-in-out 0s;
  outline: none;
  border-color: rgb(184, 196, 194);
}

.mentor-review-card {
  width: 100%;
  min-height: 130px;
  background-color: #fbffc5;
  border-left: 8px solid #ffb830;
}
.mentor-review-card i {
  font-size: 35px;
}
.mentor-review-card h1 {
  font-size: 20px;
}

.yellow-card {
  background-color: #fbffc5;
  border-left: 8px solid #ffb830;
}

.green-card {
  background-color: #d6ffce;
  border-left: 8px solid #04a93b;
}

.voilet-card {
  background-color: rgba(215, 208, 255, 0.9450980392);
  border-left: 8px solid #7968ce;
}

.orange-card {
  background-color: #fce8df;
  border-left: 8px solid #ff5f15;
}

.blue-card {
  background-color: #e2f7f8;
  border-left: 8px solid #1d91fd;
}

.red-card {
  background-color: #fde5e7;
  border-left: 8px solid #ff2442;
}

.info-card {
  background-color: rgba(101, 255, 247, 0.6039215686);
  border-left: 8px solid #05947c;
}

.green-progrees-dot {
  background: #04a93b;
  color: #fff;
  border-radius: 100%;
  width: 25px;
  height: 25px;
  line-height: 25px;
  text-align: center;
  font-size: 18px;
  margin: 0 10px;
}

.yellow-progrees-dot {
  background: #ffb830;
  color: #fff;
  border-radius: 100%;
  width: 20px;
  height: 20px;
  text-align: center;
  font-size: 19px;
  margin: 0 10px;
}

.blue-progrees-dot {
  background: #1d91fd;
  color: #fff;
  border-radius: 100%;
  width: 20px;
  height: 20px;
  text-align: center;
  font-size: 19px;
  margin: 0 10px;
}

.red-progrees-dot {
  background: #ff2442;
  color: #fff;
  border-radius: 100%;
  width: 25px;
  height: 25px;
  line-height: 25px;
  text-align: center;
  font-size: 18px;
  margin: 0 10px;
}

.border-progrees-dot {
  background: white;
  border: 1px solid #a6a9b6;
  color: #fff;
  border-radius: 100%;
  width: 20px;
  height: 20px;
  text-align: center;
  font-size: 19px;
  margin: 0 10px;
}

.grey-progrees-dot {
  background: #cdd0cb;
  color: #fff;
  border-radius: 100%;
  width: 20px;
  height: 20px;
  text-align: center;
  font-size: 19px;
  margin: 0 10px;
}

.bg-red {
  background-color: #fb7367 !important;
}

.bg-green {
  background-color: #91fa70 !important;
}

.bg-yellow {
  background-color: #f5f852 !important;
}

.bg-dark-blue {
  background-color: #1d91fd !important;
}

.bg-dark-green {
  background-color: #04a93b !important;
}

.bg-light-voilet {
  background-color: #7968ce !important;
}

.bg-dark-pink {
  background-color: #ec0068 !important;
}

.bg-dark-yellow {
  background-color: #ffb830 !important;
}

.userlist-card p {
  font-size: 14px;
  color: #353833;
  letter-spacing: 0.3px;
}
.userlist-card span {
  font-size: 12px !important;
  font-weight: 300;
}

.days-code-hero {
  background-image: url("../images/bg/days-hero.jpeg");
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
}

.waiting-section {
  font-weight: 300;
}
.waiting-section ul li {
  list-style: none;
}
.waiting-section ul li span {
  font-size: 16px;
}
.waiting-section ul {
  padding: 0;
  margin: 0;
  list-style: none;
}
.waiting-section ul li {
  margin: 20px 0 5px 25px;
}
.waiting-section ul li::before {
  content: "\f00c";
  font-family: "Font Awesome 5 Pro";
  margin-top: 2px;
  margin-left: -25px;
  display: table;
  float: left;
  color: #7968ce;
  font-weight: 500;
}

.lesson-texteditor .rdw-editor-main {
  height: 500px;
}
.lesson-texteditor pre {
  background-color: rgb(16, 46, 70);
  color: #ffffff;
  padding: 12px;
  border-radius: 5px;
}
.lesson-texteditor pre span,
.lesson-texteditor pre p {
  color: #ffffff !important;
}

.lesson-content {
  margin-left: auto !important;
  margin-right: auto !important;
}

.lesson-content img,
.lesson-texteditor img,
.quiz-frame img {
  justify-content: center !important;
  align-items: center !important;
  text-align: center !important;
}
.lesson-content pre,
.lesson-texteditor pre,
.quiz-frame pre {
  background-color: rgb(16, 46, 70);
  color: #ffffff;
  padding: 12px;
  border-radius: 5px;
}
.lesson-content pre span,
.lesson-content pre p,
.lesson-texteditor pre span,
.lesson-texteditor pre p,
.quiz-frame pre span,
.quiz-frame pre p {
  color: #ffffff !important;
}
.lesson-content table td,
.lesson-texteditor table td,
.quiz-frame table td {
  border: 1px solid #e2f7f8;
}
.lesson-content ul li,
.lesson-texteditor ul li,
.quiz-frame ul li {
  padding-top: 15px;
}
.lesson-content p,
.lesson-texteditor p,
.quiz-frame p {
  font-size: 16px;
  letter-spacing: 0.5px !important;
  color: #1f1f1f;
  font-family: "Google Sans Text", "Roboto", "Helvetica Neue", Helvetica, sans-serif !important;
  line-height: 1.25rem;
}
.lesson-content h2,
.lesson-texteditor h2,
.quiz-frame h2 {
  border-bottom: 1px solid #353833;
  padding-bottom: 10px;
}
.lesson-content blockquote,
.lesson-texteditor blockquote,
.quiz-frame blockquote {
  border-left: 3px solid #30006d;
  margin-top: 30px;
}
.lesson-content blockquote span,
.lesson-texteditor blockquote span,
.quiz-frame blockquote span {
  padding-left: 5px;
}

.lesson-content-index .index-list-ul {
  list-style: none;
  padding: 0;
  border-left: 1px solid #cdd0cb;
}
.lesson-content-index .index-list-ul li {
  padding: 5px;
  cursor: pointer;
  margin: 10px 0;
}
.lesson-content-index .index-list-ul li:hover {
  border-left: 2px solid #7968ce;
  color: #7968ce;
}
.lesson-content-index .index-list-ul .active-topic {
  border-left: 2px solid #7968ce;
  background-color: #f5f5f5;
}

.quiz-challenge-option .mt-2 {
  margin-top: 0 !important;
}
.quiz-challenge-option .option-text {
  display: flex !important;
  align-items: center !important;
  margin: 0;
}
.quiz-challenge-option .option-text .text-left {
  margin-top: 0 !important;
}

.quiz-count-badge {
  background-color: #7968ce;
  border-radius: 30px;
  padding: 3px 8px;
  font-size: 14px;
  font-weight: 600;
  color: #ffffff;
}

.quiz-dot-status ul {
  list-style: none;
  margin: 0;
  padding: 0;
}
.quiz-dot-status .quiz-dot {
  border-radius: 50%;
  width: 34px;
  height: 34px;
  padding: 5px;
  background: #fff;
  border: 1px solid #353833;
  color: #000;
  text-align: center;
}
.quiz-dot-status .active {
  background: #1d91fd;
  color: #fff;
}

.quiz-frame p {
  font-size: 14px;
  font-weight: 300;
}

.content-steps-header .active {
  color: #7968ce;
  font-weight: 600;
}
.content-steps-header .content-section-steps .col-4 i {
  margin-right: 10px;
}
.content-steps-header .content-section-steps .col-4 span {
  background-color: #fff;
  padding: 0 10px;
}
.content-steps-header .content-section-steps .col-4::before {
  position: absolute;
  content: "";
  border-bottom: 2px solid #ccc;
  width: 100%;
  top: 25px;
  left: 0%;
  z-index: -1;
}

.program-steps-container {
  position: absolute;
  top: 17%;
  left: 0%;
  display: block;
  align-items: center;
  justify-content: center;
}

.program-steps-progressbar {
  counter-reset: step;
}

.program-steps-progressbar li {
  list-style-type: none;
  width: 16%;
  float: left;
  position: relative;
  text-align: center;
  text-transform: uppercase;
  color: #7d7d7d;
  top: -10px;
}
.program-steps-progressbar li i {
  font-size: 30px;
  margin-bottom: 5px;
  position: relative;
  top: -90px;
}
.program-steps-progressbar li p {
  position: relative;
  top: -30px;
  font-size: 12px;
}

.program-steps-progressbar li:before {
  width: 30px;
  height: 30px;
  content: counter(step);
  counter-increment: step;
  line-height: 30px;
  border: 2px solid #7d7d7d;
  display: block;
  text-align: center;
  margin: 0 auto 10px auto;
  border-radius: 50%;
  background-color: white;
}

.program-steps-progressbar li:after {
  width: 100%;
  height: 2px;
  content: "";
  position: absolute;
  background-color: #7d7d7d;
  top: 15px;
  left: -50%;
  z-index: -1;
}

.program-steps-progressbar li:first-child:after {
  content: none;
}

.program-steps-progressbar li.active {
  color: green;
}

.program-steps-progressbar li.active:before {
  border-color: #55b776;
}

.program-steps-progressbar li.active + li:after {
  background-color: #55b776;
}

.daily-task-card {
  border-radius: 7px !important;
}
.daily-task-card .morning {
  background-color: #ff4e00;
  background-image: linear-gradient(315deg, #ff4e00 0%, #ec9f05 74%);
}
.daily-task-card .morning h6 {
  color: #ffffff !important;
}
.daily-task-card .noon {
  background-image: linear-gradient(135deg, #fdeb71 10%, #f8d800 100%);
  border: #000000;
}
.daily-task-card .evening {
  background-color: #537895;
  background-image: linear-gradient(315deg, #537895 0%, #09203f 74%);
}
.daily-task-card .evening .heading-text,
.daily-task-card .evening p,
.daily-task-card .evening h6 {
  color: #ffffff !important;
}
.daily-task-card .daily-task-card-body h6 {
  font-weight: 500;
}
.daily-task-card .daily-task-card-body .certificate-progress-text {
  font-size: 16px;
}
.daily-task-card .step-dot {
  border-radius: 50%;
  width: 30px;
  height: 30px;
  padding: 5px;
  background: #fff;
  border: 1px solid #000;
  color: #000;
  text-align: center;
}

.certificate-progress .progress {
  height: 30px !important;
}

.dashabord-header-icons i:hover {
  background-color: rgba(245, 245, 245, 0.8274509804);
  border-radius: 5px;
  color: #30006d !important;
}

.dayscode-roadmap .roadmap-title {
  font-size: 18px;
  font-weight: 400;
}

.team-section-nav .list:hover {
  background-color: rgba(245, 245, 245, 0.3764705882);
}

.days-setting-layout .side-nav {
  position: fixed;
}
.speaker-card {
  min-height: 260px;
  margin: 10px;
  padding: 20px;
  box-shadow: 0 0 3px #626c70;
  border-radius: 15px;
  text-align: center;
}
.speaker-card .dlt-card-btn {
  width: 20px;
  height: 20px;
  color: #673de6;
  cursor: pointer;
}
.speaker-card .dlt-card-btn:active {
  background-color: #673de6;
}
.speaker-card .remove-btn:active {
  background-color: #673de6;
}

.search-section {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 10px;
}
.search-section .add-guest {
  padding: 5px 10px;
  color: #fff;
  background-color: #673de6;
  font-weight: 600;
  border: none;
  border-radius: 5px;
}
.search-section .add-guest:hover {
  background-color: transparent;
  color: #673de6;
  border: 1px solid #673de6;
}

.event-speaker-search-table .participant-search-card {
  padding: 10px;
  margin: 10px;
  border: 1px solid #673de6;
  border-radius: 10px;
  display: flex;
}
.event-speaker-search-table .participant-search-card h6 {
  font-size: 14px;
}
.event-speaker-search-table .participant-search-card p {
  font-size: 10px;
}
.event-speaker-search-table .speaker-search-card {
  padding: 10px;
  margin: 10px;
  border: 1px solid #673de6;
  border-radius: 10px;
  display: flex;
  justify-content: space-around;
  align-items: center;
}
.event-speaker-search-table .speaker-search-card h6 {
  font-size: 14px;
}
.event-speaker-search-table .speaker-search-card p {
  font-size: 10px;
}
.event-speaker-search-table .add {
  color: #673de6;
  cursor: pointer;
}

@media (max-width: 767px) {
  .code-dashboard .qoute h6 {
    font-size: 14px;
    letter-spacing: 1px;
  }
  .upper-header h4 {
    font-size: 16px;
  }
  .solution-counter h1 {
    font-size: 20px;
  }
  h1 {
    font-size: 22px;
  }
  .dashboard-btn {
    align-items: center;
    font-size: 14px;
  }
  .problems-side-section .row {
    padding: 0;
  }
  .problem-statment {
    font-size: 30px;
  }
  .side-section-drawer.open {
    width: 100% !important;
  }
  .side-section-drawer .page-content-wrapper {
    width: 100% !important;
  }
  .side-section-drawer.open + .page-content-wrapper {
    width: 100% !important;
  }
  .page-content-wrapper {
    border: none;
  }
  .solution-nav #toggle-icon {
    display: none;
  }
  .problem-banner-box h2 {
    color: #7968ce;
    text-align: center;
    font-size: 16px;
  }
  .problem-banner-box h2 i {
    font-size: 16px;
    padding: 5px;
  }
  .leaderboard-section,
  .notification-section {
    overflow-y: scroll;
    height: 300px;
  }
  .enroll-btn {
    align-items: center !important;
    justify-content: center;
    display: flex;
    width: 100%;
    margin-left: auto;
    margin-right: auto;
  }
  .about-code-section h6 {
    margin-top: 20px;
    font-size: 18px;
  }
  .about-code-section h1 {
    font-size: 22px !important;
    white-space: nowrap !important;
  }
  .why-join-us {
    align-items: center;
  }
  .why-join-us h6 {
    font-size: 16px;
  }
  .why-join-us h1 {
    font-size: 22px;
  }
  .learning-objective img {
    height: 100px;
    width: 100px;
  }
  .problem-section,
  .lesson-section,
  .solution-section,
  .practice-section,
  .daily-task-card-section {
    height: 70vh;
  }
  .lesson-content-index {
    display: none;
  }
  .challenge-section {
    height: 60vh;
  }
  .side-section-drawer.open .problems-side-section {
    height: 50vh;
  }
  .daily-task-card .daily-task-card-body h6 {
    font-size: 14px;
    letter-spacing: 1.2px;
  }
  .daily-task-card .certificate-progress-text {
    font-size: 15px;
    font-weight: 500;
  }
  .daily-task-card .assigment-status-text span {
    font-size: 14px !important;
  }
  .daily-task-card h1 {
    font-size: 30px;
  }
  .daily-task-card .step-dot {
    border-radius: 50%;
    width: 30px;
    height: 30px;
    padding: 5px;
    border: 1px solid #000000;
    color: #000000;
    text-align: center;
  }
  .large-text {
    font-size: 60px;
  }
  .content-steps-header .content-section-steps {
    font-size: 14px;
  }
  .content-steps-header .content-section-steps .col-4::before {
    top: 9px;
  }
  .content-steps-header .content-section-steps .task-icon {
    display: none;
  }
  .quiz-dot-status ul {
    display: flex;
    overflow-x: scroll;
    overflow-y: hidden;
  }
  .quiz-dot-status .quiz-dot {
    margin-right: 10px;
  }
  .code-setting-side-nav {
    margin: 0 10px 15px 10px;
    overflow-x: scroll;
  }
  .code-setting-side-nav ul {
    display: -webkit-box;
  }
  .code-setting-side-nav ul li {
    list-style: none;
    text-decoration: none !important;
    margin: 0 10px;
  }
  .code-setting-side-nav ul li span {
    border: 1px solid #353833;
    border-radius: 30px;
    padding: 10px;
    cursor: pointer;
  }
  .code-setting-side-nav ul li span.active {
    color: #30006d;
    border-radius: 5;
    border-right: none;
    border-bottom: 5px solid #30006d !important;
    background-color: #f5f5f5;
  }
  .days-setting-layout .side-nav {
    position: inherit;
  }
  .assigment-section .event-dashboard-header ul li span {
    font-weight: 400 !important;
    white-space: nowrap;
  }
  .give-away-alert img {
    height: 150px;
    width: 150px;
  }
  .give-away-alert h1 {
    font-size: 24px;
  }
  .give-away-alert p {
    display: none;
  }
  .dayscode-roadmap .roadmap-title {
    font-size: 14px;
    font-weight: 400;
    padding-left: 8px;
  }
}
.batch-header-section {
  border: 1px solid #ece4f2;
  border-radius: 14px;
  background-color: #ece4f2;
}
.batch-header-section h5 {
  font-size: 26px;
  font-weight: 600;
}
.batch-header-section p {
  font-size: 14px;
  font-weight: 400;
}
.batch-header-section:hover {
  background-color: #decfe8;
}

.problem-heading {
  color: #9dc269;
  font-size: 35px;
  font-weight: 600;
  padding: 10px 0px 10px 0px;
}

.lessons-heading {
  color: #9dc269;
  font-size: 35px;
  font-weight: 600;
  padding: 10px 0px 10px 0px;
}

.quiz-heading {
  color: #9dc269;
  font-size: 35px;
  font-weight: 600;
  padding: 10px 0px 10px 0px;
}

.mentor-heading {
  color: #9dc269;
  font-size: 35px;
  font-weight: 600;
  padding: 10px 0px 10px 0px;
}

.participants-heading {
  color: #9dc269;
  font-size: 35px;
  font-weight: 600;
  padding: 10px 0px 10px 0px;
}

.roadmap-heading {
  color: #9dc269;
  font-size: 35px;
  font-weight: 600;
  padding: 10px 0px 10px 0px;
}

.mentor-card {
  min-height: 250px;
  margin: 10px;
  text-align: center;
  overflow: hidden;
  position: relative;
  border: 2px solid #efedf5;
  border-radius: 15px;
  margin: 40px 80px 40px 80px;
}
.mentor-card .dlt-card-btn {
  width: 20px;
  height: 20px;
  color: #f0eef5;
  cursor: pointer;
}
.mentor-card .dlt-card-btn:active {
  background-color: #673de6;
}
.mentor-card .remove-btn:active {
  background-color: #673de6;
}
.mentor-card .upper-card-portion {
  background-image: url("../images/bg/days-hero.jpeg");
  background-size: cover;
  height: 150px;
}
.mentor-card .mentor-image {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  overflow: hidden;
  margin: -50px auto -30px auto;
  border: 2px solid black;
}
.mentor-card .mentor-details {
  margin-top: 64px;
  margin-bottom: -75px;
  height: 200px;
}
.mentor-card .mentor-details p {
  font-size: 20px;
  font-weight: 700;
}
.mentor-card .mentor-details h5 {
  font-size: 15px;
  font-weight: 300;
}
.mentor-card .mentor-image img {
  display: block;
  height: auto;
  width: 100%;
}

.problem-list-card {
  border-radius: 1.5rem;
}
.problem-list-card:hover {
  transform: scale(1.01);
  background-color: #fafafa;
}

.side-navbar-icons {
  font-size: 18px;
}/*# sourceMappingURL=dayscode.css.map */


import moment from "moment"

export const required = value => (value || typeof value === 'number' ? undefined : 'Required')

export const maxLength = max => value =>
  value && value.length > max ? `Must be ${max} characters or less` : undefined

export const minLength = min => value =>
  value && value.length < min ? `Must be ${min} characters or more` : undefined

export const number = value =>
  value && isNaN(Number(value)) ? 'Must be a number' : undefined

export const minValue = min => value =>
  value && value < min ? `Must be at least ${min}` : undefined

export const email = value =>
  value && !/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,4}$/i.test(value)
    ? 'Invalid email address'
    : undefined

export const aol = value =>
  value && /.+@aol\.com/.test(value)
    ? 'Really? You still use AOL for your email?'
    : undefined

export const alphaNumeric = value =>
  value && /[^a-zA-Z0-9 ]/i.test(value)
    ? 'Only alphanumeric characters'
    : undefined

export const phoneNumber = value =>
  value && !/^(0|[1-9][0-9]{9})$/i.test(value)
    ? 'Invalid phone number, must be 10 digits'
    : undefined

export const dateTimeDiff = value => {
  let start_date_time = moment(value.start_date + ' ' + value.start_time).format()
  let end_date_time = moment(value.end_date + ' ' + value.end_time).format()
  const errors = {}
  if (start_date_time > end_date_time) {
    errors.end_date = 'End Date/Time must be greate than start Date/Time'
  }
  return errors
}

export const matchPassword = values => {
  const errors = {}

  if (values.confirmPassword !== values.password) {
    errors.confirmPassword = 'Password not matched'
  }

  return errors
}


export const validateBatchDates = value => {
  let registration_end_date = moment(value.reg_end_date).format();
  let registration_start_date = moment(value.reg_start_date).format();
  let batch_start_date = moment(value.start_date).format();
  let batch_end_date = moment(value.end_date).format();
  
  const errors = {};
  
  if (registration_end_date > batch_start_date) {
    errors.reg_end_date = 'Registration end date must be on or before the Batch Start Date';
  }

  if (registration_start_date > batch_start_date) {
    errors.reg_start_date = 'Registration start date must be on or before the Batch Start Date';
  }
  if (batch_end_date < batch_start_date) {
    errors.end_date = 'End date should be on or after start_date';
  }
  if ( registration_start_date > registration_end_date){
    errors.registration_end_date = 'Reg end date cannot be before reg start date';
  }
  return errors;
}

import React, { useState } from 'react';

const ReadMore = ({ text = '', maxLength }) => {
  const [isTruncated, setIsTruncated] = useState(true);

  const toggleTruncate = () => {
    setIsTruncated(!isTruncated);
  };

  // Ensure text exists before slicing it
  const truncatedText = text && text.length > maxLength ? text.slice(0, maxLength) + '...' : text;

  return (
    <div>
      {isTruncated ? (
        <p>
          {truncatedText}
          {text.length > maxLength && <p className='read-more' onClick={toggleTruncate}>Read more...</p>}
        </p>
      ) : (
        <p>
          {text}
          <p  className='read-more'onClick={toggleTruncate}>Read less</p>
        </p>
      )}
    </div>
  );
};

export default ReadMore;
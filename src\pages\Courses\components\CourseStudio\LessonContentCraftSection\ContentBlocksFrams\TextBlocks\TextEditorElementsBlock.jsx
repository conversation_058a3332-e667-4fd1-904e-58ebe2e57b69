import React, { useEffect, useState } from 'react'
import { useDispatch } from 'react-redux'
import Combobox from "react-widgets/Combobox";

import TextEditor from '../../../../../../../components/sharedComponents/TextEditor'
import { getOptions } from '../../../../../../../components/utils';
import { editContent } from '../../../../../actions'
import ContentBlockLayout from '../ContentBlockLayout'

const TextEditorElementsBlock = ({ item }) => {
  
  const dispatch = useDispatch()

  const [ descriptionText, getDescriptionText ] = useState('<p></p>')
  const [ element, setElement ] = useState()
  const elementOptions = [
    { name: 'Info', value: 'info' },
    { name: 'Tip', value: 'tip' },
    { name: 'Warning', value: 'warning' },
    { name: 'Note', value: 'note' },
    { name: 'Fact', value: 'fact' },
    { name: 'Important', value: 'important' },
  ]

  useEffect(() => {
    setElement(item.subType)
  }, [])

  const handleDescriptionText = (text) => {
    getDescriptionText(text)
  }

  const handleSubmit = () => {
    const data = {
      id: item._id,
      subType: element.value,
      payload: descriptionText
    }
    dispatch(editContent(data))
  }

  const headerHtml = () => {
    return (

      <>
        <div className='embeds-block-header d-flex justify-content-between'>
          <div>
            <div className='d-flex embeds-block-title'>
              <i className="fa-regular fa-pen-to-square" />
              <p className='mb-0'>Text Editor Elements</p>
            </div>
            <Combobox
              data={ getOptions(elementOptions, 'subType') }
              dataKey={ "value" }
              textField={ 'subType' }
              placeholder={ 'Text Element' }
              value={ element }
              onChange={ (value) => setElement(value) }
            />
          </div>
        </div>
      </>
    )
  }

  return (
    <>
      <ContentBlockLayout item={ item } blockHeader={ headerHtml() } previewVisibility={ true }>
        <div className="row mx-0 border m-2">
          <div className="col-12">
            {
              item.payload ?
                <TextEditor handleTextEditor={ handleDescriptionText } text={ item.payload } />
                :
                <TextEditor handleTextEditor={ handleDescriptionText } text={ descriptionText } />
            }
            <div className='d-flex justify-content-end pb-2'>
              <button onClick={ () => handleSubmit() } type="submit" className="btn btn-primary">Save</button>
            </div>
          </div>
        </div>
      </ContentBlockLayout>
    </>
  )
}
export default TextEditorElementsBlock
import { GoPlus } from "react-icons/go";

const SpeakersCard = ({ setActiveTab, eventDetails }) => {
    return (
      <>
        <div className='card p-4 rounded-15'>
          <div className='mb-3 card-head'>
            Speakers <span className='rounded-icon ml-2' onClick={() => setActiveTab("speaker")} ><GoPlus /></span>
          </div>
          <div className='d-flex flex wrap row'>{eventDetails?.speakers?.map(speaker => (
            <div key={speaker._id} className='col-xl-3 col-lg-4 col-md-5 col-5 border-dashed-purple mx-3 p-3' style={{ backgroundColor: "#f1edfd" }}>
              <div className='d-flex row'>
                <div className='d-flex flex-column col-5'>
                  <img src={`${speaker?.imgUrl}`} className='rounded-image' alt=""></img>
                  <span className='mt-1 bio'>{speaker?.bio}</span>
                </div>
                <div className='d-flex align-items-center  col-7 px-0 name'>{speaker?.name}</div>
              </div>
            </div>
          ))}</div>
        </div>
      </>
    )
  }

  export default SpeakersCard;
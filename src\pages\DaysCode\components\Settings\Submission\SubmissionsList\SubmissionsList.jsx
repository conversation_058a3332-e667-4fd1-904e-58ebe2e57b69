import React, { useEffect, useState } from "react";
import _ from "lodash";
import { useDispatch, useSelector } from "react-redux";

import LayoutContainer from "../../LayoutContainer";
import CustomLoader from "../../../../../../components/sharedComponents/CustomLoader";
import DeleteProblemModal from "../../Problems/ProblemsList/DeleteProblemModal";
import CompilerModal from "../../../../../../components/Compiler/CompilerModal";
import SubmissionFrame from "./SubmissionFrame";
import ToolTip from "../../../../../../components/sharedComponents/ToolTip";
import {
  approveUserCodeSubmission,
  getAllSubmissions,
  getAllSubmissionsByUser,
  getAllSubmissionsByProblem,
  resetCodeBatchSubmission,
  getAllSubmissionsByBatch,
  getParticipants,
  getBatchParticipants,
} from "../../../../actions";
import BatchContentHeader from "../../DaysCodeBatch/BatchContentHeader";
import NoDataBlock from "../../../../../../components/sharedComponents/NoDataBlock";

const SubmissionsList = ({ deleteUserSubmission, batchId, type }) => {
  
  const dispatch = useDispatch();
  const currentUser = useSelector((state) => state.auth.currentUser);

  const [problem, setproblem] = useState();
  const [openModal, setOpenModal] = useState(false);
  const [openCompilerModal, setOpenCompilerModal] = useState(false);
  const [tempDeleteId, setTempDeleteId] = useState();
  const [tempCode, setTempCode] = useState();
  const [tempLanguage, setTempLanguage] = useState();
  const [listView, setListView] = useState("grid");
  const [user, setUser] = useState();
  const { allSubmissions, getUserSubmissionsLoading } = useSelector(
    ({ dayscode }) => dayscode,
  );


  useEffect(() => {
    if (type === "batchSubmission") {
      dispatch(getAllSubmissionsByBatch(batchId)).then(() => {
        dispatch(getBatchParticipants({ batch: batchId }));
      });
    } else {
      dispatch(getAllSubmissions());
    }
    return () => {
      dispatch(resetCodeBatchSubmission());
    };
  }, [currentUser, type, dispatch]);

  const onChangeProblemOption = (problem) => {
    setproblem(problem);
    dispatch(getAllSubmissionsByProblem(problem));
  };

  const onChangeUserOption = (user) => {
    console.log(user, "userrr");
    setUser(user);
    dispatch(getAllSubmissionsByUser(user));
  };

  const handleSubmittionApproval = (reviewData) => {
    dispatch(
      approveUserCodeSubmission({ id: reviewData.id, data: reviewData }),
    );
    if (problem !== "" || user !== "") {
      problem
        ? dispatch(getAllSubmissionsByProblem(problem))
        : dispatch(getAllSubmissionsByUser(user));
    } else {
      dispatch(getAllSubmissions());
    }
  };

  const toggleModal = () => {
    setOpenModal(!openModal);
  };

  const toggleCompilerModal = () => {
    setOpenCompilerModal(!openCompilerModal);
  };

  const handleDeleteSolution = () => {
    deleteUserSubmission(tempDeleteId.id, tempDeleteId.userName).then((res) => {
      if (res) {
        setOpenModal(!openModal);
        if (problem) {
          onChangeProblemOption(problem);
          // onChangeStatus(status);
        }
        if (user) {
          onChangeUserOption(user);
          // onChangeStatus(status);
        }
      }
    });
  };

  const deleteSolution = (id, userName) => {
    setTempDeleteId({ id, userName });
    setOpenModal(!openModal);
  };

  const compileSolution = (code, language) => {
    setTempCode(code);
    setOpenCompilerModal(!openCompilerModal);
    setTempLanguage(language);
  };

  return (
    <>
      {type === "batchSubmission" ? (
        <>
          <BatchContentHeader
            type={"Submissions"}
            batchId={batchId}
            onChangeProblemOption={onChangeProblemOption}
            onChangeUserOption={onChangeUserOption}
          />
          <div className='px-0'>
            <div className='row mx-5'>
              <div className='col-12 d-flex align-items-center p-3 border-bottom rounded-lg justify-content-end'>
                <div className=''>
                  <span
                    id='table'
                    onClick={() => setListView("table")}
                    className='p-2 mr-3 border rounded'
                  >
                    <ToolTip
                      message={"Table View"}
                      id='table'
                      placement='bottom'
                    >
                      <i className='fas fa-list' />
                    </ToolTip>
                  </span>

                  <span
                    id='grid'
                    onClick={() => setListView("grid")}
                    className='p-2 border rounded'
                  >
                    <ToolTip message={"Grid View"} id='grid' placement='bottom'>
                      <i className='fas fa-th-large' />
                    </ToolTip>
                  </span>
                </div>
              </div>
            </div>
          </div>
          {listView === "grid" ? (
            <>
              {getUserSubmissionsLoading ? (
                <CustomLoader />
              ) : (
                <div className='row m-0 my-5'>
                  <div className='col-12 px-0'>
                    {!_.isEmpty(allSubmissions) ? (
                      <>
                        {allSubmissions &&
                          allSubmissions.map((solution, i) => (
                            <SubmissionFrame
                              i={i}
                              handleSubmittionApproval={
                                handleSubmittionApproval
                              }
                              deleteSolution={deleteSolution}
                              solution={solution}
                              problemId={problem?.value}
                              day={user?.value ? solution.day : ""}
                              compileSolution={compileSolution}
                              problem={problem}
                              currentUser={currentUser}
                            />
                          ))}
                      </>
                    ) : (
                      <NoDataBlock />
                    )}
                  </div>
                  <CompilerModal
                    open={openCompilerModal}
                    toggle={toggleCompilerModal}
                    solutionSubmitted={true}
                    preCode={tempCode}
                    preCompilerLanguage={tempLanguage}
                  />
                  <DeleteProblemModal
                    open={openModal}
                    toggle={toggleModal}
                    onSubmit={handleDeleteSolution}
                    submitButtonName={"Delete User Solution"}
                  />
                </div>
              )}
            </>
          ) : (
            <>
              <h1>Table</h1>
            </>
          )}
        </>
      ) : (
        <>
          <LayoutContainer>
            <BatchContentHeader
              type={"Submission"}
              batchId={batchId}
              onChangeProblemOption={onChangeProblemOption}
              onChangeUserOption={onChangeUserOption}
            />
            <div className='px-0'>
              <div className='row mx-5'>
                <div className='col-12 d-flex align-items-center p-3 border-bottom rounded-lg justify-content-end'>
                  <div className=''>
                    <span
                      id='table'
                      onClick={() => setListView("table")}
                      className='p-2 mr-3 border rounded'
                    >
                      <ToolTip
                        message={"Table View"}
                        id='table'
                        placement='bottom'
                      >
                        <i className='fas fa-list' />
                      </ToolTip>
                    </span>

                    <span
                      id='grid'
                      onClick={() => setListView("grid")}
                      className='p-2 border rounded'
                    >
                      <ToolTip
                        message={"Grid View"}
                        id='grid'
                        placement='bottom'
                      >
                        <i className='fas fa-th-large' />
                      </ToolTip>
                    </span>
                  </div>
                </div>
              </div>
            </div>
            {listView === "grid" ? (
              <>
                {getUserSubmissionsLoading ? (
                  <CustomLoader />
                ) : (
                  <div className='row m-0 my-5'>
                    <div className='col-12 px-0'>
                      {!_.isEmpty(allSubmissions) ? (
                        <>
                          {allSubmissions &&
                            allSubmissions.map((solution, i) => (
                              <SubmissionFrame
                                i={i}
                                handleSubmittionApproval={
                                  handleSubmittionApproval
                                }
                                deleteSolution={deleteSolution}
                                solution={solution}
                                problemId={problem?.value}
                                day={user?.value ? solution.day : ""}
                                compileSolution={compileSolution}
                                problem={problem}
                                currentUser={currentUser}
                                key={i}
                              />
                            ))}
                        </>
                      ) : (
                        <NoDataBlock />
                      )}
                    </div>
                    <CompilerModal
                      open={openCompilerModal}
                      toggle={toggleCompilerModal}
                      solutionSubmitted={true}
                      preCode={tempCode}
                      preCompilerLanguage={tempLanguage}
                    />
                    <DeleteProblemModal
                      open={openModal}
                      toggle={toggleModal}
                      onSubmit={handleDeleteSolution}
                      submitButtonName={"Delete User Solution"}
                    />
                  </div>
                )}
              </>
            ) : (
              <>
                <h1>Table</h1>
              </>
            )}
          </LayoutContainer>
        </>
      )}
    </>
  );
};

export default SubmissionsList;

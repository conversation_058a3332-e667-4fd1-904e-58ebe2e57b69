import React from 'react'

const StatusBadge = ({ status }) => {
  const color = (status) => {
    let obj = {
      reject: '#FF0000',
      pending: '#FFA500',
      accept: "#008000",
      waiting: "#",
      onhold: "#808080",
      flag: "#0000FF",
      stage1: "#800080",
      stage2: "#8B4513",
    }
    return obj[status]
  }

  const style = {
    backgroundColor: color(status),
    color: "#fff",
    border: "1px",
    borderRadius: "50px",
  }

  return (
    <>
      <span style={style} className={`px-4 py-2 status-badge`}>{status}</span>
    </>
  )
}

export default StatusBadge
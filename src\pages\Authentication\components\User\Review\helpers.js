import moment from "moment";
import CustomStarRating from "../../../../../components/sharedComponents/CustomStarRating";
import { DownOutlined } from '@ant-design/icons';
import { Dropdown, Space } from "antd";

export const userCategoryOptions = [
    { name: "SL<PERSON>", value: "SLC" },
    { name: "Intern", value: "Intern" },
    { name: "<PERSON>", value: "Volunteer" },
    { name: "<PERSON><PERSON>", value: "<PERSON><PERSON>" },
  ];


  export const getColumns = (selectedReview , items) => [
    {
      dataField: "",
      text: "S.NO.",
      align: "center",
      headerAlign: "center",
      headerClasses: "table-header s-no",
      formatter: (cell, row, index) => <span>{++index}</span>,
    },
    {
      dataField: "",
      text: "Name",
      sort: true,
      style: { color: "#757575" },
      headerClasses: "table-header status",
    },
    {
      dataField: "category",
      text: "Category",
      sort: true,
      style: { color: "#757575" },
      headerClasses: "table-header status",
    },
  
    {
      dataField: "review",
      text: "Review",
      sort: true,
      style: { color: "#757575" },
      align: "left",
      headerAlign: "left",
      headerClasses: "table-header group-name",
    },
    
    {
      dataField: "createdAt",
      text: " Date",
      sort: true,
      style: { color: "#757575" },
      align: "left",
      headerAlign: "left",
      headerClasses: "table-header group-name",
      formatter: (cell, row) => (
        <span>
          {row.createdAt === null
            ? "Invalid date"
            : moment(row.createdAt).format("MM-DD-YYYY")}
        </span>
      ),
    },
    {
        dataField: "rating",
        text: "Rating",
        align: "center",
        headerAlign: "center",
        headerClasses: "table-header s-no",
        formatter: (cell, row, index) => <span><CustomStarRating initialValue={row.rating} readonly={true}/></span>,
      },
    {
      dataField: "",
      text: " Actions",
      style: { color: "#757575" },
      align: "left",
      headerAlign: "left",
      headerClasses: "table-header group-name",
      formatter: (cell, row) => (
        <Dropdown
        menu={{ items }}
        trigger={'click'}
        key={`actions-${row._id}`}
      >
        <div
          style={{ backgroundColor: "white" }}
          className="btn bg-light"
          onClick={(e) => {
            console.log(selectedReview.current)
            selectedReview.current = row;
          }}
        >
          <Space>
            Actions
            <DownOutlined />
          </Space>
        </div>
      </Dropdown>
      ),
    }
  ];
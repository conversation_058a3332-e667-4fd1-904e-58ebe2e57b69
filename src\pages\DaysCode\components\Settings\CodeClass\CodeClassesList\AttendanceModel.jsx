import React, { useState, useEffect } from "react";
import { FaTrash } from "react-icons/fa";
import { useDispatch, useSelector } from "react-redux";

import {
  getCodeClassParticipants,
  deleteAttendanceFromClass,
} from "../../../../actions";
import CustomFormModal from "../../../../../../components/sharedComponents/CustomFormModal";

const AttendanceModel = ({
  model,
  toggle,
  handleAttendanceSubmission,
  codeClassId,
  codeClassPresentList,
}) => {
  const dispatch = useDispatch();
  const { codeClassParticipants } =
    useSelector((state) => state.dayscode) || {};

  const [attendance, setAttendance] = useState([]);
  const [isPresentListVisible, setIsPresentListVisible] = useState(false);
  const [isAbsentListVisible, setIsAbsentListVisible] = useState(false);

  useEffect(() => {
    if (model && codeClassId) {
      dispatch(getCodeClassParticipants(codeClassId));
    }
  }, [model, codeClassId, dispatch]);

  const handlePresentListToggle = () => {
    setIsPresentListVisible(!isPresentListVisible);
  };
  const handleAbsentListToggle = () => {
    setIsAbsentListVisible(!isAbsentListVisible);
  };
  const handleCheckboxChange = (studentId) => {
    setAttendance((prevState) => ({
      ...prevState,
      [studentId]: !prevState[studentId],
    }));
  };

  const onDeleteAttendance = (participant) => {
    alert(participant);
    dispatch(
      deleteAttendanceFromClass({ codeClass: codeClassId, participant }),
    );
  };

  const presentStudents = Object.keys(attendance);

  return (
    <>
      <CustomFormModal
        isOpen={model}
        toggleModal={toggle}
        title={"Class Attendance"}
        onSubmit={() =>
          handleAttendanceSubmission(codeClassId, presentStudents)
        }
        onSubmitTitle={"Submit"}
      >
        <div>
          {/* <h5 className='mr-4 '>Class Attendance:</h5>
          <p>Students: {codeClassParticipants?.length}</p> */}
          <div className='event-dashboard-header w-100 px-0 table-responsive table'>
            <ul>
              <li>
                <span
                  onClick={() => handlePresentListToggle()}
                  className='toggle-button'
                >
                  {isPresentListVisible
                    ? "Hide Present List"
                    : "Show Present List"}
                </span>
              </li>
              <li>
                <span
                  onClick={() => handleAbsentListToggle()}
                  className='toggle-button'
                >
                  Absent
                </span>
              </li>
            </ul>
          </div>
          <div className='present-student-list'>
            {isPresentListVisible && (
              <div className='list-container'>
                <h6>Present Student List</h6>
                {codeClassPresentList &&
                  codeClassPresentList.map((presentStudent) => (
                    <div className='row mx-0'>
                      <div className='col-6 col-md-6'>
                        <p className='user-list' key={presentStudent?._id}>
                          {presentStudent?.user?.firstName}
                        </p>
                      </div>
                      <div className='col-6 col-md-6 text-danger'>
                        <FaTrash
                          onClick={() =>
                            onDeleteAttendance(presentStudent?._id)
                          }
                        />
                      </div>
                    </div>
                  ))}
              </div>
            )}
          </div>

          <div className='absent-student-list'>
            {isAbsentListVisible && (
              <div className='list-container'>
                <h6>Absent Student List</h6>
                <div className='row mx-0'>
                  <div className='col-12 col-md-6'>
                    <p className='user-list'>Akshay</p>
                  </div>
                </div>
              </div>
            )}
          </div>
          <div className='class-attendance mt-3 border rounded p-3 '>
            {codeClassParticipants &&
              codeClassParticipants.map((student) => (
                <div className='row mx-0' key={student?.codeUser?._id}>
                  <div className='col-6 col-md-6'>
                    <p className='user-list' >
                      {student?.codeUser?.name}{" "}
                    </p>
                  </div>
                  <div className='col-6 col-md-6'>
                    <input
                      className='ml-3'
                      type='checkbox'
                      checked={attendance[student?.codeUser?._id] || false}
                      onChange={() =>
                        handleCheckboxChange(student?.codeUser?._id)
                      }
                    />
                  </div>
                </div>
              ))}
          </div>
        </div>
      </CustomFormModal>
      {/* <Modal centered isOpen={model} toggle={toggle} className='text-center'>
        <ModalHeader
          className='d-flex justify-content-between align-items-center'
          toggle={toggle}
        ></ModalHeader>
        <ModalBody>
          <div>
            <h5 className='mr-4 '>Class Attendance:</h5>
            <p>Students: {codeClassParticipants?.length}</p>
            <div className='event-dashboard-header w-100 px-0 table-responsive table'>
              <ul>
                <li>
                  <span
                    onClick={() => handlePresentListToggle()}
                    className='toggle-button'
                  >
                    {isPresentListVisible
                      ? "Hide Present List"
                      : "Show Present List"}
                  </span>
                </li>
                <li>
                  <span
                    onClick={() => handleAbsentListToggle()}
                    className='toggle-button'
                  >
                    Absent
                  </span>
                </li>
              </ul>
            </div>
            <div className='present-student-list'>
              {isPresentListVisible && (
                <div className='list-container'>
                  <h6>Present Student List</h6>
                  {codeClassPresentList &&
                    codeClassPresentList.map((presentStudent, i) => (
                      <div className='row mx-0'>
                        <div className='col-6 col-md-6'>
                          <p className='user-list' key={presentStudent?._id}>
                            {presentStudent?.user?.firstName}
                          </p>
                        </div>
                        <div className='col-6 col-md-6 text-danger'>
                          <FaTrash
                            onClick={() =>
                              onDeleteAttendance(presentStudent?._id)
                            }
                          />
                        </div>
                      </div>
                    ))}
                </div>
              )}
            </div>

            <div className='absent-student-list'>
              {isAbsentListVisible && (
                <div className='list-container'>
                  <h6>Absent Student List</h6>
                  <div className='row mx-0'>
                    <div className='col-12 col-md-6'>
                      <p className='user-list'>Akshay</p>
                    </div>
                  </div>
                </div>
              )}
            </div>
            <div className='class-attendance mt-3 border rounded p-3 '>
              {codeClassParticipants &&
                codeClassParticipants.map((student) => (
                  <div className='row mx-0'>
                    <div className='col-6 col-md-6'>
                      <p className='user-list' key={student?.codeUser?._id}>
                        {student?.codeUser?.name}{" "}
                      </p>
                    </div>
                    <div className='col-6 col-md-6'>
                      <input
                        className='ml-3'
                        type='checkbox'
                        checked={attendance[student?.codeUser?._id] || false}
                        onChange={() =>
                          handleCheckboxChange(student?.codeUser?._id)
                        }
                      />
                    </div>
                  </div>
                ))}
            </div>
          </div>
        </ModalBody>
        <ModalFooter className='d-flex justify-content-center align-items-center'>
          <Button
            color='success'
            onClick={() =>
              handleAttendanceSubmission(codeClassId, presentStudents)
            }
          >
            Submit
          </Button>
        </ModalFooter>
      </Modal> */}
    </>
  );
};

export default AttendanceModel;

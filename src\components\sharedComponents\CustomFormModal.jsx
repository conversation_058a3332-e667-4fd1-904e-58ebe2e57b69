import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Mo<PERSON>Footer } from "reactstrap";

const CustomFormModal = ({
  children,
  title = "",
  titleIcon,
  isOpen,
  toggleModal,
  onSubmitTitle,
  onSubmit = () => {},
  onCancel,
  width = "500px",
}) => {
  const closeBtn = (
    <button className='close' onClick={toggleModal} type='button'>
      &times;
    </button>
  );
  return (
    <>
      <Modal
        isOpen={isOpen}
        toggle={toggleModal}
        className='add-guest-modal'
        style={{ width: width }}
      >
        <ModalHeader
          toggle={toggleModal}
          close={closeBtn}
          className='add-guest-modal-header text-white '
        >
          <div className='d-flex align-items-center'>
            {titleIcon}
            <span className=''>{title}</span>
          </div>
        </ModalHeader>
        <ModalBody>{children}</ModalBody>
        <ModalFooter>
          <Button className='add-guest-btn' onClick={onSubmit}>
            {onSubmitTitle}
          </Button>
          <Button color='secondary' onClick={toggleModal}>
            Cancel
          </Button>
        </ModalFooter>
      </Modal>
    </>
  );
};

export default CustomFormModal;

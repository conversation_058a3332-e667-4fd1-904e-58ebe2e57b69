import { Button, Space } from "antd";
import React, { useEffect } from "react";
import { FaFilter } from "react-icons/fa6";

const CustomFilter = ({
  children,
  handleOk,
  handleReset,
  isOpen,
  setIsOpen,
}) => {
  const handleOutsideClick = (event) => {
    if (!event.target.closest(".filter")) {
      setIsOpen(false);
    }
  };

  useEffect(() => {
    if (isOpen) {
      document.addEventListener("click", handleOutsideClick);
    }
    return () => {
      document.removeEventListener("click", handleOutsideClick);
    };
  }, [isOpen]);

  return (
    <>
      <div className='filter-Modal-content'>
        <Space>
          <Button
            className='filter-modal-button'
            onClick={() => setIsOpen(!isOpen)}
          >
            <FaFilter className='mr-2' />
            Filter
          </Button>
        </Space>
        {isOpen ? (
          <>
            <div className='filter'>
              <div>
                <span>Filter Options</span>
              </div>
              <div className='empty-line'></div>
              <div>{children}</div>
              <div className='mt-4 filter-buttons'>
                <button
                  className='btn btn-secondary mx-2'
                  onClick={handleReset}
                >
                  Reset
                </button>
                <button className='btn btn-primary mx-2' onClick={handleOk}>
                  Apply
                </button>
              </div>
            </div>
          </>
        ) : (
          ""
        )}
      </div>
    </>
  );
};

export default CustomFilter;

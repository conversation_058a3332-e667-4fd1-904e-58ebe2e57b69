import axios from "axios";
import * as actions from "./actionCreators";
import toast from "react-hot-toast";
import {
  SET_EVENT_REGISTRATION_LOADING,
  GET_EVENT_DETAILS_LOADING,
  GET_EVENTS_LIST_LOADING,
  SET_SEARCH_EVENTS_LIST,
  SET_SEARCH_EVENTS_LIST_LOADING,
  GET_EVENT_REGISTRATIONS_LOADING,
  SET_EDIT_EVENT_LOADING,
  SET_EVENT_FORM_FIELD_LOADING,
  GET_EVENT_FORM_FIELDS_LOADING,
  SET_EVENT_FORM_FIELDS,
  SET_EVENT_TIMELINE_LOADING,
  GET_EVENT_TIMELINES_LOADING,
  GET_EVENT_TIMELINE_DETAILS_LOADING,
  SET_EVENT_TIMELINES,
  SET_EVENT_SPEAKERS,
  SET_EVENT_SPEAKER,
  SET_EVENT_SPEAKER_LOADING,
  GET_EVENT_SPEAKERS_LOADING,
  GET_EVENT_SPONSORS_LOADING,
  SET_EVENT_SPONSORS,
  SET_EVENT_SPONSOR_LOADING,
  DELETE_EVENT_TIMELINE_LOADING,
} from "../constants";
import { generateQueryParams } from "../../../components/utils";
import { triggerNotifier } from "../../../components/utils/notifier";

const baseURL = process.env.REACT_APP_BASE_URL;

export const createEvent = (event) => (dispatch) => {
  dispatch({ type: SET_EVENT_REGISTRATION_LOADING });
  return axios
    .post(`${baseURL}/event/create`, event)
    .then((res) => {
      if (res.status === 200) {
        triggerNotifier({
          message: "Create Event",
          type: "success",
          duration: 1000,
          icon: "👏",
        });
        console.log("eventRegister", res.data);
        dispatch({ type: SET_EVENT_REGISTRATION_LOADING });
        return { success: true, event: res?.data?.event };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: SET_EVENT_REGISTRATION_LOADING });
      console.log("eventRegister Error", error);
      triggerNotifier({
        message: "Create Event",
        type: "error",
        duration: 1000,
        icon: "⚠️",
      });
    });
};

export const deleteEvent = (id) => (dispatch) => {
  dispatch({ type: SET_EVENT_REGISTRATION_LOADING });
  return axios
    .delete(`${baseURL}/event/${id}`)
    .then((res) => {
      if (res.status === 200) {
        triggerNotifier({
          message: "Event details deleted",
          type: "success",
          duration: 1000,
          icon: "👏",
        });

        dispatch({ type: SET_EVENT_REGISTRATION_LOADING });
        dispatch(actions.clearEventsList());
        return { success: true, event: res?.data?.event };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: SET_EVENT_REGISTRATION_LOADING });
      //console.log("Delete Event Error", error);
      triggerNotifier({
        message: "Delete Event Error",
        type: "error",
        duration: 1000,
        icon: "⚠️",
      });
    });
};

export const eventRegister = (register) => (dispatch) => {
  dispatch({ type: SET_EVENT_REGISTRATION_LOADING });
  return axios
    .put(`${baseURL}/event/register_event`, register)
    .then((res) => {
      if (res.status === 200) {
        triggerNotifier({
          message: "Please Register for Event",
          type: "success",
          duration: 1000,
          icon: "👏",
        });
        dispatch({ type: SET_EVENT_REGISTRATION_LOADING });
        return { success: true, data: res?.data?.event };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: SET_EVENT_REGISTRATION_LOADING });
      //console.log("eventRegister Error", error);
      triggerNotifier({
        message: "EventRegister error",
        type: "error",
        duration: 1000,
        icon: "⚠️",
      });
    });
};

export const eventQuestion = (question) => (dispatch) => {
  dispatch({ type: SET_EVENT_REGISTRATION_LOADING });
  return axios
    .put(`${baseURL}/event/add_question`, question)
    .then((res) => {
      if (res.status === 200) {
        triggerNotifier({
          message: "Ask question",
          type: "success",
          duration: 1000,
          icon: "👏",
        });
        dispatch({ type: SET_EVENT_REGISTRATION_LOADING });
        return { success: true, data: res?.data?.event };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: SET_EVENT_REGISTRATION_LOADING });
      console.log("eventRegister Error", error);
      triggerNotifier({
        message: "Something went wrong",
        type: "error",
        duration: 1000,
        icon: "⚠️",
      });
    });
};

export const getEventRegisters = () => (dispatch) => {
  dispatch({ type: SET_EVENT_REGISTRATION_LOADING });
  return axios
    .get(`${baseURL}/user/get_speakers`)
    .then(({ data }) => {
      if (data?.success) {
        triggerNotifier({
          message: "Event register details loaded",
          type: "success",
          duration: 1000,
          icon: "👏",
        });
        return { success: true, data: data.Speakers };
      }
    })
    .catch((error) => {
      dispatch({ type: SET_EVENT_REGISTRATION_LOADING });
      console.log("get speakers error", error);
      triggerNotifier({
        message: "Something went wrong",
        type: "error",
        duration: 1000,
        icon: "⚠️",
      });
    });
};

export const getEventRegistrations = (data) => (dispatch) => {
  dispatch({ type: GET_EVENT_REGISTRATIONS_LOADING });
  return (
    axios
      .get(
        `${baseURL}/event/${data.event}/users/${generateQueryParams({
          event: data.event ?? null,
          status: data.status ?? null,
          profession: data.profession ?? null,
          gender: data.gender ?? null,
          limit: data.limit ? data.limit : 10,
          organization: data.organization ?? null,
          ticketType: data.ticketType ?? null,
        })}`,
      )
      // .get(`${baseURL}/event/${data.event}/users`)
      .then(({ data }) => {
        if (data?.success) {
          triggerNotifier({
            message: "Registration Details loaded",
            type: "success",
            duration: 1000,
            icon: "👏",
          });
          dispatch(actions.setEventRegistrationsList(data));
          return { success: true, data: data?.eventUsers };
        }
      })
      .catch((error) => {
        dispatch({ type: GET_EVENT_REGISTRATIONS_LOADING });
        console.log("get speakers error", error);
        triggerNotifier({
          message: "Something went wrong",
          type: "error",
          duration: 5000,
          icon: "⚠️",
        });
      })
  );
};

//Create Guest User
export const addGuestUser = (eventdata) => (dispatch) => {
  return axios
    .post(`${baseURL}/event/${eventdata.event}/user`, {
      userDetails: eventdata,
    })
    .then((res) => {
      if (res.status === 200) {
        triggerNotifier({
          message: "Added Guest User",
          type: "success",
          duration: 1000,
          icon: "👏",
        });
        return { success: true, data: res?.data?.timeline };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      console.log("Failed to Add Guest User Error", error);
      triggerNotifier({
        message: "Failed to Add Guest User",
        type: "error",
        duration: 5000,
        icon: "⚠️",
      });
    });
};

//edit Event Registration
export const editEventRegistrations = (data) => (dispatch) => {
  dispatch({ type: GET_EVENT_REGISTRATIONS_LOADING });
  return axios
    .patch(`${baseURL}/event/${data?.event}/user/${data?.userId}`, {
      data: data,
    })

    .then(({ data }) => {
      if (data.success) {
        triggerNotifier({
          message: "Registration Details edited",
          type: "success",
          duration: 1000,
          icon: "👏",
        });
        dispatch({ type: GET_EVENT_REGISTRATIONS_LOADING });
      }
    })
    .catch((error) => {
      dispatch({ type: GET_EVENT_REGISTRATIONS_LOADING });
      console.log("user edit error", error);
      triggerNotifier({
        message: "Something went wrong",
        type: "error",
        duration: 5000,
        icon: "⚠️",
      });
    });
};

export const deleteEventRegistration =
  ({ event, userId }) =>
  (dispatch) => {
    // dispatch({ type: GET_EVENT_REGISTRATIONS_LOADING });
    return axios
      .delete(`${baseURL}/event/${event}/user/${userId}`)
      .then(({ data }) => {
        console.log(data);

        if (data.success) {
          triggerNotifier({
            message: "User Registration Details Deleted",
            type: "success",
            duration: 1000,
            icon: "👏",
          });
          // dispatch(getEventRegistrations({ event: event }));
          return { success: true, data: data?.eventUsers };
        }
      })
      .catch((error) => {
        dispatch({ type: GET_EVENT_REGISTRATIONS_LOADING });
        console.log("get speakers error", error);
        triggerNotifier({
          message: "Something went wrong",
          type: "error",
          duration: 5000,
          icon: "⚠️",
        });
      });
  };

export const getEventDetails = (id) => (dispatch) => {
  dispatch({ type: GET_EVENT_DETAILS_LOADING });
  return axios
    .get(`${baseURL}/event/${id}`)
    .then(({ data }) => {
      if (data.success) {
        triggerNotifier({
          message: "Event details loaded",
          type: "success",
          duration: 1000,
          icon: "👏",
        });
        /*  toast.success("Event details loaded", {
          duration: 1000,
          icon: "👏",
        }); */
        dispatch(actions.setEventDetails(data?.event));
        dispatch(actions.setEventCountDetails(data?.count));
        return { success: true, data: data?.event };
      }
    })
    .catch((error) => {
      dispatch({ type: GET_EVENT_DETAILS_LOADING });
      console.log("get event error", error);
      triggerNotifier({
        message: "Something went wrong",
        type: "error",
        duration: 1000,
        icon: "⚠️",
      });
    });
};

export const getOrganiserEvents = (data) => (dispatch) => {
  dispatch({ type: GET_EVENTS_LIST_LOADING, payload: data?.state });
  return axios
    .get(
      `${baseURL}/event/events${generateQueryParams({
        page: data?.page,
        limit: data?.limit,
        filterTags: data?.filterTags || data?.tags,
        organizerUserName: data?.organizerUserName,
        eventRegUserEmail: data?.eventRegUserEmail,
        currentUserEmail: data?.email,
        state: data?.state,
        order: data?.order,
      })}`,
    )
    .then((res) => {
      if (
        res.status === 200 &&
        res?.data?.events &&
        data.state === "upcoming"
      ) {
        triggerNotifier({
          message: "Upcoming Events",
          type: "success",
          duration: 1000,
          icon: "👏",
        });
        dispatch(actions.setEventsUpcoming(res?.data?.events));
        return { success: true, data: res?.data?.events };
      } else if (
        res.status === 200 &&
        res?.data?.events &&
        data.state === "live"
      ) {
        triggerNotifier({
          message: "Live Events",
          type: "success",
          duration: 1000,
          icon: "👏",
        });
        dispatch(actions.setEventsLive(res?.data?.events));
        return { success: true, data: res?.data?.events };
      } else if (
        res.status === 200 &&
        res?.data?.events &&
        data?.state === "past"
      ) {
        triggerNotifier({
          message: "Past Events",
          type: "success",
          duration: 1000,
          icon: "👏",
        });
        dispatch(actions.setEventsPast(res?.data?.events));
        return { success: true, data: res?.data?.events };
      } else if (
        res.status === 200 &&
        res?.data?.events &&
        data?.state === "all"
      ) {
        triggerNotifier({
          message: "All Events List",
          type: "success",
          duration: 1000,
          icon: "👏",
        });
        dispatch(actions.setEventsList(res?.data?.events));
        return { success: true, data: res?.data?.events };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: GET_EVENTS_LIST_LOADING, payload: data.state });
      // console.log("organiser events List Error", error);
      triggerNotifier({
        message: "organiser events List Error",
        type: "error",
        duration: 1000,
        icon: "⚠️",
      });
    });
};

export const getSearchEvents = (data) => (dispatch) => {
  dispatch({ type: SET_SEARCH_EVENTS_LIST_LOADING });
  return axios
    .get(
      `${baseURL}/event/events${generateQueryParams({
        page: data?.page,
        limit: data?.limit,
        filterTags: data?.filterTags || data?.tags,
        organizerUserName: data?.organizerUserName,
        eventRegUserEmail: data?.eventRegUserEmail,
        currentUserEmail: data?.email,
        state: data?.state,
        order: data?.order,
      })}`,
    )
    .then((res) => {
      if (res.status === 200 && res?.data?.events) {
        triggerNotifier({
          message: "Searched Events list loaded",
          type: "success",
          duration: 1000,
          icon: "👏",
        });
        dispatch({ type: SET_SEARCH_EVENTS_LIST, payload: res?.data?.events });
        return { success: true, data: res?.data?.events };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: SET_SEARCH_EVENTS_LIST_LOADING });
      console.log("Searched events List Error", error);
      triggerNotifier({
        message: "Searched events List Error",
        type: "error",
        duration: 1000,
        icon: "⚠️",
      });
    });
};

export const eventFeedback = (feedback) => (dispatch) => {
  dispatch({ type: SET_EVENT_REGISTRATION_LOADING });
  return axios
    .put(`${baseURL}/event/feedback_event`, feedback)
    .then((res) => {
      if (res.status === 200) {
        triggerNotifier({
          message: "Event FeedBack",
          type: "success",
          duration: 1000,
          icon: "👏",
        });
        dispatch({ type: SET_EVENT_REGISTRATION_LOADING });
        return { success: true, data: res?.data?.event };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: SET_EVENT_REGISTRATION_LOADING });
      console.log("event Feedback Error", error);
      triggerNotifier({
        message: "Event Feedback Errorr",
        type: "error",
        duration: 1000,
        icon: "⚠️",
      });
    });
};

// export const editEventSpeaker = (sponsor) => (dispatch) => {
//   dispatch({ type: SET_EVENT_REGISTRATION_LOADING });
//   return axios
//     .put(`${baseURL}/event/edit_event_speaker`, sponsor)
//     .then((res) => {
//       if (res.status === 200) {
//         triggerNotifier({
//           message: "Edit Event Speaker details",
//           type: "success",
//           duration: 1000,
//           icon: "👏",
//         });
//         dispatch({ type: SET_EVENT_REGISTRATION_LOADING });
//         return { success: true, data: res?.data?.event };
//       } else {
//         return { success: false };
//       }
//     })
//     .catch((error) => {
//       dispatch({ type: SET_EVENT_REGISTRATION_LOADING });
//       console.log("edit event Speaker Error", error);
//       triggerNotifier({
//         message: "Something went Wrong",
//         type: "error",
//         duration: 1000,
//         icon: "⚠️",
//       });
//     });
// };

export const getEventsList = () => (dispatch) => {
  dispatch({ type: GET_EVENTS_LIST_LOADING, payload: "all" });
  return axios
    .get(`${baseURL}/event`)
    .then((res) => {
      if (res.status === 200) {
        triggerNotifier({
          message: "Event List loaded",
          type: "success",
          duration: 1000,
          icon: "👏",
        });
        dispatch(actions.setEventsList(res?.data && res.data?.events));
        dispatch({ type: GET_EVENTS_LIST_LOADING, payload: "all" });
        return { success: true, data: res?.data && res?.data.events };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: GET_EVENTS_LIST_LOADING, payload: "all" });
      console.log("event List Error", error);
      triggerNotifier({
        message: "Something went wrong",
        type: "error",
        duration: 1000,
        icon: "⚠️",
      });
    });
};

export const editEvent = (event) => (dispatch) => {
  dispatch({ type: SET_EDIT_EVENT_LOADING });
  let eventId = event._id || event.event;
  return axios
    .patch(`${baseURL}/event/${eventId}`, { data: event })
    .then((res) => {
      if (res.status === 200) {
        triggerNotifier({
          message: "Edit Event ",
          type: "success",
          duration: 1000,
          icon: "👏",
        });
        dispatch({ type: SET_EDIT_EVENT_LOADING });
        return { success: true, data: res?.data?.event };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: SET_EDIT_EVENT_LOADING });
      console.log("event Edit Error", error);
      triggerNotifier({
        message: "Something went wrong",
        type: "error",
        duration: 1000,
        icon: "⚠️",
      });
    });
};

export const checkUserEventReg = (data) => (dispatch) => {
  dispatch({ type: SET_EVENT_REGISTRATION_LOADING });
  return axios
    .put(`${baseURL}/event/check_event_reg`, data)
    .then((res) => {
      if (res.status === 200) {
        triggerNotifier({
          message: "Check User Event Registration ",
          type: "success",
          duration: 1000,
          icon: "👏",
        });
        dispatch({ type: SET_EVENT_REGISTRATION_LOADING });
        return { success: true, data: res?.data?.registeredUser };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: SET_EVENT_REGISTRATION_LOADING });
      console.log("check user event reg Error", error);
      triggerNotifier({
        message: "Something went wrong",
        type: "error",
        duration: 1000,
        icon: "⚠️",
      });
    });
};

export const addEventAttendees = (data) => (dispatch) => {
  dispatch({ type: SET_EVENT_REGISTRATION_LOADING });
  return axios
    .put(`${baseURL}/event/attendees`, data)
    .then((res) => {
      if (res.status === 200) {
        triggerNotifier({
          message: "Add Event Attendees",
          type: "success",
          duration: 1000,
          icon: "👏",
        });
        dispatch({ type: SET_EVENT_REGISTRATION_LOADING });
        return { success: true, data: res?.data?.event };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: SET_EVENT_REGISTRATION_LOADING });
      console.log("check user event reg Error", error);
      triggerNotifier({
        message: "Failed to add event attendees",
        type: "error",
        duration: 1000,
        icon: "⚠️",
      });
    });
};

// -------------Event Form Fields ----------------------------

export const addEventFormField = (field) => (dispatch) => {
  dispatch({ type: SET_EVENT_FORM_FIELD_LOADING });
  return axios
    .patch(`${baseURL}/event/${field.eventId}/form_field`, { data: field })
    .then((res) => {
      if (res.status === 200) {
        triggerNotifier({
          message: "Event form field added",
          type: "success",
          duration: 1000,
          icon: "👏",
        });
        dispatch({ type: SET_EVENT_FORM_FIELD_LOADING });
        dispatch(
          getEventFormFields({
            eventId: field.eventId,
            formName: field.formName,
          }),
        );
        return { success: true, data: res?.data?.event };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: SET_EVENT_FORM_FIELD_LOADING });
      console.log("Add Event Form Fields Error", error);
      triggerNotifier({
        message: "Add Event Form Fields Error",
        type: "error",
        duration: 1000,
        icon: "⚠️",
      });
    });
};

export const editEventFormField = (field) => (dispatch) => {
  dispatch({ type: SET_EVENT_FORM_FIELD_LOADING });
  return axios
    .patch(`${baseURL}/event/${field.eventId}/form_field/${field.id}`, {
      data: field,
    })
    .then((res) => {
      if (res.status === 200) {
        triggerNotifier({
          message: "Edit Event form field",
          type: "success",
          duration: 1000,
          icon: "👏",
        });
        dispatch({ type: SET_EVENT_FORM_FIELD_LOADING });
        dispatch(
          getEventFormFields({
            eventId: field?.eventId,
            formName: field?.formName,
          }),
        );
        return { success: true, data: res?.data?.event };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: SET_EVENT_FORM_FIELD_LOADING });
      console.log("Edit Event Form Fields Error", error);
      triggerNotifier({
        message: "Edit Event Form Fields Error",
        type: "error",
        duration: 1000,
        icon: "⚠️",
      });
    });
};

export const orderEventFormField =
  (fields, eventId, formName) => (dispatch) => {
    dispatch({ type: SET_EVENT_FORM_FIELD_LOADING });
    return axios
      .patch(`${baseURL}/event/${eventId}/form_fields/order`, {
        data: fields,
        formName,
      })
      .then((res) => {
        if (res.status === 200) {
          triggerNotifier({
            message: "Event form field",
            type: "success",
            duration: 1000,
            icon: "👏",
          });
          dispatch({ type: SET_EVENT_FORM_FIELD_LOADING });
          dispatch(getEventFormFields({ eventId: eventId, formName }));
          return { success: true, data: res?.data?.event };
        } else {
          return { success: false };
        }
      })
      .catch((error) => {
        dispatch({ type: SET_EVENT_FORM_FIELD_LOADING });
        console.log("Edit Event Form Fields Error", error);
        triggerNotifier({
          message: "Something went wrong",
          type: "error",
          duration: 1000,
          icon: "⚠️",
        });
      });
  };

export const getEventFormFields = (data) => (dispatch) => {
  dispatch({ type: GET_EVENT_FORM_FIELDS_LOADING });
  return axios
    .get(`${baseURL}/event/${data?.eventId}/form_fields/${data?.formName}`)
    .then((res) => {
      if (res.status === 200) {
        triggerNotifier({
          message: "Form fields",
          type: "success",
          duration: 1000,
          icon: "👏",
        });
        dispatch({
          type: SET_EVENT_FORM_FIELDS,
          payload: res.data.form_fields,
        });
        return { success: true, data: res?.data?.event };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: GET_EVENT_FORM_FIELDS_LOADING });
      console.log("Get Event Form Fields Error", error);
      triggerNotifier({
        message: "Something went wrong",
        type: "error",
        duration: 1000,
        icon: "⚠️",
      });
    });
};

export const deleteEventFormFields = (data) => (dispatch) => {
  dispatch({ type: GET_EVENT_FORM_FIELDS_LOADING });
  return axios
    .delete(`${baseURL}/event/${data.eventId}/form_field/${data.id}`)
    .then((res) => {
      if (res.status === 200) {
        triggerNotifier({
          message: "Delete Form fields",
          type: "success",
          duration: 1000,
          icon: "👏",
        });
        dispatch({ type: GET_EVENT_FORM_FIELDS_LOADING });
        return { success: true, data: res?.data?.event };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: GET_EVENT_FORM_FIELDS_LOADING });
      console.log("Delete Event Form Fields Error", error);
      triggerNotifier({
        message: "Something went wrong",
        type: "error",
        duration: 1000,
        icon: "⚠️",
      });
    });
};

// --------Event Timeline ----------

export const addEventTimeline = (timeline) => (dispatch) => {
  dispatch({ type: SET_EVENT_TIMELINE_LOADING });
  return axios
    .post(`${baseURL}/event/${timeline?.eventId}/timeline`, timeline)
    .then((res) => {
      if (res.status === 200) {
        triggerNotifier({
          message: "Add Event Timeline",
          type: "success",
          duration: 1000,
          icon: "👏",
        });

        dispatch({ type: SET_EVENT_TIMELINE_LOADING });
        dispatch(getEventTimelines({ eventId: timeline.eventId }));
        return { success: true, data: res?.data?.timeline };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: SET_EVENT_TIMELINE_LOADING });
      //console.log("Add Event Timeline Error", error);
      triggerNotifier({
        message: "Failed to Add Event Timeline",
        type: "error",
        duration: 1000,
        icon: "⚠️",
      });
    });
};

export const getEventTimelines = (data) => (dispatch) => {
  dispatch({ type: GET_EVENT_TIMELINES_LOADING });
  return axios
    .get(`${baseURL}/event/${data?.eventId}/timelines`)
    .then((res) => {
      if (res.status === 200) {
        triggerNotifier({
          message: "Event Timeline Loaded",
          type: "success",
          duration: 1000,
          icon: "👏",
        });
        dispatch({ type: GET_EVENT_TIMELINES_LOADING });
        dispatch({ type: SET_EVENT_TIMELINES, payload: res.data });
        return { success: true, data: res?.data };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: GET_EVENT_TIMELINES_LOADING });

      triggerNotifier({
        message: "Failed to get Event timeline",
        type: "error",
        duration: 1000,
        icon: "⚠️",
      });
    });
};

export const editEventTimeLines = (data) => (dispatch) => {
  dispatch({ type: GET_EVENT_TIMELINES_LOADING });
  return axios
    .patch(`${baseURL}/event/${data?.eventId}/timelines}`, { data: data })
    .then((res) => {
      if (res.status === 200) {
        triggerNotifier({
          message: "Event Timeline order edited",
          type: "success",
          duration: 1000,
          icon: "👏",
        });
        dispatch({ type: GET_EVENT_TIMELINES_LOADING });
        dispatch(getEventTimelines(data?.eventId));
        return { success: true, data: res?.data };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: GET_EVENT_TIMELINES_LOADING });
      console.log("Edit Event Timeline Error", error);
      triggerNotifier({
        message: "Failed to edit Event timeline order",
        type: "error",
        duration: 1000,
        icon: "⚠️",
      });
    });
};
export const getEventTimelineDetails = (data) => (dispatch) => {
  dispatch({ type: GET_EVENT_TIMELINE_DETAILS_LOADING });
  return axios
    .get(`${baseURL}/event/${data.eventId}/timeline/${data?.timelineId}`)
    .then((res) => {
      if (res.status === 200) {
        triggerNotifier({
          message: "TimeLine details loaded",
          type: "success",
          duration: 1000,
          icon: "👏",
        });
        dispatch({ type: GET_EVENT_TIMELINE_DETAILS_LOADING });
        dispatch({ type: SET_EVENT_TIMELINES, payload: res.data });
        return { success: true, data: res?.data };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: GET_EVENT_TIMELINE_DETAILS_LOADING });
      console.log("Get Event Timeline Details Error", error);
      triggerNotifier({
        message: "Failed to get Event timeline",
        type: "error",
        duration: 1000,
        icon: "⚠️",
      });
    });
};

export const deleteEventTimeline = (data) => (dispatch) => {
  dispatch({ type: DELETE_EVENT_TIMELINE_LOADING });
  return axios
    .delete(`${baseURL}/event/${data.eventId}/timeline/${data && data.timelineId}`)
    .then((res) => {
      if (res.status === 200) {
        toast.error("Event Timeline Deleted");
        dispatch({ type: DELETE_EVENT_TIMELINE_LOADING });
        dispatch(getEventTimelines(data));
        return { success: true, data: res?.data };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: DELETE_EVENT_TIMELINE_LOADING });
      console.log("Delete Event Timeline Error", error);
      triggerNotifier({
        message: "Delete Event Timeline Error",
        type: "error",
        duration: 1000,
        icon: "⚠️",
      });
    });
};

// -----Event Speaker ------

export const addEventSpeaker = (speaker) => (dispatch) => {
  dispatch({ type: SET_EVENT_SPEAKER_LOADING });
  return axios
    .post(`${baseURL}/event/${speaker.eventId}/speaker/add`, speaker)
    .then((res) => {
      if (res.status === 200) {
        triggerNotifier({
          message: "Event speaker details added",
          type: "success",
          duration: 1000,
          icon: "👏",
        });
        dispatch({ type: SET_EVENT_SPEAKER_LOADING });
        // dispatch(getEventSpeakers(speaker.eventId));
        dispatch(getEventDetails(speaker?.eventId));
        return { success: true, data: res?.data?.event };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: SET_EVENT_SPEAKER_LOADING });
      console.log("Event Speaker Error", error);
      triggerNotifier({
        message: "Event Speaker Error",
        type: "error",
        duration: 1000,
        icon: "⚠️",
      });
    });
};

export const getEventSpeaker =
  ({ id, event }) =>
  (dispatch) => {
    dispatch({ type: GET_EVENT_SPEAKERS_LOADING });
    return axios
      .get(`${baseURL}/event/${event}/speaker/${id}`)
      .then((res) => {
        if (res.status === 200) {
          triggerNotifier({
            message: "Speaker details loaded",
            type: "success",
            duration: 1000,
            icon: "👏",
          });
          dispatch({ type: GET_EVENT_SPEAKERS_LOADING });
          dispatch({ type: SET_EVENT_SPEAKER, payload: res?.data });
          return { success: true, data: res?.data };
        } else {
          return { success: false };
        }
      })
      .catch((error) => {
        dispatch({ type: GET_EVENT_SPEAKERS_LOADING });
        console.log("Get Event Speakers Error", error);
        triggerNotifier({
          message: "Event Speaker Error",
          type: "error",
          duration: 1000,
          icon: "⚠️",
        });
      });
  };

export const deleteEventSpeaker = (speaker) => (dispatch) => {
  dispatch({ type: SET_EVENT_SPEAKER_LOADING });
  return axios
    .delete(`${baseURL}/event/${speaker?.eventId}/speaker/${speaker?.id}`)
    .then((res) => {
      if (res.status === 200) {
        triggerNotifier({
          message: "Event speaker details Deleted",
          type: "success",
          duration: 1000,
          icon: "👏",
        });
        dispatch({ type: SET_EVENT_SPEAKER_LOADING });
        // dispatch(getEventSpeakers(speaker.eventId));
        dispatch(getEventDetails(speaker?.eventId));
        return { success: true, data: res?.data?.event };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: SET_EVENT_SPEAKER_LOADING });
      console.log("Event Speaker Error", error);
      triggerNotifier({
        message: "Event Speaker Error",
        type: "error",
        duration: 1000,
        icon: "⚠️",
      });
    });
};

export const editEventSpeaker = (speaker) => (dispatch) => {
  dispatch({ type: SET_EVENT_SPEAKER_LOADING });
  return axios
    .patch(`${baseURL}/event/${speaker?.eventId}/speaker/${speaker?.id}`, {
      data: speaker,
    })
    .then((res) => {
      if (res.status === 200) {
        triggerNotifier({
          message: "Event speaker details added",
          type: "success",
          duration: 1000,
          icon: "👏",
        });
        dispatch({ type: SET_EVENT_SPEAKER_LOADING });
        dispatch(getEventSpeakers(speaker?.eventId));
        return { success: true, data: res?.data?.event };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: SET_EVENT_SPEAKER_LOADING });
      console.log("Event Speaker Error", error);
      triggerNotifier({
        message: "Event Speaker Error",
        type: "error",
        duration: 1000,
        icon: "⚠️",
      });
    });
};

export const getEventSpeakers = (id) => (dispatch) => {
  dispatch({ type: GET_EVENT_SPEAKERS_LOADING });
  return axios
    .get(`${baseURL}/event/${id}/speakers`)
    .then((res) => {
      if (res.status === 200) {
        triggerNotifier({
          message: "Speaker details loaded",
          type: "success",
          duration: 1000,
          icon: "👏",
        });
        dispatch({ type: GET_EVENT_SPEAKERS_LOADING });
        dispatch({ type: SET_EVENT_SPEAKERS, payload: res?.data });
        return { success: true, data: res?.data };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: GET_EVENT_SPEAKERS_LOADING });
      console.log("Get Event Speakers Error", error);
      triggerNotifier({
        message: "Event Speaker Error",
        type: "error",
        duration: 1000,
        icon: "⚠️",
      });
    });
};

// -----Event Sponsor ------

export const addEventSponsor = (sponsor) => (dispatch) => {
  dispatch({ type: SET_EVENT_SPONSOR_LOADING });
  return axios
    .post(`${baseURL}/event/${sponsor.eventId}/sponsor`, sponsor)
    .then((res) => {
      if (res.status === 200) {
        triggerNotifier({
          message: "Event Sponser Added Successfully",
          type: "success",
          duration: 1000,
          icon: "👏",
        });
        dispatch({ type: SET_EVENT_SPONSOR_LOADING });
        dispatch(getEventSponsors(sponsor.eventId));
        return { success: true, data: res?.data?.event };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: SET_EVENT_SPONSOR_LOADING });
      console.log("Add Event Sponsor Error", error);
      triggerNotifier({
        message: "Event Sponser Error",
        type: "error",
        duration: 1000,
        icon: "⚠️",
      });
    });
};

export const deleteEventSponsor = (sponsor) => (dispatch) => {
  dispatch({ type: SET_EVENT_SPEAKER_LOADING });
  return axios
    .delete(`${baseURL}/event/${sponsor.eventId}/sponsor/${sponsor.id}`)
    .then((res) => {
      if (res.status === 200) {
        triggerNotifier({
          message: "Event sponser details deleted ",
          type: "success",
          duration: 1000,
          icon: "👏",
        });
        dispatch({ type: SET_EVENT_SPEAKER_LOADING });
        dispatch(getEventSponsors(sponsor.eventId));
        return { success: true, data: res?.data?.event };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: SET_EVENT_SPEAKER_LOADING });
      console.log("Event Speaker Error", error);
      triggerNotifier({
        message: "Event Sponser Error",
        type: "error",
        duration: 1000,
        icon: "⚠️",
      });
    });
};

export const getEventSponsors = (id) => (dispatch) => {
  dispatch({ type: GET_EVENT_SPONSORS_LOADING });
  return axios
    .get(`${baseURL}/event/${id}/sponsors`)
    .then((res) => {
      if (res.status === 200) {
        triggerNotifier({
          message: "Event sponsers ",
          type: "success",
          duration: 1000,
          icon: "👏",
        });
        dispatch({ type: GET_EVENT_SPONSORS_LOADING });
        dispatch({ type: SET_EVENT_SPONSORS, payload: res.data });
        return { success: true, data: res.data };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: GET_EVENT_SPONSORS_LOADING });
      console.log("Get Event Sponsors Error", error);
      triggerNotifier({
        message: "Failed to load sponser details",
        type: "error",
        duration: 1000,
        icon: "⚠️",
      });
    });
};

// create Event Ticket

export const createEventTicket = (data) => (dispatch) => {
  dispatch({ type: SET_EVENT_REGISTRATION_LOADING });
  return axios
    .post(`${baseURL}/event/${data.eventId}/ticket`, data)
    .then((res) => {
      if (res.status === 200) {
        triggerNotifier({
          message: "Created Event Ticket",
          type: "success",
          duration: 1000,
          icon: "👏",
        });
        dispatch({ type: SET_EVENT_REGISTRATION_LOADING });
        dispatch(getEventTicket(data.eventId));
        return { success: true, event: res?.data?.tickets };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: SET_EVENT_REGISTRATION_LOADING });
      console.log("Event Ticket Error", error);
      triggerNotifier({
        message: "Create Event Ticket",
        type: "error",
        duration: 1000,
        icon: "⚠️",
      });
    });
};

//get Event Ticket
export const getEventTicket = (event) => (dispatch) => {
  dispatch({ type: SET_EVENT_REGISTRATION_LOADING });
  return axios
    .get(`${baseURL}/event/${event}/tickets`)
    .then(({ data }) => {
      if (data.success) {
        triggerNotifier({
          message: "Event register details loaded",
          type: "success",
          duration: 1000,
          icon: "👏",
        });
        dispatch({ type: SET_EVENT_REGISTRATION_LOADING });
        dispatch(actions.setEventTicket(data));
        return { success: true, data: data };
      }
    })
    .catch((error) => {
      dispatch({ type: SET_EVENT_REGISTRATION_LOADING });
      triggerNotifier({
        message: "Something went wrong",
        type: "error",
        duration: 1000,
        icon: "⚠️",
      });
    });
};

//delete Event Ticket
export const deleteEventTicket =
  ({ event, ticketId }) =>
  (dispatch) => {
    dispatch({ type: GET_EVENT_REGISTRATIONS_LOADING });
    return axios
      .delete(`${baseURL}/event/${event}/ticket/${ticketId}`)
      .then(({ data }) => {
        if (data?.success) {
          triggerNotifier({
            message: "Ticket Deleted Succesfully",
            type: "success",
            duration: 1000,
            icon: "👏",
          });
          dispatch(getEventTicket(event));
          return { success: true, data: data?.eventUsers };
        }
      })
      .catch((error) => {
        dispatch({ type: GET_EVENT_REGISTRATIONS_LOADING });
        console.log("Ticket Delete error", error);
        triggerNotifier({
          message: "Something went wrong",
          type: "error",
          duration: 5000,
          icon: "⚠️",
        });
      });
  };

//edit Event Ticket
export const editEventTicket = (eventdata) => (dispatch) => {
  dispatch({ type: SET_EDIT_EVENT_LOADING });
  return axios
    .patch(`${baseURL}/event/${eventdata?.event}/ticket/${eventdata?.ticketId}`, {
      data: eventdata,
    })
    .then((res) => {
      if (res.status === 200) {
        triggerNotifier({
          message: "Edit Event ",
          type: "success",
          duration: 1000,
          icon: "👏",
        });
        dispatch({ type: SET_EDIT_EVENT_LOADING });
        dispatch(getEventTicket(eventdata?.event));
        return { success: true, data: res?.data?.tickets };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: SET_EDIT_EVENT_LOADING });
      console.log("Event Ticket Edit Error", error);
      triggerNotifier({
        message: "Something went wrong",
        type: "error",
        duration: 1000,
        icon: "⚠️",
      });
    });
};

//Get Event Content Management System (CMS)
export const getEventCMS = (event) => (dispatch) => {
  dispatch({ type: SET_EVENT_REGISTRATION_LOADING });
  return axios
    .get(
      `${baseURL}/content${generateQueryParams({
        slug: "projectOverview",
        event,
      })}`,
    )
    .then(({ data }) => {
      if (data?.success) {
        triggerNotifier({
          message: "Event Content details loaded",
          type: "success",
          duration: 1000,
          icon: "👏",
        });
        dispatch({ type: SET_EVENT_REGISTRATION_LOADING });
        return { success: true, data: data };
      }
    })
    .catch((error) => {
      dispatch({ type: SET_EVENT_REGISTRATION_LOADING });
      triggerNotifier({
        message: "Something went wrong",
        type: "error",
        duration: 1000,
        icon: "⚠️",
      });
    });
};

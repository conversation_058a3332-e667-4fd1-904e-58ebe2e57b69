import React, { useEffect, useRef, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { getColumns } from './helpers'
import { useNavigate } from 'react-router'

import { setEditReview } from '../../actions/actionCreators'
import { deleteReview, getReviewsList, getUserProfile, getUsersListDetails } from '../../actions/operations'
import CustomLoader from '../../../../components/sharedComponents/CustomLoader'
import NoDataBlock from '../../../../components/sharedComponents/NoDataBlock'
import UserCard from './UserCard'
import CreateReviewForm from './Review/CreateReviewForm'
import ReviewCard from './Review/ReviewCard'

const UsersList = () => {

  const dispatch = useDispatch()
  
  const { userProfileLoading, usersData, userProfile, reviewsList, usersListLoading } = useSelector((state) => state.auth)
  
  const [search, setSearch] = useState('')
  const [open, setOpen] = useState(false)
  const [tempId, setTempId] = useState(null);
  const [tempUser, setTempUser] = useState(null)
  const [isEdit, setIsEdit] = useState(false)
  const [activeUser, setActiveUser] = useState(null);
  const selectedReview = useRef()

  useEffect(() => {
    dispatch(getUsersListDetails())
  }, [])

  useEffect(() => {
    console.log(tempId, 'id')
  }, [tempId])

  const OnUserCardClick = (user) => {
    setActiveUser(user._id);
    dispatch(getUserProfile(user._id));
    dispatch(getReviewsList({ user: user._id }))
    setTempId(user._id)
    setTempUser(user)
    setOpen(false);
    setIsEdit(false);
  }
  const handleEdit = (review) => {
    dispatch(setEditReview(review))
  }
  const handledelete = (id) => {
    dispatch(deleteReview(id))
  }
  const items = [
    {
      key: '1',
      label: (
        <div onClick={(e) => handleEdit(selectedReview.current)}> Edit </div>
      ),
    },
    {
      key: '2',
      label: (
        <div onClick={(e) => handledelete()}>Delete</div>
      ),
    },
  ];
  const filterUsersData = usersData && usersData.filter(item => item?.email?.toUpperCase().includes(search.toUpperCase()) || item?.firstName?.toUpperCase().includes(search.toUpperCase()) || item?.userName?.toUpperCase().includes(search.toUpperCase()))
  let columns = getColumns(OnUserCardClick)
  return (

    <>
      <div className='event-attendees '>
        <div className='row mx-0'>
          <div className='table table-responsive col-4 m-0 px-0 border-right'>
            <div className='row mx-0 solution-nav'>
              <div className='col-md-8 col-6 mx-0 d-flex p-2 align-items-center'>
                <h4 className='py-md-3 py-2 mb-0'>
                  Portal Users List
                </h4>
              </div>
            </div>
            <div
              className='w-100 border-top border-left border-right rounded d-flex justify-content-between align-items-center'
              style={{ backgroundColor: "#f0eeee" }}
            >
              <div className='table-options-search ml-4'>
                <input
                  type="text"
                  className="search"
                  value={search}
                  placeholder="Username"
                  onChange={(e) => setSearch(e.target.value)}
                />
              </div>
            </div>

            {usersListLoading ? (
              <CustomLoader />
            ) : (
              <>
                {filterUsersData.map((user) => (
                  <div
                    key={user._id}
                    onClick={() => OnUserCardClick(user)}
                    className={`px-3 ${activeUser === user._id ? 'active' : ''}`}
                  >
                    <UserCard user={user} />
                  </div>
                ))}
              </>
            )}
          </div>
          <div className='col-8 m-0 px-0'>
            <div className='row mx-0 solution-nav'>
              <div className='col-md-8 col-6 mx-0 d-flex p-2 align-items-center'>
                <h4 className='py-md-3 py-2 mb-0'>
                  {tempUser?.firstName}`s User Review
                </h4>
              </div>
              {tempUser && <div className='col-md-4 col-6 d-flex justify-content-end align-items-center'>
                <button
                  onClick={() => {
                    setOpen(true)
                    setIsEdit(false)
                  }
                  }
                  type='button'
                  className='btn enroll-small-btn'
                >
                  <small>Add Review</small>
                </button>
              </div>}
            </div>
            {open ?
              <>
                <p onClick={() => setOpen(false)}>Back</p>
                <CreateReviewForm id={tempId} isEdit={isEdit} setIsEdit={setIsEdit} />
              </>
              :
              <>
                {(!tempUser && !open) ?
                  <>
                    <div className='d-flex justify-content-center mt-4'>Select User to display reviews</div>
                    <NoDataBlock />
                  </>
                  : (
                    <>
                      {
                        userProfileLoading ? (
                          <CustomLoader />
                        ) :
                          <div className='row m-0'>
                            <div className="col-12">

                              {
                                reviewsList.length ?
                                  <div className="row mx-0">
                                    {
                                      reviewsList?.map((review) => (
                                        <div className='col-6 m-0 p-3'>
                                          <ReviewCard review={review} items={items} selectedReview={selectedReview} />
                                        </div>
                                      ))
                                    }
                                  </div>
                                  :
                                  <>
                                    <div className="row mx-0">
                                      <div className="col-12 text-center">
                                        <div className="">
                                          <NoDataBlock />
                                          <button
                                            onClick={() => {
                                              setOpen(true)
                                              setIsEdit(false)
                                            }}
                                            type='button'
                                            className='btn btn-primary'
                                          >
                                            <small>Add Review</small>
                                          </button>
                                        </div>
                                      </div>
                                    </div>
                                  </>
                              }
                            </div>
                          </div>
                      }
                    </>
                  )}
              </>
            }
          </div>
        </div>
      </div>
    </>
  )
}

export default UsersList
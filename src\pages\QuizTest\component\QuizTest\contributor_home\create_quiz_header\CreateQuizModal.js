import React from "react";

import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ooter } from "reactstrap";
import PropTypes from "prop-types";

import CreateQuiz from "./createQuiz";

const CreateQuizModal = ({
  open,
  toggle,
  onSubmit,
  closeModal,
  submitButtonName,
  submitButtonColor,
  title,
  message,
  children,
}) => {
  const handleFormSubmit = (formData) => {
    console.log("Form Data:", formData);
    toggle();
    onSubmit(formData);
  };

  return (
    <Modal isOpen={open} className='custom-modal'>
      <ModalHeader className='custom-modal-header d-flex justify-content-between align-items-center'>
        <span>{title || "Create Quiz"}</span>
        <button
          type='button'
          className='close custom-close-button'
          onClick={toggle}
          aria-label='Close'
        >
          <span aria-hidden='true'>&times;</span>
        </button>
      </ModalHeader>

      <ModalBody className='custom-modal-body'>
        {!children ? (
          <p className='modal-message'>
            🚀 Hey! You are about to create a new Quiz Test! Please read all
            instructions carefully.
          </p>
        ) : null}
        {children ? (
          React.cloneElement(children, {
            isEditable: true,
            onSubmit: handleFormSubmit,
          })
        ) : (
          <CreateQuiz toggle={toggle} closeModal={closeModal} />
        )}
      </ModalBody>
      {/* {type !== "OtherDetails" && (
        <ModalFooter className='custom-modal-footer'>
          {!children && (
            <Button
              color={submitButtonColor}
              onClick={onSubmit}
              className='submit-btn'
            >
              {submitButtonName}
            </Button>
          )}
          <Button color='secondary' onClick={toggle} className='cancel-btn'>
            Cancel
          </Button>
        </ModalFooter>
      )} */}
    </Modal>
  );
};

CreateQuizModal.defaultProps = {
  message: "",
  title: "",
  submitButtonName: "Okay",
  onSubmit: () => {},
  submitButtonColor: "primary",
};

CreateQuizModal.propTypes = {
  message: PropTypes.string,
  title: PropTypes.string,
  submitButtonName: PropTypes.string,
  submitButtonColor: PropTypes.string,
  onSubmit: PropTypes.func,
  toggle: PropTypes.func.isRequired,
  open: PropTypes.bool.isRequired,
  children: PropTypes.node,
};

export default CreateQuizModal;

import React, { useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { Field, reduxForm } from 'redux-form'
import { Link, useNavigate } from 'react-router-dom'

import { renderInputField, renderSelectField } from '../../../../components/sharedComponents/ReduxFormFields'
import { required } from '../../../../components/utils/validators'
import BannerImageUpload from '../../../../components/sharedComponents/BannerImageUpload';
import { getOptions } from '../../../../components/utils';
import { categoryOptions } from '../../../../components/utils/selectOptions';
import CustomLoader from '../../../../components/sharedComponents/CustomLoader'
import { createLesson } from '../../../Courses/actions'

const CreateLessonBlog = ({
  reset,
  handleSubmit,
  submitting,
  type
}) => {
  const dispatch = useDispatch();
  const { createCourseLoading } = useSelector((state) => state.course) || {};
  const navigate = useNavigate();

  const [uplodedImg, setUploadedImg] = useState('')

  const onSubmit = (values) => {
    const course = { ...values }
    course['thumbnail'] = uplodedImg;
    dispatch(createLesson(course)).then((res) => {
      if (res && res.success) {
        navigate(`/blog/${res.lesson._id}/manage`)
      }
    })
    reset('create-lesson')
  }

  return (
    <>
      <div className="row mx-0">
        <div className="col-md-12 col-6 mx-0 px-md-5 px-2 d-flex align-items-center justify-content-between">
          <h4 className="mb-0 py-md-3 py-2">{'Create Course'}</h4>
          <h4><Link to="/course"><i className="fal fa-times" /></Link></h4>
        </div>
      </div>
      <div className="row mx-0 d-flex justify-content-center">
        <div className="col-6 border rounded-lg m-5">
          <form className="py-3 px-md-5 px-1" onSubmit={handleSubmit(onSubmit)}>
            <small>
              Lorazepam, sold under the brand name Ativan among others, is a benzodiazepine medication. It is used to treat anxiety disorders, trouble sleeping, severe agitation, active seizures including status epilepticus, alcohol withdrawal, and chemotherapy-induced nausea and vomiting
            </small>
            <div>
              <span className="form-label">Upload Banner Image</span>
              {
                uplodedImg &&
                <div className='uploaded-img-preview border p-2'>
                  <img src={uplodedImg} alt="course-banner" className='mx-auto img-fluid' />
                </div>
              }
              <BannerImageUpload setUploadedImg={setUploadedImg} />
            </div>
            <Field
              type="text"
              name="name"
              label="Title"
              placeholder=""
              component={renderInputField}
            // validate={[required]}
            />
            <Field
              type="textarea"
              name="summary"
              label="Summary"
              placeholder=""
              component={renderInputField}
            // validate={[required]}
            />
            <Field
              type="text"
              name="promoVideo"
              label="Promotional video (YouTube URL only)"
              placeholder=""
              component={renderInputField}
            />
            <Field
              type="textarea"
              name="description"
              label="Description"
              placeholder=""
              component={renderInputField}
            />
            <div className="row m-0 border rounded-lg my-2 p-md-3 p-0">
              <div className="col-md-6 col-12">
                <Field
                  name="category"
                  label="Category"
                  placeholder="Article"
                  options={getOptions(categoryOptions, 'category')} // category is form variable
                  textField={'category'}
                  component={renderSelectField}
                  validate={[required]}
                />
              </div>
            </div>
            <div className="row my-4">
              <div className="col-12 text-right">
                <button type="submit" className="btn btn-primary" disabled={submitting}>
                  <span>{createCourseLoading ? <CustomLoader /> : 'Create Course'}</span>
                </button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </>
  )
}

export default reduxForm({
  form: 'create-lesson',
})(CreateLessonBlog);
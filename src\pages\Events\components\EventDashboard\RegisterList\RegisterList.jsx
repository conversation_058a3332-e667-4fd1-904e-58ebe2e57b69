import React, { useEffect } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { useParams } from 'react-router'
import BootstrapTable from 'react-bootstrap-table-next'
import exportFromJSON from 'export-from-json'

import { getColumns } from './helpers'
import { getEventDetails, getEventRegistrations } from '../../../actions'
import CustomLoader from '../../../../../components/sharedComponents/CustomLoader'

const RegisterList = () => {

  const dispatch = useDispatch()
  const {id} = useParams()
  
  const { eventRegistrationsLoading, eventRegistrationsList, eventRegistrationsCount  ,eventDetails} = useSelector((state) => state.event)

  useEffect(() => {
    dispatch(getEventDetails(id))
  }, [dispatch , id]);

  let columns = getColumns()

  const fileName = eventDetails && eventDetails.title
  const exportType = exportFromJSON.types.xls
  const data = eventRegistrationsList

  useEffect(() => {
    if(eventDetails){
    dispatch(getEventRegistrations(eventDetails._id))}
  }, [eventDetails , dispatch])

  const downloadExel = () => {
    exportFromJSON({ data, fileName, exportType })
  }

  return (
    <>
      <h1 className="m-4">Event Register List</h1>
      <div className="mb-2 d-flex align-items-center">
        <h6 className="mx-4">{`Total Registration : ${eventRegistrationsCount}`}</h6>
        <button className="btn btn-secondary" onClick={() => downloadExel()}>
          Download File
        </button>
      </div>
      {
        eventRegistrationsLoading ?
          <CustomLoader />
          :
          <>
            {eventRegistrationsList?.length ?
              <div className="table table-responsive">
                <BootstrapTable
                  keyField='id'
                  bordered={false}
                  data={eventRegistrationsList}
                  columns={columns}
                />
              </div>
              :
              <span>No Reigstration Available</span>
            }
          </>
      }
    </>
  )
}

export default RegisterList
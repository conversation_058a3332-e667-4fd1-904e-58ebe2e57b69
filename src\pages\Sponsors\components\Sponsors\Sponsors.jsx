import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Link, useNavigate } from "react-router-dom";
import { FaThLarge } from "react-icons/fa";
import { FaList } from "react-icons/fa";

import { Pagination } from "@akshayDatacode/datacode-ui";
import { getSponsors, deleteSponsor } from "../../actions";
import CustomLoader from "../../../../components/sharedComponents/CustomLoader";
import DeleteModal from "../../../../components/sharedComponents/DeleteModal";
import SponsorsTableView from "./SponsorsTableView";
import SponsorsGridView from "./SponsorsGridView";

const Sponsors = () => {

  const dispatch = useDispatch();
  const navigate = useNavigate();

  const { sponsorsList, sponsorsCount, sponsorsListLoading } =
    useSelector(({ sponsor }) => sponsor) || {};
    
  const [view, setView] = useState("grid");
  const [selected, setSelected] = useState(0);
  const [openModal, setOpenModal] = useState(false);
  const [tempDeleteId, setTempDeleteId] = useState();

  useEffect(() => {
    dispatch(getSponsors({ page: selected, limit: sponsorsPerPage }));
    window.scrollTo(0, 0);
  }, [dispatch , selected]);

  // Pagination
  const sponsorsPerPage = 5;
  const pageCount = Math.ceil(sponsorsCount / sponsorsPerPage);
  const changePage = ({ selected }) => {
    setSelected(selected);
    dispatch(getSponsors({ page: selected, limit: sponsorsPerPage }));
  };

  const handleEditSponsor = (id) => {
    navigate(`/sponsor/${id}`);
  };

  // Delete Sponsor
  const toggleModal = () => {
    setOpenModal(!openModal);
  };

  const handleSponsorDeleteModal = () => {
    dispatch(deleteSponsor(tempDeleteId)).then((res) => {
      if (res) {
        setOpenModal(!openModal);
        dispatch(getSponsors({ page: selected, limit: sponsorsPerPage }));
      }
    });
  };
  const handleDeleteSponsor = (id) => {
    setTempDeleteId(id);
    setOpenModal(!openModal);
  };

  return (
    <>
      <div className="row mx-0">
        <div className="col-12 nav-sponsor-header">
          <h3 className="py-2 mb-0">Community Sponsors</h3>
          <h3 className="mb-0">{sponsorsCount}</h3>
          <Link to="/sponsor/new">
            <button className="add-sponsor-btn">Add Sponsor</button>
          </Link>
        </div>
      </div>
      <div className="row mx-0">
        <div className="col-12 nav-sponsor-icon">
          <FaThLarge
            className={view === "grid" ? "active-icon" : "icon"}
            onClick={() => setView("grid")}
          />
          <FaList
            className={view === "list" ? "active-icon" : "icon"}
            onClick={() => setView("list")}
          />
        </div>
      </div>

      <div className="row mx-0 my-5">
        <div className="col-12 p-3">
          {sponsorsListLoading ? (
            <CustomLoader />
          ) : (
            <>
              {view === "list" ? (
                <SponsorsTableView
                  handleDeleteSponsor={handleDeleteSponsor}
                  handleEditSponsor={handleEditSponsor}
                  sponsorsList={sponsorsList}
                />
              ) : (
                <>
                  <SponsorsGridView
                    handleEditSponsor={handleEditSponsor}
                    handleDeleteSponsor={handleDeleteSponsor}
                    sponsorsList={sponsorsList}
                  />
                </>
              )}
            </>
          )}
        </div>
      </div>
      {sponsorsCount > 0 && (
        <div className="d-flex justify-content-center">
          <Pagination pageCount={pageCount} changePage={changePage} />
        </div>
      )}
      <DeleteModal
        open={openModal}
        toggle={toggleModal}
        onSubmit={handleSponsorDeleteModal}
        submitButtonName={"Delete Sponsor"}
        message={"Are you sure want to delete sponsor"}
      />
    </>
  );
};

export default Sponsors;

import { GrEdit } from "react-icons/gr";
import { GoPlus } from "react-icons/go";

import defaultimg from "../../../../../../assets/images/bg/user-avatar.avif";

const HostCard = ({ eventDetails }) => {
  return (
    <>
      <div className="card p-4 rounded-15">
        <div className="card-head">
          Organizers{" "}
          <span className="rounded-icon ml-2">
            <GoPlus />
          </span>
        </div>
        <div className="row d-flex">
          <div
            className="col-xl-3 col-lg-4 col-md-5 col-sm-6 col-10 border-dashed-purple mx-3 p-3 mt-3"
            style={{ backgroundColor: "#f1edfd" }}
          >
            <div className="d-flex row mx-0">
              <div className="d-flex flex-wrap flex-column col-4">
                <img
                  src={defaultimg}
                
                  className="rounded-image"
                  alt="user-avatar"
                />
                <span className="mt-1 text-center bio">Admin</span>
              </div>
              <div className="d-flex align-items-top  col-6 px-0 name">
                {eventDetails?.host?.firstName}
              </div>
              <div className="col-2 d-flex px-0">
                <div className="rounded-icon ">
                  <GrEdit />
                </div>
              </div>
            </div>
          </div>
          {/* {eventDetails?.co_hosts?.map((cohost)=>{
              <div  className='col-3 border-dashed-purple mx-3 p-3 mt-3' style={{ backgroundColor: "#f1edfd" }}>
              <div className='d-flex row'>
                <div className='d-flex flex-column col-4'>
                  <img src={"https://images.app.goo.gl/d8PCQnKhCV5zLnf2A"} className='rounded-image'></img>
                  <span className='mt-1 bio'>Admin</span>
                </div> 
                <div className='d-flex align-items-top  col-6 px-0 name'>{cohost?.firstName}</div>
                <div className='col-2 d-flex px-0'><div className='rounded-icon '><GrEdit /></div></div>
              </div>
            </div>
            })} */}
        </div>
      </div>
    </>
  );
};

export default HostCard;

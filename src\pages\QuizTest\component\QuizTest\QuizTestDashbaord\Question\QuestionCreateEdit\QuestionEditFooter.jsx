import { useSelector } from "react-redux";

import CustomLoader from "../../../../../../../components/sharedComponents/CustomLoader";

const QuestionEditFooter = ({
  setIsEditDrawer,
  questiontype,
  HandleAddFormData,
  HandleEditFormData,
}) => {
  const { questionCreateLoading } = useSelector(({ quizTest }) => quizTest);

  return (
    <>
      <div className='col-12 py-2 d-lg-flex justify-content-end edit-footer '>
        {questiontype.length > 0 ? (
          <>
            {questionCreateLoading ? (
              <button className='px-4 d-flex justify-content-center align-items-center publish-btn'>
                <CustomLoader color='white' height={30} width={30} />
              </button>
            ) : (
              <button
                className='publish-btn p-2 px-4'
                onClick={HandleAddFormData}
              >
                Create
              </button>
            )}
          </>
        ) : (
          <>
            <button
              onClick={() => {
                setIsEditDrawer(false);
              }}
              className='discard-btn p-2 px-4'
            >
              {" "}
              Discard Changes
            </button>
            {questionCreateLoading ? (
              <button className='px-4 d-flex justify-content-center align-items-center  publish-btn btn'>
                <CustomLoader color='white' height={30} width={30} />
              </button>
            ) : (
              <button
                className='publish-btn p-2 px-4'
                onClick={HandleEditFormData}
              >
                Publish
              </button>
            )}
          </>
        )}
      </div>
    </>
  );
};

export default QuestionEditFooter;

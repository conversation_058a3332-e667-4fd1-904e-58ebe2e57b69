import React from 'react'
import PropTypes from 'prop-types'
import { useDispatch, useSelector } from 'react-redux'
import { uploadImgToCloud } from '../../app/actions'
import CustomLoader from './CustomLoader'

const ProfileImageUpload = ({ setUploadedImg = () => {}, uplodedImg = 'https://res.cloudinary.com/datacode/image/upload/v1664786288/xs4idyfyatoqbsuvfoni.png' }) => {
  const dispatch = useDispatch()
  const { imgUploading } = useSelector((state) => state.app) || {}

  const handleImage = (e) => {
    const data = new FormData()
    data.append("file", e.target.files[0])
    data.append("upload_preset", "user-profile-img")
    data.append("cloud_name", "datacode")
    dispatch(uploadImgToCloud(data)).then((res) => {
      if (res && res.success) {
        return setUploadedImg(res.data)
      }
    })
  }

  return (
    <>
      <div className="row">
        <div className="col-12 border-2 d-flex justify-content-center align-items-center text-center">
          <>
            <input
              type="file"
              id="upload-btn"
              onChange={(e) => handleImage(e)}
              hidden
            />
            {imgUploading ?
              <div className="profile-blank-frame d-flex justify-content-center align-items-center">
                <div className="">
                  <CustomLoader />
                </div>
              </div>
              :
              <img
                className="border rounded-circle header-profile-img pic"
                height="140"
                width="140"
                loading="lazy"
                src={uplodedImg}
                alt="avatar"
              />
            }
            <label className="camera-icon" htmlFor="upload-btn">
              {
                // default img
                uplodedImg === 'https://res.cloudinary.com/datacode/image/upload/v1664786288/xs4idyfyatoqbsuvfoni.png'
                  ?
                  <i className="fal fa-camera" />
                  :
                  <i className="fal fa-edit" />
              }
            </label>
          </>
        </div>
      </div>
    </>
  )
}

ProfileImageUpload.propTypes = {
  setUploadedImg : PropTypes.func,
  uplodedImg: PropTypes.string,
};

export default ProfileImageUpload
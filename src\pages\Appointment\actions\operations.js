import axios from "axios";
import {
  ADD_APPOINTMENT_EVENT_LOADING,
  SET_APPOINTMENTS_LIST,
  DELETE_APPOINTMENT_EVENT_LOADING,
  GET_APPOINTMENT_EVENTS_LIST_LOADING,
  GET_APPOINTMENT_EVENT_DETAILS_LOADING,
  SET_APPOINTMENT_EVENTS_LIST,
  SET_APPOINTMENT_EVENT_DETAILS,
  GET_MENTOR_AVAILABILITIES_LIST_LOADING,
  SET_MENTOR_AVAILABILITIES_LIST,
  GET_MENTOR_APPOINTMENT_DELETE_LOADING,
  GET_SELECTED_SPEAKER_LOADING,
  GET_EDIT_MENTOR_AVAILABILITY_LOADING,
  GET_EDIT_APPOINTMENT_EVENT_FORM_LOADING,
  GET_APPOINTMENT_LIST_LOADING
} from "../constants";
import { generateQueryParams } from "../../../components/utils";
import { triggerNotifier } from "../../../components/utils/notifier";

const baseURL = process.env.REACT_APP_BASE_URL;

export const createAppointmentEvent = (appointmentEvent) => (dispatch) => {
  dispatch({ type: ADD_APPOINTMENT_EVENT_LOADING });
  return axios
    .post(`${baseURL}/appointment/event/create`, appointmentEvent)
    .then((res) => {
      if (res.status === 200) {
        triggerNotifier({
          message: "Create Appointment Event",
          type: "success",
          duration: 1000,
          icon: "👏",
        });
        dispatch({ type: ADD_APPOINTMENT_EVENT_LOADING });
        return {
          success: true,
          appointmentEvent: res.data && res.data.appointmentEvent,
        };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: ADD_APPOINTMENT_EVENT_LOADING });
      console.log("Add Appointment Event Error", error);
      triggerNotifier({
        message: "Add Appointment Error",
        type: 'error',
        duration: 1000,
        icon: "⚠️",
      });
    });
};

export const getAppointmentEvents = (data) => (dispatch) => {
  dispatch({ type: GET_APPOINTMENT_EVENTS_LIST_LOADING });
  return axios
    .get(
      `${baseURL}/appointment/events${generateQueryParams({
        limit: data?.limit,
        page: data?.page,
        search: data?.search,
        technology: data?.technology,
        schedule: data?.schedule,
      })}`
    )
    .then((res) => {
      if (res.status === 200) {
        triggerNotifier({
          message: "Appointment Events details loaded ",
          type: "success",
          duration: 1000,
          icon: "👏",
        });
        dispatch({ type: SET_APPOINTMENT_EVENTS_LIST, payload: res.data });
        dispatch({ type: GET_APPOINTMENT_EVENTS_LIST_LOADING });
        return {
          success: true,
          appointmentEvents: res.data && res.data.appointmentEvents,
        };
      } else {
        dispatch({ type: GET_APPOINTMENT_EVENTS_LIST_LOADING });
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: GET_APPOINTMENT_EVENTS_LIST_LOADING });
      console.log("Get Appointment Events Error", error);
      triggerNotifier({
        message: "Appointment Event Error",
        type: 'error',
        duration: 1000,
        icon: "⚠️",
      });
    });
};

export const getAppointmentsList = (data) => (dispatch) => {
  dispatch({ type: GET_APPOINTMENT_LIST_LOADING });
  return axios
    .get(
      `${baseURL}/appointment${generateQueryParams({
        limit: data?.limit || "all",
        page: data?.page || 0,
        event: data?.event,
        schedule: data?.schedule,
        filterDate: data?.filterDate || null,
      })}`
    )
    .then((res) => {
      if (res.status === 200) {
        triggerNotifier({
          message: "Appointment List Loaded",
          type: "success",
          duration: 1000,
          icon: "👏",
        });
        dispatch({ type: SET_APPOINTMENTS_LIST, payload: res?.data });
        dispatch({ type: GET_APPOINTMENT_LIST_LOADING });
        return { success: true, Appointments: res.data && res.data };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: GET_APPOINTMENT_LIST_LOADING });
      console.log("Get Appointments List Error", error);
      triggerNotifier({
        message: "Appointment list Error",
        type: 'error',
        duration: 1000,
        icon: "⚠️",
      });
    });
};

export const getAppointmentEventDetails = (id) => (dispatch) => {
  dispatch({ type: GET_APPOINTMENT_EVENT_DETAILS_LOADING });
  // dispatch({ type: GET_SELECTED_SPEAKER_LOADING });
  return axios
    .get(`${baseURL}/appointment/event/${id}`)
    .then((res) => {
      if (res.status === 200) {
        triggerNotifier({
          message: "Appointment Event Details loaded",
          type: "success",
          duration: 1000,
          icon: "👏",
        });
        dispatch({
          type: SET_APPOINTMENT_EVENT_DETAILS,
          payload: res.data.appointmentEvent,
        });
        // dispatch({ type: GET_SELECTED_SPEAKER_LOADING });
        dispatch({ type: GET_APPOINTMENT_EVENT_DETAILS_LOADING });
        return {
          success: true,
          appointmentEvent: res.data && res.data.appointmentEvent,
        };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      // dispatch({ type: GET_SELECTED_SPEAKER_LOADING });
      dispatch({ type: GET_APPOINTMENT_EVENT_DETAILS_LOADING });
      console.log("Get Speaker Details Error", error);
      triggerNotifier({
        message: "Something went wrong",
        type: 'error',
        duration: 1000,
        icon: "⚠️",
      });
    });
};

export const editAppointmentEvent = (event) => (dispatch) => {
  dispatch({ type: GET_EDIT_APPOINTMENT_EVENT_FORM_LOADING });
  return axios
    .patch(`${baseURL}/appointment/event/${event._id}`, { data: event })
    .then((res) => {
      if (res.status === 200) {
        triggerNotifier({
          message: "Edit Appointment Event",
          type: "success",
          duration: 1000,
          icon: "👏",
        });
        dispatch(getAppointmentEventDetails(event._id));
        dispatch({ type: GET_EDIT_APPOINTMENT_EVENT_FORM_LOADING });
        return { success: true, appointmentEvent: res.data && res?.data?.appointmentEvent };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: GET_EDIT_APPOINTMENT_EVENT_FORM_LOADING });
      console.log("Edit Speaker Details Error", error);
      triggerNotifier({
        message: "Something went wrong",
        type: 'error',
        duration: 1000,
        icon: "⚠️",
      });
    });
};

export const addAppointmentEventMentor = (event) => (dispatch) => {
  dispatch({ type: GET_SELECTED_SPEAKER_LOADING });
  return axios
    .patch(`${baseURL}/appointment/${event._id}/mentor/${event.mentor}`)
    .then((res) => {
      if (res.status === 200) {
        triggerNotifier({
          message: "Add Appointment Mentor",
          type: "success",
          duration: 1000,
          icon: "👏",
        });
        dispatch(getAppointmentEventDetails(event._id));
        dispatch({ type: GET_SELECTED_SPEAKER_LOADING });
        return { success: true };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: GET_SELECTED_SPEAKER_LOADING });
      console.log("Add Appointment Mentor Error", error);
      triggerNotifier({
        message: "Something went wrong",
        type: 'error',
        duration: 1000,
        icon: "⚠️",
      });
    });
};

export const deleteAppointmentEventMentor = (event) => (dispatch) => {
  dispatch({ type: GET_MENTOR_APPOINTMENT_DELETE_LOADING });
  return axios
    .delete(`${baseURL}/appointment/${event._id}/mentor/${event.mentor}`)
    .then((res) => {
      if (res.status === 200) {
        triggerNotifier({
          message: "delete Appointment Mentor Speaker",
          type: "success",
          duration: 1000,
          icon: "👏",
        });
        dispatch(getAppointmentEventDetails(event._id));
        dispatch({ type: GET_MENTOR_APPOINTMENT_DELETE_LOADING });
        return { success: true };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: GET_MENTOR_APPOINTMENT_DELETE_LOADING });
      console.log("delete Speaker Details Error", error);
      triggerNotifier({
        message: "Something went wrong",
        type: 'error',
        duration: 1000,
        icon: "⚠️",
      });
    });
};

export const deleteAppointmentEvent = (event) => (dispatch) => {
  dispatch({ type: DELETE_APPOINTMENT_EVENT_LOADING });
  return axios
    .delete(`${baseURL}/appointment/event/${event}`)
    .then((res) => {
      if (res.status === 200) {
        triggerNotifier({
          message: "Delete Appointment Event",
          type: "success",
          duration: 1000,
          icon: "👏",
        });
        // dispatch(getAppointmentsList(event._id));
        dispatch({ type: DELETE_APPOINTMENT_EVENT_LOADING });
        return { success: true, speaker: res.data && res.data.speaker };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: DELETE_APPOINTMENT_EVENT_LOADING });
      console.log("Delete Speaker Details Error", error);
      triggerNotifier({
        message: "Something went wrong",
        type: 'error',
        duration: 1000,
        icon: "⚠️",
      });
    });
};

// Appointments ---------------------------->

export const editAppointment = (appointment) => (dispatch) => {
  dispatch({ type: ADD_APPOINTMENT_EVENT_LOADING });
  return axios
    .patch(`${baseURL}/appointment/${appointment.id}`, { data: appointment })
    .then((res) => {
      if (res.status === 200) {
        triggerNotifier({
          message: "Edit Appointment",
          type: "success",
          duration: 1000,
          icon: "👏",
        });
        dispatch(
          getAppointmentsList({
            event: appointment.id,
            schedule: appointment.schedule,
          })
        );
        dispatch({ type: ADD_APPOINTMENT_EVENT_LOADING });
        return { success: true, appointment: res.data && res.data.appointment };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: ADD_APPOINTMENT_EVENT_LOADING });
      console.log("Edit Appointment Error", error);
      triggerNotifier({
        message: "Edit Appointment Error",
        type: 'error',
        duration: 1000,
        icon: "⚠️",
      });
    });
};

export const deleteAppointment = (id) => (dispatch) => {
  dispatch({ type: DELETE_APPOINTMENT_EVENT_LOADING });
  return axios
    .delete(`${baseURL}/appointment/${id}`)
    .then((res) => {
      if (res.status === 200) {
        triggerNotifier({
          message: "Delete Appointment details",
          type: "success",
          duration: 1000,
          icon: "👏",
        });
        dispatch({ type: DELETE_APPOINTMENT_EVENT_LOADING });
        return { success: true, appointment: res.data && res.data.appointment };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: DELETE_APPOINTMENT_EVENT_LOADING });
      console.log("Delete Appointment Error", error);
      triggerNotifier({
        message: "Delete Appointment Error",
        type: 'error',
        duration: 1000,
        icon: "⚠️",
      });
    });
};

// Admin Availability

export const getMentorAvailabilities = (data) => (dispatch) => {
  dispatch({ type: GET_MENTOR_AVAILABILITIES_LIST_LOADING });
  return axios
    .get(
      `${baseURL}/appointment/availability${generateQueryParams({
        limit: 1000,
        page: data?.page || 0,
        appointmentEvent: data?.event,
        mentor: data?.mentor,
      })}`
    )
    .then((res) => {
      if (res.status === 200) {
        triggerNotifier({
          message: "Mentor availabilties loaded",
          type: "success",
          duration: 1000,
          icon: "👏",
        });
        dispatch({ type: SET_MENTOR_AVAILABILITIES_LIST, payload: res.data.adminAvailabilities });
        dispatch({ type: GET_MENTOR_AVAILABILITIES_LIST_LOADING });
        return {
          success: true,
          mentorAvailabilities: res.data && res.data.adminAvailabilities,
        };
      } else {
        dispatch({ type: GET_MENTOR_AVAILABILITIES_LIST_LOADING });
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: GET_MENTOR_AVAILABILITIES_LIST_LOADING });
      console.log("Mentor availabilties Error", error);
      triggerNotifier({
        message: "Mentor availabilties Error",
        type: 'error',
        duration: 1000,
        icon: "⚠️",
      });
    });
};

export const editMentorAvailabilities = (availability) => (dispatch) => {
  dispatch({ type: GET_EDIT_MENTOR_AVAILABILITY_LOADING });
  return axios
    .patch(`${baseURL}/appointment/availability/${availability.availabilityId}`, { data: availability })
    .then((res) => {
      if (res.status === 200) {
        triggerNotifier({
          message: "Mentor availabilties loaded",
          type: "success",
          duration: 1000,
          icon: "👏",
        });
        //  dispatch( getMentorAvailabilities({ event: availability.appointmentEvent, mentor: availability.mentor }));
        dispatch({ type: GET_EDIT_MENTOR_AVAILABILITY_LOADING });
        return {
          success: true,
          mentorAvailabilities: res.data && res.data.adminAvailabilities,
        };
      } else {
        dispatch({ type: GET_EDIT_MENTOR_AVAILABILITY_LOADING });
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: GET_MENTOR_AVAILABILITIES_LIST_LOADING });
      console.log("Mentor availabilties Error", error);
      triggerNotifier({
        message: "Mentor availabilties Error",
        type: 'error',
        duration: 1000,
        icon: "⚠️",
      });
    });
};






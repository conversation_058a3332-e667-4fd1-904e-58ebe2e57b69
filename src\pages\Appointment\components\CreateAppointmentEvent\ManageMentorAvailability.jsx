// Import necessary modules
import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { Collapse } from 'reactstrap';

import MentorAvailabilityForm from './MentorAvailabilityForm';
import { getMentorAvailabilities } from "../../actions"
import CustomLoader from '../../../../components/sharedComponents/CustomLoader';

const ManageMentorAvailability = ({
  appointmentEventDetails
}) => {

  const dispatch = useDispatch();
  const { id } = useParams();
  
  const { mentorAvailabilitiesList, mentorAvailabilitiesListLoading } = useSelector((state) => state.appointment);

  const [isEdit, setEdit] = useState(false);
  const [openCollapseId, setOpenCollapseId] = useState(false);
  
  useEffect(() => {
    if (id === 'new') {
      setEdit(false);
    } else {
      setEdit(true);
    }
  }, []);

  const handleMentorCollapse = (mentorId) => {
    if (!openCollapseId) {
      dispatch(getMentorAvailabilities({ event: id, mentor: mentorId }))
    }
    setOpenCollapseId(openCollapseId === mentorId ? false : mentorId);
  };

  return (
    <>
      <div className="row mx-0 borders d-flex justify-content-center manage-mentor-availability">
        <div className="col-12 p-0">
          <div className='border manage-mentor-header'>
            <h1 >Manage Mentor Availability</h1>
          </div>
          {appointmentEventDetails.mentors.length ? (
            appointmentEventDetails && appointmentEventDetails.mentors.map((mentor, i) => (
              <div key={i} className="row mx-0 my-3  d-flex justify-content-center">
                <div className="col-12 form-body">
                  <div className="row mx-0 mentor-section">
                    <div className='col-12 px-2 py-4 align-items-center'>
                      <p className='mb-0 font-weight-bold' onClick={() => handleMentorCollapse(mentor._id)}>{`Mentor : ${mentor.name} Availability`}</p>
                      <Collapse isOpen={openCollapseId === mentor._id}>
                        {
                          mentorAvailabilitiesListLoading ? <CustomLoader /> :
                            <MentorAvailabilityForm
                              mentorAvailabilitiesList={mentorAvailabilitiesList}
                              appointmentEventDetails={appointmentEventDetails}
                              startDate={appointmentEventDetails?.startDate}
                              endDate={appointmentEventDetails?.endDate}
                              id={mentor._id}
                            />
                        }
                      </Collapse>
                    </div>
                  </div>
                </div>
              </div>
            ))
          ) : (
            <div>
              <h1 className='no-mentor-found'>No Mentor Found</h1>
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default ManageMentorAvailability;
import React from "react";

export const getColumns = (dispatch, session, userAttendancePresentLoading) => [
  {
    dataField: "",
    text: "S.NO.",
    align: "center",
    headerAlign: "center",
    headerClasses: "table-header s-no",
  },
  {
    dataField: "userName",
    text: "Username",
    headerClasses: "table-header name",
    style: { color: "#757575" },
  },
  {
    dataField: "",
    text: "Action",
    align: "center",
    headerAlign: "center",
    headerClasses: "table-header s-no",
    formatter: (cell, row, index) => {
      return (
        <div className=''>
          {attentedUser ? (
            <button className='btn btn-sm btn-danger'>Absent</button>
          ) : (
            <>
              {userAttendancePresentLoading ? (
                <CustomLoader />
              ) : (
                <button
                  className='btn btn-sm btn-primary'
                  onClick={() => handlePresentAction(dispatch, row, session)}
                >
                  Present
                </button>
              )}
            </>
          )}
        </div>
      );
    },
  },
];

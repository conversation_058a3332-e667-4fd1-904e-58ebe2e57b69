import React from 'react';
import axios from 'axios';
import { DndProvider, Draggable, Droppable } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';

const BatchContentList = ({ payload }) => {
  const handleDrop = async (droppedIndex) => {
    try {
      // Assuming your API endpoint is /api/reorder-problems
      await axios.post('/api/reorder-problems', { droppedIndex });
      // You can add logic to handle success or show a notification
    } catch (error) {
      console.error('Error:', error);
      // Handle error
    }
  };

  return (
    <>
      <DndProvider backend={HTML5Backend}>
        <Droppable droppableId="contentList">
          {(provided) => (
            <div {...provided.droppableProps} ref={provided.innerRef}>
              {problems.map((problem, index) => (
                <Draggable key={index} draggableId={`content-${index}`} index={index}>
                  {(provided) => (
                    <div
                      ref={provided.innerRef}
                      {...provided.draggableProps}
                      {...provided.dragHandleProps}
                      onDragEnd={() => handleDrop(index)} // Call API on drop
                    >
                      {problem}
                    </div>
                  )}
                </Draggable>
              ))}
              {provided.placeholder}
            </div>
          )}
        </Droppable>
      </DndProvider>
    </>
  )
}

export default BatchContentList
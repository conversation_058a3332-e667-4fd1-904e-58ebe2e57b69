import { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  UncontrolledDropdown,
  DropdownToggle,
  DropdownMenu,
  DropdownItem,
} from "reactstrap";
import {
  FaEllipsisV,
  FaArrowDown,
  FaArrowUp,
  FaTrash,
  FaEyeSlash,
  FaEye,
} from "react-icons/fa";

import { handleListOrder } from "../../../../../../components/utils";
import {
  deleteContent,
  editLesson,
  getLessonDetails,
} from "../../../../actions";
import { BlocksFramPreviewDictionary } from "./BlocksFramPreviewDictionry";
import ToolTip from "../../../../../../components/sharedComponents/ToolTip";

const ContentBlockLayout = ({
  children,
  blockHeader,
  item,
  previewVisibility,
}) => {

  const dispatch = useDispatch();
  const {
    lessonDetails: { contents },
  } = useSelector((state) => state.course);
  
  const [isPreview, setPreview] = useState(false);
 
  const contentsCount = contents.length;
  const index = contents.indexOf(item);

  const handleDelete = () => {
    dispatch(deleteContent(item._id)).then((res) => {
      if (res) {
        dispatch(getLessonDetails(item.lesson));
      }
    });
  };
  
  const handleContentOrder = (index, signal) => {
    console.log(item)

    if (contents) {
      let orderedContents = handleListOrder(contents, index, signal);
      dispatch(editLesson({ _id: item.lesson, contents: orderedContents }));
    }
  };

  return (
    <>
      <div className="border m-5 quiz-block">
        <div className="d-flex justify-content-between border-bottom quiz-header">
          <div>{isPreview ? "Preview" : blockHeader}</div>
          <div className="d-flex">
            {previewVisibility && (
              <div onClick={() => setPreview(!isPreview)}>
                {isPreview ? (
                  <ToolTip title="UnHide" placement="bottom">
                    <FaEyeSlash className="mr-3" />
                  </ToolTip>
                ) : (
                  <ToolTip title="Hide" placement="bottom">
                    <FaEye className="mr-3" />
                  </ToolTip>
                )}
              </div>
            )}

            <UncontrolledDropdown setActiveFromChild>
              <DropdownToggle tag="a">
                <ToolTip title="Edit" placement="bottom">
                  <FaEllipsisV />
                </ToolTip>
              </DropdownToggle>
              <DropdownMenu>
                {contentsCount > 1 &&
                  (index > 0 && index < contentsCount - 1 ? (
                    <>
                      <DropdownItem
                        onClick={() => handleContentOrder(index, 1)}
                      >
                        <FaArrowUp className="mr-2" />
                        Move up
                      </DropdownItem>
                      <DropdownItem
                        onClick={() => handleContentOrder(index, -1)}
                      >
                        <FaArrowDown className="mr-2" />
                        Move down
                      </DropdownItem>
                    </>
                  ) : index === 0 ? (
                    <DropdownItem onClick={() => handleContentOrder(index, -1)}>
                      <FaArrowDown className="mr-2" />
                      Move down
                    </DropdownItem>
                  ) : (
                    <DropdownItem onClick={() => handleContentOrder(index, 1)}>
                      <FaArrowUp />
                      Move up
                    </DropdownItem>
                  ))}
                <DropdownItem onClick={() => handleDelete()}>
                  <FaTrash className="mr-2" />
                  Delete
                </DropdownItem>
              </DropdownMenu>
            </UncontrolledDropdown>
          </div>
        </div>
        <div className="quiz-body">
          {isPreview ? BlocksFramPreviewDictionary(item) : children}
        </div>
      </div>
    </>
  );
};

export default ContentBlockLayout;

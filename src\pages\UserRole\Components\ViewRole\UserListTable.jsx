import React from 'react'
import BootstrapTable from 'react-bootstrap-table-next'

import { getColumns } from './helpers';

import './style.css';

const UserListTable = ({id , role,items, userIdRef}) => {
  
  let columns = getColumns({items ,userIdRef})
  
  return (
    <>
      {role?.users &&
        <div className="table table-responsive">
          <BootstrapTable
            keyField='id'
            data={role.users}
            columns={columns}
            search={{
              search: true,
              searchPosition: "top",
              delay: 500,
            }}
            bordered={false}
            hover
            condensed
            headerClasses="header-class"
            rowClasses="row-class"
          />
        </div>
      }
    </>
  )
}

export default UserListTable

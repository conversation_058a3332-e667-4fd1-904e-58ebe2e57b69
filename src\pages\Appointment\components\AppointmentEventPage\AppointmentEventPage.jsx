import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Link, useParams } from "react-router-dom";
import moment from "moment"
import { PiCopySimpleLight } from "react-icons/pi";
import { CiViewList, CiCalendar } from "react-icons/ci";

import {
  getAppointmentEventDetails,
  getAppointmentsList,
  editAppointment,
  deleteAppointment,
} from "../../actions/operations";
import ToolTip from "../../../../components/sharedComponents/ToolTip";
import CustomLoader from "../../../../components/sharedComponents/CustomLoader";
import { resetAppointmentsList } from "../../actions/actionCreators";
import AppointmentListRow from "./AppointmentListRow";
import DeleteModal from "../../../../components/sharedComponents/DeleteModal";

const AppointmentEventPage = () => {
  const dispatch = useDispatch();
  const { id } = useParams();

  const {
    appointmentsList,
    appointmentsCount,
    appointmentEventDetails,
    AppointmentManageEventLoading,
    appointmentListLoading,
  } = useSelector((state) => state.appointment) || {};

  const [schedule, setSchedule] = useState("upcoming");
  const [showNoteMeeting, setShowNoteMeeting] = useState(false);
  const [appointmentFilterdList, setAppointmentFilterdList] = useState([]);
  const [meetingNote, setMeetingNote] = useState("");
  const [editNote, setEditNote] = useState(false);
  const [filterDate, setFilterDate] = useState(moment());
  const [seeMore, setSeeMore] = useState(5);
  const [views, setViews] = useState("list");
  const [openModal, setOpenModal] = useState(false);
  const [tempDeleteId, setTempDeleteId] = useState();
  const [page, setPage] = useState(0);
  const formattedDate = filterDate.format("ddd D MMM");
  const formattedNextDate = filterDate.clone().add(1, "day").format("D");
  const formattedPreviousDate = filterDate
    .clone()
    .subtract(1, "day")
    .format("D");

  useEffect(() => {
    dispatch(
      getAppointmentsList({
        event: id,
        schedule,
        filterDate,
        limit: seeMore,
        page,
      })
    );
    dispatch(getAppointmentEventDetails(id));
  }, [schedule]);

  useEffect(() => {
    setAppointmentFilterdList(appointmentsList);
  }, [appointmentsList]);

  const handleNavTab = (id) => {
    setPage(0);
    dispatch(resetAppointmentsList([]));
    setSchedule(id);
  };

  const handleAddMeetingNotes = () => {
    setShowNoteMeeting(!showNoteMeeting);
  };

  const saveMeetingNotes = (appointmentID) => {
    dispatch(
      editAppointment({ id: appointmentID, remark: meetingNote, schedule })
    );
    alert(meetingNote);
  };

  const handleEditNote = (remark) => {
    setEditNote(true);
    setMeetingNote(remark);
  };

  const toggleModal = () => {
    setOpenModal(!openModal);
  };

  const handleDeleteAppointment = (id) => {
    setTempDeleteId(id);
    setOpenModal(!openModal);
  };

  const handleDeleteAppointmentModal = () => {
    dispatch(deleteAppointment(tempDeleteId)).then((res) => {
      if (res) {
        toggleModal()
        dispatch(
          getAppointmentsList({
            event: id,
            schedule,
            filterDate,
            limit: seeMore,
            page: page,
          })
        )
      }
    })
  }

  const handlePreviousDate = () => {
    setFilterDate((prevDate) => prevDate.clone().subtract(1, "day"));
  };

  const handleNextDate = () => {
    setFilterDate((prevDate) => prevDate.clone().add(1, "day"));
  };

  const getDateFilterAppointments = (list) => {
    if (views === 'list') {
      return list
    } else {
      return list.filter(
        (appointment) => appointment.date ===filterDate.format("DD/MM/YYYY")
      );
    }
  };

  const handleSeeMore = () => {
    setPage(page + 1);
    dispatch(
      getAppointmentsList({
        event: id,
        schedule,
        filterDate,
        limit: seeMore,
        page: page + 1,
      })
    );
  };

 
  return (
    <>
      <div>
        <div>
          <div className="shadow bottom appointment-detail-header">
            <div className="row mx-0 justify-content-center">
              <div className="col-9 my-5 d-flex justify-content-between appointment-header-heading">
                <h1 className="pt-2 detail-header-heading">
                  {appointmentEventDetails.title}
                </h1>
                <div className="detail-header-footer">
                  {AppointmentManageEventLoading ? (
                    <CustomLoader />
                  ) : (
                    <Link to={`/appointment/event/manage/${id}`}>
                      <ToolTip title="Manage Event Details" placement="bottom">
                        <span>Manage Event</span>
                      </ToolTip>
                    </Link>
                  )}
                  <ToolTip title="Copy link" placement="bottom">
                    <span>
                      <PiCopySimpleLight className="copy-icon" />
                      Copy Link
                    </span>
                  </ToolTip>
                </div>
              </div>
            </div>
          </div>
          <div className="my-5 row mx-0 justify-content-center appointment-detail-header-2">
            <div className="col-9 ">
              <div className="appointement-detailpage-border">
                <div className="d-flex justify-content-between align-items-center border-bottom">
                  <ul className="mb-0 appointement-header-2">
                    <ToolTip title="Upcoming appointment" placement="bottom">
                      <li
                        className={schedule === "upcoming" ? "active" : ""}
                        onClick={() => handleNavTab("upcoming")}
                        id="up-coming"
                      >
                        Upcoming
                        <span className="appointment-count">
                          {schedule === "upcoming" && appointmentsCount}
                        </span>
                      </li>
                    </ToolTip>
                    <ToolTip title="Pending appointment" placement="bottom">
                      <li
                        className={schedule === "pending" ? "active" : ""}
                        onClick={() => handleNavTab("pending")}
                        id="pending-appoint"
                      >
                        Pending
                        <span className="appointment-count">
                          {schedule === "pending" && appointmentsCount}
                        </span>
                      </li>
                    </ToolTip>
                    <ToolTip title="Past appointment" placement="bottom">
                      <li
                        className={schedule === "past" ? "active" : ""}
                        onClick={() => handleNavTab("past")}
                        id="past-appoint"
                      >
                        Past
                        <span className="appointment-count">
                          {schedule === "past" && appointmentsCount}
                        </span>
                      </li>
                    </ToolTip>
                  </ul>
                  <div className="d-flex align-items-end calendar-list">
                    <ToolTip title="list view" placement="bottom">
                      <CiViewList
                        className={`${views === "list" ? 'active' : 'text-secondary'} list-icon`}
                        onClick={() => setViews("list")}
                      />
                    </ToolTip>
                    <ToolTip title="calendar view" placement="bottom">
                      <CiCalendar
                        className={`${views === "calendar" ? 'active' : 'text-secondary'} calendar-icon`}
                        onClick={() => setViews("calendar")}
                      />
                    </ToolTip>
                  </div>
                </div>
                {appointmentListLoading ? (
                  <CustomLoader />
                ) : (
                  <div>
                    <>
                      {views === "calendar" && (
                        <div className="row mx-0 justify-content-center border">
                          <div className="col-5 my-2">
                            <ul className="mb-0 header-2-list">
                              <li
                                onClick={() => {
                                  handlePreviousDate();
                                }}
                              >
                                {formattedPreviousDate}
                              </li>
                              <li className="px-5 active">{formattedDate}</li>
                              <li
                                onClick={() => {
                                  handleNextDate();
                                }}
                              >
                                {formattedNextDate}
                              </li>
                            </ul>
                          </div>
                        </div>
                      )}
                    </>
                    <>
                      {
                        (appointmentsList && appointmentFilterdList?.length &&
                          getDateFilterAppointments(appointmentFilterdList).length) ?
                          <>
                            {getDateFilterAppointments(appointmentFilterdList).map(
                              (appointment) => (
                                <React.Fragment key={appointment._id}>
                                  <AppointmentListRow handleDeleteAppointment={handleDeleteAppointment} saveMeetingNotes={saveMeetingNotes} appointment={appointment} />
                                </React.Fragment>
                              ))
                            }
                            {appointmentFilterdList?.length >= 5 && (
                              <div className="d-flex justify-content-center see-more-button py-3 header-1-detailpage">
                                <span onClick={() => handleSeeMore()}>See More</span>
                              </div>
                            )}
                          </>
                          :
                          <div className="d-flex justify-content-center py-5">
                            <h4>No Appointments Available</h4>
                          </div>
                      }
                    </>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
      <DeleteModal
        open={openModal}
        toggle={toggleModal}
        onSubmit={handleDeleteAppointmentModal}
        submitButtonName={"Cancel Appointment"}
        message={"Are you sure want to cancel this appointment"}
        title={"Cancel Appointment"}
      />
    </>
  );
};

export default AppointmentEventPage;

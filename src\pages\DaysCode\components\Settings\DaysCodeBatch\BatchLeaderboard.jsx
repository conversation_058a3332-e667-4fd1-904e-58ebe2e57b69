import React from "react";
import {useSelector} from 'react-redux'

import CustomLoader from "../../../../../components/sharedComponents/CustomLoader";
import BatchLeaderboardTable from "../batchLeaderboardTable/BatchLeaderboardTable";

const BatchLeaderboard = () => {
  
  const { batchLeaderboard ,batchLeaderboardLoading} =
  useSelector((state) => state.dayscode) || {};

    return(
        <>
           {batchLeaderboardLoading ?
        (<div className="row mx-0 d-flex justify-items                                                                                                                                                    -center">
          <div className="col-12 align-items-center text-center ">
            <CustomLoader/>
          </div>
        </div>) 
        :
         ( <BatchLeaderboardTable  batchLeaderboard={batchLeaderboard}/>)}
          
          
        </>
    )
}

export default BatchLeaderboard
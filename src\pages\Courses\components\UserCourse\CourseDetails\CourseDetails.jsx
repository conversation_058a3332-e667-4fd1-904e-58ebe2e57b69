import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useLocation } from "react-router";
import { useParams } from "react-router-dom";

import { getCourseDetails, getCourseProgress, getCourseCurriculum } from "../../../actions";
import CourseDetailsInfo from "./CourseDetailsInfo";
import CourseDetailsCurriculum from "./CourseDetailsCurriculum";

const CourseDetails = () => {

  const dispatch = useDispatch();
  const { id } = useParams();
  const location = useLocation();

  const { courseDetails, courseProgress, courseCurriculum } = useSelector((state) => state.course) || {};
  const path = location.pathname;

  const [totalCourseLessons, setTotalCourseLessons] = useState([])

  useEffect(() => {
    if (id) {
      dispatch(getCourseProgress(id))
      dispatch(getCourseCurriculum(id))
      dispatch(getCourseDetails(id));
    }
  }, [dispatch, id])

  useEffect(() => {
    setTotalCourseLessons(getAllCourseLessonsList(courseCurriculum))
  }, [courseCurriculum])

  //get curriculum lessons----------
  const getAllCourseLessonsList = (courseCurriculum) => {
    const courseLessons = [];
    courseCurriculum?.sections && courseCurriculum?.sections.forEach((section) => {
      if (section) {
        section.lessons.forEach((lesson) => {
          courseLessons.push(lesson);
        });
      }
    });

    return courseLessons;
  };

  return (
    <>
      <div className="row mx-0">
        <div className="col-12 col-md-12 col-lg-12 col-sm-10">
          <h5>{path}</h5>
          <div className="row mx-0">
            <div className="col-lg-7 col-md-7 col-12 col-sm-12 p-2 ">
              <CourseDetailsInfo courseDetails={courseDetails} totalCourseLessons={totalCourseLessons} />
            </div>
            <div className="col-lg-5  col-md-5  col-12 col-sm-12 p-2 ">
              <CourseDetailsCurriculum courseDetails={courseDetails} totalCourseLessons={totalCourseLessons} courseProgress={courseProgress} />
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default CourseDetails;

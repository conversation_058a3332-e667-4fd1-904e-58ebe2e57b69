import React, { useState } from "react";
import { useSelector } from "react-redux";
import { Link } from "react-router-dom";
import PropTypes from "prop-types";
import { Dropdown } from "antd";
import { SettingOutlined } from "@ant-design/icons";
import _ from "lodash";

import { checkAdmin } from "../../../../../../core/auth";
import AddContentToBatchModal from "../../../../../../components/sharedComponents/AddContentToBatchModal";

const ProblemListCard = ({
  item,
  day,
  days,
  batch,
  batchContent,
  currentUser,
  deleteProblem,
  showAddBatchIcon,
  showDeleteFromBatchIcon,
  handleAddIntoBatch,
  handleDeleteFromBatch,
  problemListCardLayout,
  type,
  tempDay
}) => {
  const { codeBatchDetails } = useSelector((state) => state.dayscode) || {};
  
  const [showModal, setshowModal] = useState(false);
  const [isEdit, setIsEdit] = useState(false);

  const handleBatchProblemEdit = () => {
    setshowModal(true);
    setIsEdit(true);
  };

  const items = [
    {
      label: (
        <>
          <Link
            className='d-flex  mx-2 my-1 align-items-center'
            target={"_blank"}
            to={`/admin/days_code/problem/preview/${item?._id}`}
          >
            <i className='far fa-eye green' />
            <span className='ml-2 '>Preview</span>
          </Link>
        </>
      ),
      key: "1",
    },
    {
      label: (
        <>
          {type === "libraryProblem" ? (
            <div className='d-flex align-items-center mx-2 my-1 '>
              <Link
                to={`/admin/days_code/problem/${item?._id}`}
                className='text-dark'
              >
                <i className='far fa-edit' />
                <span className='ml-2 '>Edit</span>
              </Link>
            </div>
          ) : (
            <div
              className='d-flex align-items-center mx-2 my-1 '
              onClick={() => {
                handleBatchProblemEdit();
              }}
            >
              <i className='far fa-edit' />
              <span className='ml-2 '>Edit</span>
            </div>
          )}
        </>
      ),
      key: "2",
    },
    {
      label: (
        <>
          {showDeleteFromBatchIcon && (
            <div
              className='d-flex mx-2 my-1 align-items-center'
              onClick={() => handleDeleteFromBatch(batchContent._id)}
            >
              <i className='fas fa-trash' />
              <span className='ml-2 '>delete</span>
            </div>
          )}
        </>
      ),
      key: "3",
    },
    {
      label: (
        <>
          {checkAdmin(currentUser) && (
            <div className='d-flex align-items-center mx-2 my-1'>
              <i
                className='fas fa-trash red'
                onClick={() => deleteProblem(item?.id)}
              />
              <span className='ml-2 '>delete</span>
              {/* <DeleteFilled onClick={() => deleteProblem(item?.id)} /> */}
            </div>
          )}
        </>
      ),
      key: "4",
    },
  ];

  return (
    <>
      <div
        className={`row mx-0 my-1 
      border problem-list-card d-flex justify-content-between card-schadow p-2 ${
        problemListCardLayout ? `h-100` : ""
      }`}
      >
        <div className='col-8 px-1 '>
          <div className='mx-0'>
            {days ? (
              <p className='col-6 p-0 m-2'>
                Days:
                {_.isEmpty(days)
                  ? "No Schadule"
                  : days.map((item) => <b>{item} </b>)}
              </p>
            ) : (
              <p className='p-0 m-2'>
                Day: {day ? <b>{day} </b> : "No Schadule"}
              </p>
            )}

            {item?.level && <p className='mx-2 p-0'>Level: {item?.level}</p>}
            {item?.topic && <p className='mx-2 p-0'>Topic: {item?.topic}</p>}
            {batchContent?.status && (
              <p className='mx-2 p-0'>Status: {batchContent?.status}</p>
            )}
            {batchContent?.problem_type && (
              <p className='mx-2 p-0'>
                Problem Type: {batchContent?.problem_type}
              </p>
            )}
          </div>
          <p className='m-2 p-0 '>
            {type == "lesson" ? (
              <b>{item?.name?.slice(0, 25)}...</b>
            ) : (
              <b>{item?.title?.slice(0, 25)}...</b>
            )}
          </p>
        </div>
        <div className='col-3 px-0 align-items-baseline d-flex justify-content-end'>
          {showAddBatchIcon ? (
            <div className='d-flex '>
              {showAddBatchIcon ? (
                <div className='d-flex justify-content-center  m-2'>
                  <i
                    className='fas fa-plus'
                    onClick={() => {
                      setshowModal(true);
                    }}
                  />
                </div>
              ) : (
                ""
              )}
              <Link
                className='d-flex justify-content-end m-2'
                target={"_blank"}
                to={`/admin/days_code/problem/preview/${item?._id}`}
              >
                <i className='far fa-eye green ' />
              </Link>
            </div>
          ) : (
            <Dropdown menu={{ items }} trigger={["click"]} className='m-2'>
              <SettingOutlined
                onClick={(e) => e.preventDefault()}
                key='setting'
              />
            </Dropdown>
          )}
        </div>
      </div>

      {showModal && (
        <AddContentToBatchModal
          showModal={showModal}
          setshowModal={setshowModal}
          isEdit={isEdit}
          setIsEdit={setIsEdit}
          codeBatchDetails={codeBatchDetails}
          handleAddIntoBatch={handleAddIntoBatch}
          batch={batch}
          item={item}
          type={type}
          batchContent={batchContent}
          tempDay={tempDay}
        />
      )}
    </>
  );
};

ProblemListCard.defaultProps = {
  item: {},
  currentUser: {},
  deleteProblem: () => {},
  showAddBatchIcon: false,
  showDeleteFromBatchIcon: false,
  handleAddIntoBatch: () => {},
  handleDeleteFromBatch: () => {},
};

ProblemListCard.propTypes = {
  item: PropTypes.object,
  currentUser: PropTypes.object,
  deleteProblem: PropTypes.func,
  day: PropTypes.string,
  days: PropTypes.array,
  batchContentId: PropTypes.string,
  handleAddIntoBatch: PropTypes.func,
  handleDeleteFromBatch: PropTypes.func,
  showAddBatchIcon: PropTypes.bool,
  showDeleteFromBatchIcon: PropTypes.bool,
};

export default ProblemListCard;

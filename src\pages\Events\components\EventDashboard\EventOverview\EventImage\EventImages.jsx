import React, { useEffect, useState } from "react";
import { useParams } from "react-router";
import Carousel from "react-multi-carousel";
import { useDispatch, useSelector } from "react-redux";
import { Button } from "antd";
import { DeleteForeverRounded } from "@mui/icons-material";

import "react-multi-carousel/lib/styles.css";
import ToolTip from "../../../../../../components/sharedComponents/ToolTip";
import CustomLoader from "../../../../../../components/sharedComponents/CustomLoader";
import noImage from "../../../../../../assets/images/bg/noImage.png";
import BannerImageUpload from "../../../../../../components/sharedComponents/BannerImageUpload";
import { editEvent, getEventDetails, getEventsList } from "../../../../actions";

const EventImages = () => {
  
  const dispatch = useDispatch();
  const { id } = useParams();
  
  const { eventDetails, editEventLoading, eventDetailsLoading } =
    useSelector(({ event }) => event) || {};

  const [imagesData, setImagesData] = useState([]);

  const responsive = {
    superLargeDesktop: {
      breakpoint: { max: 4000, min: 3000 },
      items: 1,
    },
    desktop: {
      breakpoint: { max: 3000, min: 1024 },
      items: 1,
    },
    tablet: {
      breakpoint: { max: 1024, min: 464 },
      items: 1,
    },
    mobile: {
      breakpoint: { max: 464, min: 0 },
      items: 1,
    },
  };

  useEffect(() => {
    dispatch(getEventDetails(id)).then((res) => {
      setImagesData(res.data && res.data.eventPhotos);
    });
  }, []);

  const handleImageUpload = (image) => {
    debugger;
    setImagesData((prevImages) => [...prevImages, image]);
  };
  const handleDeleteImage = (image) => {
    setImagesData((prevImages) => prevImages.filter((img) => img !== image));
  };

  const handleUploadImage = () => {
    dispatch(editEvent({ _id: id, eventPhotos: imagesData })).then((res) => {
      dispatch(getEventsList());
    });
  };

  return (
    <div className='overview-event-images py-3'>
      <div className='event-images-header px-4'>
        <h1 className=''>You can add Event Images</h1>
        <span>Select images which you want to upload</span>
      </div>
      {eventDetailsLoading ? (
        <CustomLoader />
      ) : (
        <div className='event-images border rounded-lg bg-white  mx-3'>
          <div className='event-images-frame '>
            <Carousel
              swipeable={true}
              draggable={true}
              // showDots={true}
              // ssr={true}
              responsive={responsive}
              infinite={true}
              autoPlay={true}
              autoPlaySpeed={3000}
              customTransition='all .5'
              transitionDuration={500}
              containerClass='carousel-container'
              removeArrowOnDeviceType={["tablet", "mobile"]}
            >
              {imagesData.length !== 0 ? (
                imagesData&&imagesData.map((image, index) => (
                  <div key={image}>
                    <img
                      src={image}
                      alt='Event Image1'
                      width={"100%"}
                      height={400}
                    />
                  </div>
                ))
              ) : (
                <div>
                  <img
                    src={noImage}
                    alt='Event Image2'
                    width={"100%"}
                    height={400}
                  />
                </div>
              )}
            </Carousel>
          </div>

          <div className='event-images-array border '>
            <div className='d-flex flex-column'>
              <div className=''>
                <h2>Upload Image</h2>
                <BannerImageUpload setUploadedImg={handleImageUpload} />
              </div>
              <div className=''>
                {imagesData?.map((image, index) => (
                  <div key={image} className='event-image-name'>
                    <span className='mr-3'>{index + 1}.</span>
                    <img src={image} alt='Event Image3' width={50} height={50} />
                    <span className='mx-3'> Image{index + 1}.png</span>
                    <ToolTip title='Delete' color='#dc3545' placement='right'>
                      <span
                        className='delete-image-btn'
                        onClick={() => handleDeleteImage(image)}
                      >
                        <DeleteForeverRounded />
                      </span>
                    </ToolTip>
                  </div>
                ))}
              </div>
              <Button
                type='primary'
                size='large'
                className='upload-btn'
                onClick={handleUploadImage}
              >
                {editEventLoading ? <CustomLoader /> : "Upload"}
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default EventImages;

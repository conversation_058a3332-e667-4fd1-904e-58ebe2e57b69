import React from "react";
import PropTypes from "prop-types";
import { Tooltip } from "antd";

const ToolTip = ({
  title = "",
  children,
  placement = "top",
  color = "black",
}) => {
  return (
    <Tooltip title={title} placement={placement} color={color}>
      {children}
    </Tooltip>
  );
};

ToolTip.propTypes = {
  title: PropTypes.string,
  children: PropTypes.node.isRequired,
  placement: PropTypes.oneOf(["bottom", "top", "left", "right"]),
  color: PropTypes.string,
};

export default ToolTip;

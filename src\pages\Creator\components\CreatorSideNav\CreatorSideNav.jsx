import React from "react";
import { useDispatch, useSelector } from "react-redux";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { FaUsers } from "react-icons/fa";
import { MdDashboardCustomize } from "react-icons/md";
import { CgProfile } from "react-icons/cg";
import { FaChevronDown } from "react-icons/fa6";
import { MdEvent } from "react-icons/md";
import { FaRegClock } from "react-icons/fa6";
import { PiNotebookLight } from "react-icons/pi";
import { PiCreditCard } from "react-icons/pi";
import { PiMagnifyingGlassLight } from "react-icons/pi";
import { PiLightbulbLight } from "react-icons/pi";
import { PiMicrophoneLight } from "react-icons/pi";
import { SiGithubsponsors } from "react-icons/si";
import { PiLightningLight } from "react-icons/pi";
import { PiStudentBold } from "react-icons/pi";
import { FaCrown } from "react-icons/fa6";
import { FaLaptopCode } from "react-icons/fa";
import { IoShieldCheckmarkSharp } from "react-icons/io5";
import { PiQuestionLight } from "react-icons/pi";
import { MdOutlineAdminPanelSettings } from "react-icons/md";
import { IoLogOutOutline } from "react-icons/io5";

import usePermissions from "../../../../hooks/userPermission";
import ToolTip from "../../../../components/sharedComponents/ToolTip";
import { SET_LOGOUT_USER, SET_LOGIN_USER_LOADING } from "../../../Authentication/constants";
import "../../../../assets/scss/creator.scss";

const CreatorSideNav = () => {

  const { hasPermission } = usePermissions()
  const location = useLocation();
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const courseUserId = useSelector((state) => state.course.courseUserId);
  console.log(courseUserId);

  const currentUser = sessionStorage.getItem("currentUser") || localStorage.getItem("currentUser");
  const user = JSON.parse(currentUser);
  console.log(user?.courseUser);
  
  const handleLogout = () => {
    dispatch({ type: SET_LOGOUT_USER });
    dispatch({ type: SET_LOGIN_USER_LOADING });
    navigate("/login");
  }
  
  return (
    <>
      <div className="row mx-0 creator-side-nav">
        <div className="col-12 px-0 creator-side-bar">
          {/* Dashboard */}
          <div className="side-nav-dashboard">
            <p>DASHBOARD</p>
            <ul>
              <ToolTip title="Dashboard" placement="right">
                <li>
                  <Link
                    to="/"
                    className={`${location.pathname === "/" ? "active-tab" : ""
                      }`}
                  >
                    <MdDashboardCustomize />
                    <span>Dashboard</span>
                  </Link>
                </li>
              </ToolTip>
              {
                hasPermission("batch", "read") &&
                <ToolTip title="DaysCode" placement="right">
                  <li>
                    <Link
                      to="/admin/days_code/general"
                      className={`${location.pathname === "/admin/days_code/general" ? "active-tab" : ""
                        }`}
                    >
                     <FaLaptopCode />
                      <span>Dayscode</span>
                    </Link>
                  </li>
                </ToolTip>
              }
              {
                hasPermission("club", "read") &&
                <ToolTip title="DaysCode" placement="right">
                  <li>
                    <Link
                      to="/admin/club/application"
                      className={`${location.pathname === "/admin/club/application" ? "active-tab" : ""
                        }`}
                    >
                      <PiStudentBold />
                      <span>SLC Club</span>
                    </Link>
                  </li>
                </ToolTip>
              }
              {
                hasPermission("users", "read") &&
                <>
                  <ToolTip title="Users" placement="right">
                    <li>
                      <Link
                        to="/users"
                        className={`${location.pathname === "/users" ? "active-tab" : ""
                          }`}
                      >
                       <FaUsers />
                        <span>Users</span>
                      </Link>
                    </li>
                  </ToolTip>
                  <ToolTip title="Roles" placement="right">
                    <li>
                      <Link
                        to="/roles"
                        className={`${location.pathname === "/roles" ? "active-tab" : ""
                          }`}
                      >
                     <FaCrown />
                        <span>Roles</span>
                      </Link>
                    </li>
                  </ToolTip>
                  <ToolTip title="Permissions" placement="right">
                    <li>
                      <Link
                        to="/permissions"
                        className={`${location.pathname === "/permissions" ? "active-tab" : ""
                          }`}
                      >
                        <IoShieldCheckmarkSharp />
                        <span>Permissions</span>
                      </Link>
                    </li>
                  </ToolTip>
                </>
              }
              <ToolTip title="View your profile" placement="right">
                <li>
                  <Link
                    to="/profile"
                    className={`${location.pathname === "/profile" ? "active-tab" : ""
                      }`}
                  >
                    <div className="nav-profile">
                      <div className="profile">
                       <CgProfile />
                        <span>Profile</span>
                      </div>
                      <div className="arrow">
                        <FaChevronDown />
                      </div>
                    </div>
                  </Link>
                </li>
              </ToolTip>
            </ul>
          </div>

          {/* categories */}
          <div className="side-nav-categories">
            <p>CATEGORIES</p>
            <ul>
              {
                hasPermission("event", "read") &&
                <ToolTip title="Events" placement="right">
                  <li>
                    <Link to="/events">
                      <MdEvent />
                      <span>Events</span>
                    </Link>
                  </li>
                </ToolTip>
              }
              {
                hasPermission("appointment", "read") &&
                <ToolTip title="Appointments" placement="right">
                  <li>
                    <Link to="/appointments">
                      <FaRegClock />
                      <span>Appointments</span>
                    </Link>
                  </li>
                </ToolTip>
              }
              {
                hasPermission("course", "read") &&
                <>
                  <ToolTip title="Course" placement="right">
                    <li>
                      <Link to="/course">
                        <PiNotebookLight />
                        <span>Courses</span>
                      </Link>
                    </li>
                  </ToolTip>
                  <ToolTip title="My Courses" placement="right">
                    {(user?.courseUser) || (courseUserId) ? (<li>
                      <Link to="/courses/my">
                        <PiNotebookLight />
                        <span>My Courses</span>
                      </Link>
                    </li>) : (<li>
                      <Link to="/courses/user">
                        <PiNotebookLight />
                        <span>My Courses</span>
                      </Link>
                    </li>)}

                  </ToolTip>
                </>
              }
              {
                hasPermission("blog", "read") &&
                <ToolTip title="Blogs" placement="right">
                  <li>
                    <Link to="/blogs">
                      <PiNotebookLight />
                      <span>Blogs</span>
                    </Link>
                  </li>
                </ToolTip>
              }
              {
                hasPermission("batch", "read") &&
                <ToolTip title="Tutorials" placement="right">
                  <li>
                    <Link to="/tutorials">
                      <PiCreditCard />
                      <span>Tutorials</span>
                    </Link>
                  </li>
                </ToolTip>
              }
              {
                hasPermission("batch", "read") &&
                <ToolTip title="Interview-Questions" placement="right">
                  <li>
                    <Link to="/interview-question">
                      <PiMagnifyingGlassLight />
                      <span>Interview Question</span>
                    </Link>
                  </li>
                </ToolTip>
              }
              {
                hasPermission("batch", "read") &&
                <ToolTip title="Quiz Studio" placement="right">
                  <li>
                    <Link to="/quiztest">
                      <PiLightbulbLight />
                      <span>Quiz Studio</span>
                    </Link>
                  </li>
                </ToolTip>
              }
              {
                hasPermission("speaker", "read") &&
                <ToolTip title="Speakers" placement="right">
                  <li>
                    <Link to="/speakers">
                      <PiMicrophoneLight />
                      <span>Speakers</span>
                    </Link>
                  </li>
                </ToolTip>
              }
              {
                hasPermission("sponser", "read") &&
                <ToolTip title="Sponsers" placement="right">
                  <li>
                    <Link to="/sponsors">
                      <SiGithubsponsors />
                      <span>Sponsors</span>
                    </Link>
                  </li>
                </ToolTip>
              }
              {
                hasPermission("workshop", "read") &&
                <ToolTip title="Workshops" placement="right">
                  <li>
                    <Link to="/workshop">
                      <PiLightningLight />
                      <span>Workshop</span>
                    </Link>
                  </li>
                </ToolTip>
              }
              {
                hasPermission("batch", "read") &&
                <ToolTip title="WebPageContent" placement="right">
                  <li>
                    <Link to="/webpagecontent">
                      <MdOutlineAdminPanelSettings />
                      <span>Web Page Content</span>
                    </Link>
                  </li>
                </ToolTip>
              }
              <ToolTip title="FAQs" placement="right">
                <li id="faq-side-nav">
                  <Link to="/faq">
                    <PiQuestionLight />
                    <span>FAQs</span>
                  </Link>
                  {/*  <ToolTip message="FAQs" placement="right" id="faq-side-nav" /> */}
                </li>
              </ToolTip>
              <ToolTip title="Logout" placement="right">
                <li>
                  <Link className="logout-btn" onClick={handleLogout} to="/login">
                    <IoLogOutOutline />
                    <span>Logout</span>
                  </Link>

                </li>
              </ToolTip>
            </ul>
          </div>
        </div>
      </div>
    </>
  );
};

export default CreatorSideNav;

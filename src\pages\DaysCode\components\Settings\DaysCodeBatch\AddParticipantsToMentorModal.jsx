import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import Combobox from "react-widgets/Combobox";

import {
  getBatchParticipants,
  deleteParticipantTomentor,
} from "../../../actions";
import usePermissions from "../../../../../hooks/userPermission";
import CustomFormModal from "../../../../../components/sharedComponents/CustomFormModal";

const AddParticipantsToMentorModal = ({
  model,
  toggle,
  handleAssignParticipantsToMentor,
  mentorId,
  batch,
  mentorParticipantsList,
}) => {
  
  const dispatch = useDispatch();
  const { hasPermission } = usePermissions();
  
  const { batchParticipantsList } =
    useSelector((state) => state.dayscode) || {};

  const [participantId, setParticipantId] = useState("");

  useEffect(() => {
    if (model && mentorId) {
      dispatch(
        getBatchParticipants({
          batch: batch,
          limit: "All",
          status: " ",
          search: null,
        }),
      );
    }
  }, [model, mentorId, batch, dispatch]);

  const getBatchesOptions = (participants) => {
    return participants
      .filter((participant) => !participant.mentors.includes(mentorId))
      .map((participant) => ({
        participant: participant?.codeUser?.user?.firstName,
        value: participant._id,
      }));
  };
  

  const handleDeleteParticipantToMentor = (participantId) => {
    dispatch(
      deleteParticipantTomentor({
        mentor: mentorId,
        batchParticipant: participantId,
      }),
    ).then((res) => {
      if (res && res.success) {
        toggle();
        window.location.reload();
      }
    });
  };

  return (
    <>
      <CustomFormModal
        isOpen={model}
        toggleModal={toggle}
        title={"Assign Participants to the Mentor:"}
        onSubmit={() => handleAssignParticipantsToMentor(participantId)}
        onSubmitTitle={"Add"}
      >
        <div>
          {/* <h5 className='mr-4 '>Assign Participants to the Mentor:</h5> */}
          <div className='border mt-3 rounded-lg  p-3 card-schadow'>
            {mentorParticipantsList &&
              mentorParticipantsList.map((participant,i) => {
                return (
                  <>
                    <div className='p-2 d-flex align-items-center justify-content-between' key={i}>
                      <h6 className='mb-0' >
                        {participant?.codeUser?.user?.firstName}
                      </h6>
                      {hasPermission("batch", "delete") && (
                        <i
                          className='fas fa-times'
                          onClick={() =>
                            handleDeleteParticipantToMentor(participant?._id)
                          }
                        />
                      )}
                    </div>
                  </>
                );
              })}
            <div className='mt-1'>
              <Combobox
                data={getBatchesOptions(batchParticipantsList)}
                dataKey={"value"}
                textField='participant'
                placeholder={"Select Participant"}
                value={participantId}
                onChange={(value) => setParticipantId(value)}
              />
            </div>
          </div>
        </div>
      </CustomFormModal>
      {/* <Modal centered isOpen={model} toggle={toggle} className='text-center'>
        <ModalHeader
          className='d-flex justify-content-between align-items-center'
          toggle={toggle}
        ></ModalHeader>
        <ModalBody>
          <div>
            <h5 className='mr-4 '>Assign Participants to the Mentor:</h5>
            <div className='border mt-3 rounded-lg card-schadow p-3'>
              {mentorParticipantsList &&
                mentorParticipantsList.map((participant) => {
                  return (
                    <>
                      <div className='p-2 d-flex align-items-center justify-content-between'>
                        <h6 className='mb-0' key={participant?._id}>
                          {participant?.codeUser?.user?.firstName}
                        </h6>
                        {hasPermission("batch", "delete") && (
                          <i
                            className='fas fa-times'
                            onClick={() =>
                              handleDeleteParticipantToMentor(participant?._id)
                            }
                          />
                        )}
                      </div>
                    </>
                  );
                })}
              <div className='mt-1'>
                <Combobox
                  data={getBatchesOptions(batchParticipantsList)}
                  dataKey={"value"}
                  textField='participant'
                  placeholder={"Select Participant"}
                  value={participantId}
                  onChange={(value) => setParticipantId(value)}
                />
              </div>
            </div>
          </div>
        </ModalBody>
        <ModalFooter className='d-flex justify-content-center align-items-center'>
          <Button
            color='success'
            onClick={() => handleAssignParticipantsToMentor(participantId)}
          >
            Add
          </Button>
        </ModalFooter>
      </Modal> */}
    </>
  );
};

export default AddParticipantsToMentorModal;

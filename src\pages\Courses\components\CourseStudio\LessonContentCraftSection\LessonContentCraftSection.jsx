import React, { useState } from "react";
import { useSelector, useDispatch } from "react-redux";

import InputDoubleClickElement from "../../../../../components/services/InputDoubleClickElement";
import CustomLoader from "../../../../../components/sharedComponents/CustomLoader";
import { setLessonName } from "../../../actions";
import { editLesson, getLessonDetails } from "../../../actions/operations";
import { BlocksFramDictionry } from "./ContentBlocksFrams/BlocksFramDictionry";
import ToolTip from "../../../../../components/sharedComponents/ToolTip";
import LessonPreview from "../PreviewPages/LessonPreview/LessonPreview";

const LessonContentCraftSection = ({ type }) => {
  const dispatch = useDispatch();
  const { lessonDetails, createContentLoading } = useSelector((state) => state.course) || {};

  const [lessonName, setLessonNameState] = useState(
    lessonDetails?.name ? lessonDetails.name : "Untitled Lesson"
  );
  const [lessonDuration, setLessonDuration] = useState(
    lessonDetails?.duration ? lessonDetails.duration : "0"
  );
  const [toggleButton, setToggleButton] = useState(false);
  const [showInputEle, setShowInputEle] = useState(false);
  const [showInputElel, setShowInputElel] = useState(false);

  const handleEditLesson = () => {
    dispatch(editLesson({ id: lessonDetails._id, name: lessonName })).then((res) => {
      res && dispatch(getLessonDetails(lessonDetails?._id));
    });
    setShowInputEle(false);
    setToggleButton(false);
  };

  const handleEditLessonDuration = () => {
    dispatch(editLesson({ id: lessonDetails._id, duration: lessonDuration })).then((res) => {
      res && dispatch(getLessonDetails(lessonDetails?._id));
    });
    setShowInputElel(false);
    setToggleButton(false);
  };

  const handleInputChange = (value) => {
    dispatch(setLessonName({ name: value, id: lessonDetails._id }));
    setLessonNameState(value);
  };
  
  const handleInputChangeDuration = (value) => {
    dispatch(setLessonName({ duration: value, id: lessonDetails._id }));
    setLessonDuration(value);
  };

  const handleLessionPreviewButtonClick = () => {
    setToggleButton(!toggleButton);
  };

  return (
    <>
      <div className="row mx-0 lesson-craft-header border-bottom mr-2 ">
        <div className="col-12 col-md-9 col-lg-9 col-sm-12 px-5 py-2 d-flex justify-content-between">
          <h1 className="lesson-heading">
            <InputDoubleClickElement
              value={lessonName}
              handleChange={(e) => handleInputChange(e.target.value)}
              handleDoubleClick={() => setShowInputEle(true)}
              handleBlur={() => handleEditLesson()}
              showInputEle={showInputEle}
            />
            <p className="lesson-duration mb-0">
              Mins:
              <InputDoubleClickElement
                value={lessonDuration}
                handleChange={(e) => handleInputChangeDuration(e.target.value)}
                handleDoubleClick={() => setShowInputElel(true)}
                handleBlur={() => handleEditLessonDuration()}
                showInputEle={showInputElel}
              />
            </p>
          </h1>
        </div>
        <div className="col-12 col-md-3 col-lg-3 col-sm-12 mt-3">
          {toggleButton ? (
            <ToolTip title="Edit Preview" placement="bottom">
              <button className="btn btn-secondary" onClick={handleLessionPreviewButtonClick}>
                Edit Preview
              </button>
            </ToolTip>
          ) : (
            <ToolTip title="Lesson Preview" placement="bottom">
              <button className="btn btn-secondary" onClick={handleLessionPreviewButtonClick}>
                Lesson Preview
              </button>
            </ToolTip>
          )}
        </div>
      </div>

      <div className="row mx-0 lesson-craft">
        {toggleButton ? (
          <div className="col-12 p-0">
            <div>
              <LessonPreview component={true} />
            </div>
          </div>
        ) : (
          <div className="col-12 p-0">
            {lessonDetails.contents.map((item) => (
              <div key={item._id}>{BlocksFramDictionry(item)}</div>
            ))}
          </div>
        )}
      </div>
      <div className="row mx-0 block-drop-box">
        <div className="col-12 p-0 d-flex align-items-center justify-content-center">
          {createContentLoading ? (
            <CustomLoader />
          ) : (
            <small>Add Content Block</small>
          )}
        </div>
      </div>
    </>
  );
};

export default LessonContentCraftSection;

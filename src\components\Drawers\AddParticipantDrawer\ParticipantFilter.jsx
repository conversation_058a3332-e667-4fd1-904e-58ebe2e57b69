import React, { useState, useEffect } from "react";
import { FaPlus } from "react-icons/fa6";
import { useDispatch, useSelector } from "react-redux";

import {
  getDaysUsers,
  setDaysUsersList,
} from "../../../pages/DaysCode/actions";

const ParticipantFilter = ({ handleSelectedParticipant, onClose }) => {

  const dispatch = useDispatch();

  const { daysUsers } = useSelector((state) => state.dayscode) || {};

  const [filterQuery, setFilterQuery] = useState({
    search: "",
  });

  
  useEffect(() => {
    const debounceTimeout = setTimeout(() => {
      if (filterQuery.search?.trim() === "") {
        dispatch(setDaysUsersList([]));
      } else {
        dispatch(getDaysUsers(filterQuery));
      }
    }, 800);
    
    return () => clearTimeout(debounceTimeout);
  }, [filterQuery, dispatch]);
  
  const onclick = (user) => {
    onClose();
    handleSelectedParticipant(user?._id);
  };
  
  return (
    <>
      <div className='row mx-0'>
        <div className='col-12 px-0'>
          <div className='row mx-0'>
            <div className='col-12'>
              <label className='form-label'>Search Name</label>
              <input
                className='form-control'
                type='type'
                name='search'
                value={filterQuery.search}
                onChange={(e) =>
                  setFilterQuery({ [e.target.name]: e.target.value })
                }
              />
            </div>
          </div>
        </div>
        <div className='col-12 px-0'>
          {
            <div className='row mx-0 event-speaker-search-table'>
              {daysUsers &&
                daysUsers?.map((user, key) => { debugger;
                  return (
                  <div className='w-100 participant-search-card' key={key}>
                    <div className='d-flex justify-content-between'>
                      <div className='d-flex flex-column'>
                        <h6 className=''>{user.name}</h6>
                        <p className=''>{user.email}</p>
                      </div>
                      <div>
                        <FaPlus className='add' onClick={() => onclick(user)} />
                      </div>
                    </div>
                  </div>
                )})}
            </div>
          }
        </div>
      </div>
    </>
  );
};

export default ParticipantFilter;

import React from "react";
import { CgNotes } from "react-icons/cg";
import { GoClock } from "react-icons/go";
import { BsFlag } from "react-icons/bs";

import ReadMore from "./ReadMore";

const CourseDetailsInfo = ({ courseDetails, courseDetails: { name }, totalCourseLessons }) => {
  console.log("courseDetails", courseDetails);

  const totalDuration = totalCourseLessons && totalCourseLessons.reduce((total, lesson) => {
    const lessonDuration = parseInt(lesson.duration, 10);
    return total + lessonDuration;
  }, 0);

  return (
    <div className="p-2 course-detail-info">
      <div className="row mx-0">
        <div className="p-2 d-flex align-items-center course-details-header">
          <div className="col-12 col-md-4 col-lg-4 col-sm-12 px-0">
            <div className="header-icon text-center">
              <BsFlag className="icon" />
            </div>
          </div>
          <div className="col-12 col-md-8 col-lg-8 col-sm-12  px-0">
            <div className="header-info">
              <p className="fw-normal fs-6 mt-2">Capacity Network</p>
              <h3 className="fw-bold ">
                {" "}
                {name === ""
                  ? `Untitled Course`
                  : courseDetails.name}
              </h3>
              <div>
                <div className="clock">
                  <GoClock className="fw-normal fs-6 " />
                  <p className="fw-normal fs-6 ">
                    {Math.floor(totalDuration / 60)}h {totalDuration % 60}m
                  </p>
                </div>
                <div className="lesson">
                  <CgNotes className="fw-normal fs-6 " />
                  <p className="fw-normal fs-6 ">
                    {totalCourseLessons?.length} Lessons
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <hr />
      <div className="p-2 course-desciption">
        <h3>What you will learn</h3>
        <div>
          <ReadMore
            text={
              courseDetails.description === ""
                ? `No Description`
                : courseDetails.description
            }
            maxLength={250}
          />
        </div>
        <p className=" fs-6">
          <span className="fw-bold">Enrolled: </span>{" "}
          {courseDetails.users?.length} Learner
        </p>
      </div>
      {courseDetails?.author &&
        courseDetails?.author.map((teacher, index) => {
          return (
            <>
              <div className="d-flex align-items-center teacher-info">
                <img
                  src={teacher?.imgUrl}
                  alt="logo"
                  className="rounded-circle shadow  img-fluid"
                />
                <div>
                  <p className="teacher-name">Your Teacher</p>
                  <h4 className="fw-bold">{teacher?.firstName}</h4>
                </div>
              </div>
            </>
          );
        })}
    </div>
  );
};

export default CourseDetailsInfo;

import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";

import ProblemListCard from "../Problems/ProblemsList/ProblemListCard";
import CustomLoader from "../../../../../components/sharedComponents/CustomLoader";
import {
  deleteProblemFromBatch,
  editProblemIntoBatch,
  getBatchProblems,
} from "../../../actions";
import BatchContentHeader from "./BatchContentHeader";
import NoDataBlock from "../../../../../components/sharedComponents/NoDataBlock";
import AddBatchProblemDrawer from "./AddBatchProblemDrawer";

const ProblemList = ({
  batch,
  codeBatchDetails,
  codeBatchesLoading,
  setActiveTab,
}) => {
  const dispatch = useDispatch();

  const [problemStatus, setProblemStatus] = useState(null);
  const [problemType, setProblemType] = useState(null);
  const [openFilterModal, setFilterModal] = useState();
  const [open, setOpen] = useState(false);
  const [openAddDay, setOpenAddDay] = useState("");

  const showDrawer = () => {
    setOpen(true);
  };
  const onClose = () => {
    setOpen(false);
    setOpenAddDay("");
  };

  const { batchProblemList, problemsListLoading } = useSelector(
    ({ dayscode }) => dayscode,
  );

  useEffect(() => {
    dispatch(
      getBatchProblems({
        id: codeBatchDetails._id,
      }),
    );
  }, [
    getBatchProblems,
    problemStatus,
    problemType,
    dispatch,
    codeBatchDetails._id,
  ]);

  const handleDeleteProblemFromBatch = (problem) => {
    dispatch(deleteProblemFromBatch({ batch: batch, problem }));
  };

  const handleEditProblemIntoBatch = (contentData) => {
    dispatch(editProblemIntoBatch(contentData));
  };

  // const onChangeProblemType = (type) => {
  //   setProblemType(type.value);
  //   // getProblems({ status: problemStatus, problem_type: type.value });
  // };
  return (
    <div
      onClick={() => {
        setFilterModal(!openFilterModal);
      }}
    >
      <BatchContentHeader
        onChangeProblemType={setProblemType}
        setBatchContentStatus={setProblemStatus}
        batch={batch}
        type={"problem"}
        setActiveTab={setActiveTab}
        open={open}
        setOpen={setOpen}
        showDrawer={showDrawer}
        setOpenAddDay={setOpenAddDay}
        openAddDay={openAddDay}
        onClose={onClose}
      />

      {problemsListLoading ? (
        <div className='row d-flex justify-content-center align-items-center'>
          <div className='col-12 align-items-center text-center '>
            <CustomLoader />
          </div>
        </div>
      ) : (
        <div className='row mx-4 mt-3 border-top'>
          {batchProblemList.length > 0 ? (
            batchProblemList.map((problem, key) => (
              <div className='col-md-4 col-12 p-2 6' key={key}>
                <ProblemListCard
                  batch={batch}
                  day={problem?.day}
                  batchContent={problem}
                  item={problem.problemDetails}
                  showDeleteFromBatchIcon={true}
                  handleDeleteFromBatch={handleDeleteProblemFromBatch}
                  handleAddIntoBatch={handleEditProblemIntoBatch}
                  type={"problem"}
                />
              </div>
            ))
          ) : (
            // <div className='col-12 text-center h1 mt-5'>No Problem Found</div>
            <div className='w-100 h-100'>
              <NoDataBlock
                route={
                  <AddBatchProblemDrawer
                    batch={batch}
                    open={open}
                    setOpen={setOpen}
                    showDrawer={showDrawer}
                    setOpenAddDay={setOpenAddDay}
                    openAddDay={openAddDay}
                    onClose={onClose}
                  />
                }
                // btnName={"Add Problem"}
              />
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default ProblemList;

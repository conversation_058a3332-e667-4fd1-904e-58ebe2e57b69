import { useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
  DropdownItem,
  DropdownMenu,
  DropdownToggle,
  UncontrolledDropdown,
} from "reactstrap";
import { RiDeleteBin6Line } from "react-icons/ri";

import {
  addAppointmentEventMentor,
  deleteAppointmentEventMentor,
} from "../../actions/operations";
import ImageFrame from "../../../../components/sharedComponents/ImageFrame";
import ToolTip from "../../../../components/sharedComponents/ToolTip";
import CustomLoader from "../../../../components/sharedComponents/CustomLoader";
import AddSpeakerDrawer from "../../../../components/Drawers/AddSpeaker/AddSpeakerDrawer";

const AddAppointmentMentor = ({ id, appointmentEventDetails }) => {

  const dispatch = useDispatch();
  
  const { mentorAppointmentDeleteLoading, selectedSpeakerLoading } =
    useSelector((state) => state.appointment) || {};

  const [tempDeleteMentorId, setTempDeleteMentorId] = useState(null);

  const handleSelectedSpeaker = (speakerId) => {
    const event = {
      _id: id,
      mentor: speakerId,
    };

    dispatch(addAppointmentEventMentor(event));
  };
  const handleDeleteSpeaker = (speakerId) => {
    setTempDeleteMentorId(speakerId);
    const event = {
      _id: id,
      mentor: speakerId,
    };

    dispatch(deleteAppointmentEventMentor(event));
  };

  return (
    <>
      <div className="row mx-0 mt-3 border rounded p-3 mentor-section">
        <div className="col-12 p-0">
          <div className="speaker-header mb-3">
            <h3>Appointment Mentor List</h3>
            <AddSpeakerDrawer
              btnName={"Add New Mentor"}
              handleSelectedSpeaker={handleSelectedSpeaker}
            />
          </div>
          {appointmentEventDetails.mentors.length ? (
            <>
              <div className="row mx-0 py-3">
                {appointmentEventDetails &&
                  appointmentEventDetails.mentors &&
                  appointmentEventDetails.mentors.map((mentor, i) => (
                    <div className="col-12 col-md-3" key={i}>
                      <div className="speaker-card">
                        <UncontrolledDropdown
                          setActiveFromChild
                          className="event-host-action-toggle"
                        >
                          <DropdownToggle tag="span" className="card-action">
                            <ToolTip title="Remove Speaker" placement="bottom">
                              <RiDeleteBin6Line className="dlt-card-btn" />{" "}
                            </ToolTip>
                          </DropdownToggle>
                          <DropdownMenu
                            className="dropdown-menu mt-3 card-shadow"
                            left
                          >
                            <DropdownItem header>
                              <span>Actions</span>
                            </DropdownItem>
                            <DropdownItem
                              className="remove-btn"
                              onClick={() => handleDeleteSpeaker(mentor._id)}
                            >
                              Remove Speaker
                            </DropdownItem>
                          </DropdownMenu>
                        </UncontrolledDropdown>
                        {tempDeleteMentorId === mentor._id &&
                        mentorAppointmentDeleteLoading ? (
                          <CustomLoader />
                        ) : (
                          <>
                            <ImageFrame imgUrl={mentor.imgUrl} />
                            <p className="mb-0 text-truncate">
                              senior developer
                            </p>
                            <h5 className="mt-2">{mentor.name}</h5>
                          </>
                        )}
                      </div>
                    </div>
                  ))}
                {selectedSpeakerLoading && (
                  <div className="col-12 col-md-3">
                    <div className="speaker-card">
                      <CustomLoader />
                    </div>
                  </div>
                )}
              </div>
            </>
          ) : (
            <div>
              <h1 className="py-4 no-mentor-found">No Mentor Found</h1>
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default AddAppointmentMentor;

import { useState } from "react";
// import CreateQuestionModal from "./created_questions_list/CreateQuestionModal";
import { useSelector } from "react-redux";

import QuestionTypesDrawer from "./Drawers/QuestionTypesDrawer";

const QuestionHeader = () => {
  // const [openModal, setOpenModal] = useState(false);
  const { quizTestsQuestions } = useSelector((state) => state.quizTest);
  const [open, setOpenDrawer] = useState(false);

  // const totalScore = quizTestsQuestions.reduce((sum, ques) => sum + parseFloat(ques.maxScore || 0), 0);

  // const toggleModal = () => {
  //   setOpenModal(!openModal);
  // };
  return (
    <>
      <div className='col-12 py-2 px-0 mb-2 border-bottom align-items-baseline questions-header'>
        <div className='row mx-0 '>
          <div className='col-lg-6 px-0 d-flex align-items-baseline'>
            <h5>Questions ({quizTestsQuestions?.length})</h5>
            <span className='m-2'>Total Score : </span>
          </div>
          <div className='col-lg-6 px-0 d-flex justify-content-end align-items-center'>
            <button
              type='button'
              className='btn btn-primary custom-btn mr-1'
              onClick={() => {
                setOpenDrawer(true);
              }}
            >
              Create a new question
            </button>
            <button
              type='button'
              className='btn btn-outline-primary custom-btn ml-1'
            >
              Choose from library
            </button>
          </div>
        </div>
      </div>
      <QuestionTypesDrawer open={open} setOpenDrawer={setOpenDrawer} />

      {/* <CreateQuestionModal open={openModal} toggle={toggleModal} /> */}
    </>
  );
};

export default QuestionHeader;

import React, { useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';

import { editContent } from '../../../../../actions';
import ContentBlockLayout from '../ContentBlockLayout';

const CodeBlock = ({ item }) => {

   const dispatch = useDispatch();

  const [code, setCode] = useState("");
  const [language, setLanguage] = useState("javascript");

  useEffect(() => {
    setCode(item.payload?.code);
  }, [item]);

  const handleLanguageChange = (event) => {
    setLanguage(event.target.value);
  };

  const handleSubmit = () => {
    const data = {
      id: item._id,
      payload: { code, language }
    };
    dispatch(editContent(data));
  };

  const headerHtml = () => {
    return (
      <>
        <div className='embeds-block-header d-flex justify-content-between'>
            <div className='d-flex embeds-block-title'>
              <i className="fa-regular fa-pen-to-square" />
              <p className='mb-0'>Code Snippet</p>
          </div>
        </div>
      </>
    );
  };

  return (
    <>
      <ContentBlockLayout item={item} blockHeader={headerHtml()} previewVisibility={true}>
        <div className="row mx-0 border m-2">
          <div className='m-3'>
            <label htmlFor="language" className='form-label'>Choose Language: </label>
            <select id="language" value={language} onChange={handleLanguageChange}>
              <option value="javascript">JavaScript</option>
              <option value="python">Python</option>
              <option value="java">Java</option>
              <option value="cpp">C++</option>
              <option value="html">HTML</option>
              <option value="css">CSS</option>
              {/* Add more languages as needed */}
            </select>
          </div>

          <textarea
            value={code}
            onChange={(e) => setCode(e.target.value)}
            className='text-area'
          />
          <div className="col-12">
            <div className='d-flex justify-content-end pb-2'>
              <button onClick={handleSubmit} type="submit" className="btn btn-primary">Save</button>
            </div>
          </div>
        </div>
      </ContentBlockLayout>
    </>
  );
};

export default CodeBlock;

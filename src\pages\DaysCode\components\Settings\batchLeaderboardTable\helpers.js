import React from "react";

export const getColumns = () => [
  {
    dataField: "s. no.",
    text: "S.NO.",
    align: "center",
    headerAlign: "center",
    headerClasses: "table-header s-no",
    formatter: (cell, row, index) => <span>{++index}</span>,
  },
  {
    dataField: "id",
    text: "Id",
    align: "center",
    headerAlign: "center",
    sort: true,
    headerClasses: "table-header s-no",
    formatter: (cell, row, index) => <span>{row._id}</span>
  },
  {
    dataField: "total classes",
    text: "Total Classes",
    sort: true,
    headerClasses: "table-header name",
    style: { color: "#757575" },
    formatter: (cell, row) => <span>{row.totalClasses}</span>,
  },
  {
    dataField: "total score",
    text: "Total Score",
    sort: true,
    style: { color: "#757575" },
    headerClasses: "table-header status",
    formatter: (cell, row) => <span>{row.totalScore}</span>,
  },
  
]
import axios from "axios";
import * as actions from "./actionCreators";
import {
  CLUB_APPLICATION_LOADING,
  GET_CLUB_APPLICATIONS
} from "../constants";
import { generateQueryParams } from "../../../components/utils";
import { triggerNotifier } from "../../../components/utils/notifier";


const baseURL = process.env.REACT_APP_BASE_URL;

export const createApplication = (application) => (dispatch) => {
  dispatch({ type: CLUB_APPLICATION_LOADING });
  return axios
    .post(`${baseURL}/club/application/create`, application)
    .then((res) => {
      if (res.status === 200) {
        dispatch(actions.setClubApplication(res?.data?.application));
        return { success: true, application: res?.data };
      } else {
        return { success: false, message: "Something Went Wrong" };
      }
    })
    .catch((error) => {
      dispatch({ type: CLUB_APPLICATION_LOADING });
      console.log("Create Student Application Error", error);
       triggerNotifier({
              message: "Mentor availabilties Error",
              type: 'error',
              duration: 1000,
              icon: "⚠️",
            });
    });
};

export const getClubApplications = (query) => (dispatch) => {
  const { page, limit, search, status, startDate, endDate } = query;
  dispatch({ type: CLUB_APPLICATION_LOADING });
  return axios
    .get(`${baseURL}/club/application${generateQueryParams({
      page: page,
      limit: limit,
      search: search,
      status: status,
      startDate,
      endDate,
    })}`)
    .then((res) => {
      if (res.status === 200) {
        dispatch({
          type: GET_CLUB_APPLICATIONS,
          payload: res && res.data,
        });
        return { success: true, data: res && res.data };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: CLUB_APPLICATION_LOADING });
      console.log("Courses List Error", error);
      triggerNotifier({
              message: "Mentor availabilties Error",
              type: 'error',
              duration: 1000,
              icon: "⚠️",
            });
    });
};

export const getMemberClubApplication = (id) => (dispatch) => {

  dispatch({ type: CLUB_APPLICATION_LOADING });
  return axios
    .get(`${baseURL}/club/application/${id}`)
    .then((res) => {
      if (res.status === 200) {
        console.log(res, "res")
        dispatch(actions.setClubApplication(res?.data?.application));
        return { success: true, data: res?.data?.application };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: CLUB_APPLICATION_LOADING });
      console.log("Get Application Error", error);
      triggerNotifier({
              message: "Mentor availabilties Error",
              type: 'error',
              duration: 1000,
              icon: "⚠️",
            });
    });
};

export const deleteClubApplication = (id) => (dispatch) => {
  dispatch({ type: CLUB_APPLICATION_LOADING });
  return axios
    .delete(`${baseURL}/club/application/${id}`)
    .then((res) => {
      if (res.status === 200) {
        dispatch({ type: CLUB_APPLICATION_LOADING });
        return { success: true };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: CLUB_APPLICATION_LOADING });
      console.log("Delete Club Application Error", error);
      triggerNotifier({
              message: "Mentor availabilties Error",
              type: 'error',
              duration: 1000,
              icon: "⚠️",
            });
    });
};

export const editClubApplication = (application) => (dispatch) => {
  dispatch({ type: CLUB_APPLICATION_LOADING });
  return axios
    .patch(`${baseURL}/club/application/${application._id}`, { data: application })
    .then((res) => {
      if (res.status === 200) {
        dispatch(actions.setClubApplication(res?.data?.application));
        return { success: true, data: res && res.data };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: CLUB_APPLICATION_LOADING });
      console.log("Edit Lesson Error", error);
      triggerNotifier({
              message: "Mentor availabilties Error",
              type: 'error',
              duration: 1000,
              icon: "⚠️",
            });
    });
};
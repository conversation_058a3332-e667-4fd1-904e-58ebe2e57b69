import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from "antd";
import { Combobox } from "react-widgets";
import { useSelector,useDispatch } from "react-redux";
import { getQuizChallenges, addQuizIntoBatch } from "../../../actions";

import ProblemListCard from "../Problems/ProblemsList/ProblemListCard";
import { DSAContent } from "../../Constants/helper";
import { getTopicOptions } from "../../utils";

const AddBatchChallengeDrawer = ({
  batch,
  open,
  setOpen,
  showDrawer,
  setOpenAddDay,
  openAddDay,
  onClose,
  tempDay,
}) => {
  
  const dispatch = useDispatch();
  const { quizChallengesList } = useSelector((state) => state.dayscode) || {};

  const [topic, setTopic] = useState();
  const [placement, setPlacement] = useState("right");

  const handleQuizTopic = (topic) => {
    setTopic(topic.name);
    dispatch(getQuizChallenges({ status: "public", topic: topic.name }));
  };

  const handleAddQuizIntoBatch = (data) => {
    dispatch(
      addQuizIntoBatch({
        batch: data.batch,
        quizDetails: data.id,
        day: data.day,
        status: data.status,
      }),
    );
  };

  return (
    <>
      <Space>
        <Button type='primary' onClick={showDrawer}>
          Add Quiz Challenge
        </Button>
      </Space>
      <Drawer
        title='Add Quiz Challenge Into Batch'
        placement={placement}
        setPlacement={setPlacement}
        closable={false}
        onClose={onClose}
        open={open}
        key={placement}
      >
        <h1>Add Quiz Challenge</h1>
        <Combobox
          data={getTopicOptions(DSAContent)}
          dataKey={"value"}
          textField='name'
          placeholder={"Select Topic Name"}
          value={topic}
          onChange={(value) => handleQuizTopic(value)}
        />
        {quizChallengesList &&
          quizChallengesList.map((quiz, key) => (
            <div key={key}>
              <ProblemListCard
                handleAddIntoBatch={handleAddQuizIntoBatch}
                showAddBatchIcon={true}
                item={quiz}
                openAddDay={openAddDay}
                setOpenAddDay={setOpenAddDay}
                type={"quiz"}
                batch={batch}
                tempDay={tempDay}
              />
            </div>
          ))}
      </Drawer>
    </>
  );
};

export default AddBatchChallengeDrawer;

import axios from "axios";
import { SET_IMAGE_UPLOADING } from '../constants'

const baseURL = process.env.REACT_APP_BASE_URL;

const instance = axios.create()

export const uploadImgToCloud = (data) => (dispatch) => {
  dispatch({ type: SET_IMAGE_UPLOADING });
  return instance
    .post(`https://api.cloudinary.com/v1_1/datacode/image/upload`, data)
    .then(({ data }) => {
      console.log(data);
      dispatch({ type: SET_IMAGE_UPLOADING });
      return { success: true, data: data.url };
    })
    .catch((error) => {
      dispatch({ type: SET_IMAGE_UPLOADING });
      console.log("img uploading error", error);
    });
};

export const uploadFrameImgToCloud = (data, setIsLoading) => (dispatch) => {
  setIsLoading(true);
  return instance
    .post(`https://api.cloudinary.com/v1_1/datacode/image/upload`, data)
    .then(({ data }) => {
      console.log(data);
      setIsLoading(false);
      return { success: true, data: data.url };
    })
    .catch((error) => {
      setIsLoading(false);
      console.log("img uploading error", error);
    });
};
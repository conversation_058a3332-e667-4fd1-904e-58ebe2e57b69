import React, { useState } from "react"
import { Checkbox, Col, Row } from 'antd';
import { useDispatch } from "react-redux";

import { getClubApplications } from "../actions/operations";

const FilterPopover = ({ filterQuery }) => {
  const dispatch = useDispatch()
  
  
  const [startDate, setStartDate] = useState()
  const [endDate, setEndDate] = useState()

  const onChange = (checkedValues) => {
    dispatch(getClubApplications({ ...filterQuery, status: checkedValues }))
  };

  const handleDateFilter = () => {
    dispatch(getClubApplications({ ...filterQuery, startDate, endDate }))
  }

  return (
    <div className="row mx-0">
      <div className="col-12 p-0">
        <div className="d-flex align-items-center mb-2">
          <div className="">
            <input placeholder="Start Date" onChange={(e) => setStartDate(e.target.value)} className="form-control" type="date" value={startDate} name='startDate' />
          </div>
          <div className="">
            <input placeholder="End Date" onChange={(e) => setEndDate(e.target.value)} className="form-control" type="date" value={endDate} name='endDate' />
          </div>
          <div onClick={() => handleDateFilter()} className="btn btn-primary">search</div>
        </div>
        <div className="border-bottom mb-2">
          <h6>By Status</h6>
          <Checkbox.Group
            style={{
              width: '100%',
            }}
            onChange={onChange}
          >
            <Row>
              <Col span={8}>
                <Checkbox value="pending">Pending</Checkbox>
              </Col>
              <Col span={8}>
                <Checkbox value="accept">Accept</Checkbox>
              </Col>
              <Col span={8}>
                <Checkbox value="onhold">OnHold</Checkbox>
              </Col>
              <Col span={8}>
                <Checkbox value="reject">Reject</Checkbox>
              </Col>
              <Col span={8}>
                <Checkbox value="waiting">Waiting</Checkbox>
              </Col>
              <Col span={8}>
                <Checkbox value="flag">Flag</Checkbox>
              </Col>
              <Col span={8}>
                <Checkbox value="stage1">Stage 1</Checkbox>
              </Col>
              <Col span={8}>
                <Checkbox value="stage2">Stage 2</Checkbox>
              </Col>
            </Row>
          </Checkbox.Group>
        </div>
      </div>
    </div>
  )
}

export default FilterPopover
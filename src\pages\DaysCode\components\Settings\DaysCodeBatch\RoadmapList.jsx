import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { FaInfoCircle } from "react-icons/fa";

import AddBRoadmapDrawer from "./AddBRoadmapDrawer";
import {
  deleteRoadmapfromBatch,
  editLessonIntoBatch,
  editProblemIntoBatch,
  getBatchRoadmap,
} from "../../../actions/operations";
import RoadmapListCard from "./RoadmapListCard";
import BatchContentHeader from "./BatchContentHeader";
import NoDataBlock from "../../../../../components/sharedComponents/NoDataBlock";
import RoadmapDaysCard from "./RoadmapDaysCard";
import RoadmapColorInfoModal from "./RoadmapColorInfoModal";
import { ContentComponents } from "./ContentComponents";

const RoadmapList = ({ batch, codeBatchDetails, setActiveTab }) => {

  const dispatch = useDispatch();
  
  const { roadmapList, contentError } =
    useSelector((state) => state.dayscode) || {};
  const updateMode = (newmode) => {
    setMode(newmode);
  };

  const [mode, setMode] = useState("add");
  const [open, setOpen] = useState(false);
  const [openContent, setOpenContent] = useState(false);
  const [tempDay, setTempDay] = useState();
  const [openDaysModal, setOpenDaysModal] = useState(false);
  const [tempRoadmap, setTempRoadmap] = useState(null);
  const [selectedComponent, setSelectedComponent] = useState(null);
  const [openAddDay, setOpenAddDay] = useState("");
  const numbers = Array.from(
    { length: codeBatchDetails?.days },
    (_, i) => i + 1
  );
  const SelectedComponent = ContentComponents[selectedComponent];

  const showDrawer = () => {
    setOpen(true);
  };

  const showContentDrawer = () => {
    setOpenContent(true);
  };
  const onClose = () => {
    setOpen(false);
    setTempDay(null);
    setTempRoadmap(null);
  };
  const onContentClose = () => {
    setOpenContent(false);
    setSelectedComponent(null);
    setOpenAddDay("");
  };
  const handelroadmapdelete = (id) => {
    dispatch(
      deleteRoadmapfromBatch({
        batch: batch,
        roadmap: id,
      })
    );
  };

  const editAction = (roadMap) => {
    showDrawer();
    setMode("edit");
    setTempRoadmap(roadMap);
  };
  const findMissingItems = (data, day) => {
    const { lessons, quizes, problems } = data;

    const lessonExists = lessons?.some(
      (item) => item.day === day && item.status === "public"
    );
    const quizExists = quizes?.some(
      (item) => item.day === day && item.status === "public"
    );
    const problemExists = problems?.some(
      (item) =>
        item.day === day &&
        item.status === "public" &&
        item.problem_type === "assignment"
    );

    const missingItems = [];

    if (!lessonExists) missingItems.push("Lesson");
    if (!quizExists) missingItems.push("Quiz");
    if (!problemExists) missingItems.push("Problem");

    return missingItems;
  };
  const doesRoadmapExist = (data, day) => {
    const { roadmap } = data;
    const roadmapExists = roadmap.some((item) => item.day === day);
    return roadmapExists;
  };
  const onCardClick = (day) => {
    const roadmapExist = doesRoadmapExist(codeBatchDetails, day);
    if (roadmapExist) {
      setMode("edit");
      setTempRoadmap(roadmapList.filter((item) => item.day === day)[0]);
      showDrawer();
    } else {
      setMode("add");
      setTempDay(day);
      showDrawer();
    }
  };
  useEffect(() => {
    dispatch(getBatchRoadmap(batch));
  }, [dispatch]);

  return (
    <>
      <BatchContentHeader
        type="RoadMap"
        batch={batch}
        mode={mode}
        updateMode={updateMode}
        showDrawer={showDrawer}
        onClose={onClose}
        open={open}
        tempRoadmap={tempRoadmap}
        setActiveTab={setActiveTab}
        selectedComponent={selectedComponent}
        setSelectedComponent={setSelectedComponent}
        setOpenContent={setOpenContent}
        tempDay={tempDay}
      />

      <hr />
      <div className="d-flex row mt-3 days-card-container">
        <div className="col-12 d-flex justify-content-center days-head">
          Days Details{" "}
          <span className="mx-3" onClick={() => setOpenDaysModal(true)}>
            <FaInfoCircle />
          </span>
        </div>

        {numbers.map((number) => (
          <div
            key={number}
            className="my-3 col-1 "
            onClick={() => onCardClick(number)}
          >
            <RoadmapDaysCard
              day={number}
              findMissingItems={findMissingItems}
              codeBatchDetails={codeBatchDetails}
              showDrawer={showDrawer}
            />
          </div>
        ))}
      </div>
      <div className="mx-4 mt-3 border-top">
        {roadmapList.length > 0 ? (
          <RoadmapListCard
            roadmapList={roadmapList}
            handelroadmapdelete={handelroadmapdelete}
            editAction={editAction}
          />
        ) : (
          <NoDataBlock
            route={
              <AddBRoadmapDrawer
                batch={batch}
                mode={mode}
                updateMode={updateMode}
                showDrawer={showDrawer}
                onClose={onClose}
                open={open}
                tempRoadmap={tempRoadmap}
                error={contentError}
                selectedComponent={selectedComponent}
                setSelectedComponent={setSelectedComponent}
                setOpenContent={setOpenContent}
                tempDay={tempDay}
                setTempDay={setTempDay}
              />
            }
          />
        )}
      </div>
      <RoadmapColorInfoModal
        open={openDaysModal}
        toggle={() => setOpenDaysModal(!openDaysModal)}
      />
      {selectedComponent ? (
        <SelectedComponent
          batch={batch}
          open={openContent}
          setOpen={setOpenContent}
          showDrawer={showContentDrawer}
          setOpenAddDay={setOpenAddDay}
          openAddDay={openAddDay}
          onClose={onContentClose}
          tempDay={tempDay}
        />
      ) : null}
    </>
  );
};

export default RoadmapList;

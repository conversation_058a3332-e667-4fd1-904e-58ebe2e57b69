// usePermissions.js

const usePermissions = () => {
  const { roles } = JSON.parse(localStorage.getItem("currentUser"))

  const userPermissions = roles && roles.flatMap(role => role.permissions);

  const hasPermission = (requiredModule, requiredAccess) => {
    return userPermissions && userPermissions.some(permission =>
      permission.module === requiredModule && permission?.access.includes(requiredAccess)
    );
  };

  return { hasPermission };
};

export default usePermissions;
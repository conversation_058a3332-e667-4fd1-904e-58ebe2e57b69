import { connect } from "react-redux";
import {
  addProblem,
  getProblemDetails,
  editProblem,
  addAdminSolution,
  deleteAdminSolution,
  deleteProblemTestCase,
  addProblemTestCase,
  editProblemTestCase,
} from "../../../../actions";

import AddProblem from "./AddProblem";

const mapStateToProps = ({ dayscode, auth }) => ({
  currentUser: auth.currentUser ? auth.currentUser : {},
  problemDetailsLoading: dayscode.problemDetailsLoading,
  problemDetails: dayscode.problemDetails,
});

const mapDispatchToProps = {
  addProblem,
  getProblemDetails,
  editProblem,
  addAdminSolution,
  deleteAdminSolution,
  deleteProblemTestCase,
  addProblemTestCase,
  editProblemTestCase,
};

export default connect(mapStateToProps, mapDispatchToProps)(AddProblem);

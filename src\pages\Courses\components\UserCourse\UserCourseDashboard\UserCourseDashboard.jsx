import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Link } from "react-router-dom";

import { getUserCourses, getUserProgress } from "../../../actions/operations";
import UserProgress from "./UserProgress";
import CustomLoader from "../../../../../components/sharedComponents/CustomLoader";
import AllUserCourseList from "../AllUserCourses/AllUserCourseList";
import ToolTip from "../../../../../components/sharedComponents/ToolTip";

const UserCourseDashboard = () => {

  const dispatch = useDispatch();
  const {usercoursesList, userProgress, userProgressLoading} = useSelector((state) => state.course);

  const [userCourseList, setUserCourselist] = useState(true);
 
  useEffect(() => {
    dispatch(getUserCourses(courseUserId));
    dispatch(getUserProgress(courseUserId));
  }, []);
  const currentUser =JSON.parse(sessionStorage.getItem("currentUser") || localStorage.getItem("currentUser"));
  let courseUserId = currentUser?.courseUser;

  return (
    <>
      <div className="container-fluid">
        <div className="row mx-0">
          <div className="col-12 px-5 py-4">
            {userProgressLoading ? (
              <CustomLoader />
            ) : (
              <UserProgress userProgress = {userProgress} coursesList = {usercoursesList} />
            )}
            <div className="user-course-list">
              <div>
                <div className="row">
                  <div className="col-12 col-md-6 col-lg-6 col-sm-12">
                    {userCourseList ? (
                      <h3 className="f-wfw-bold ">Your Courses </h3>
                    ) : (
                      <h3 className="f-wfw-bold ">
                        There Is No Course Enrolled ,Please Browse The Courses
                      </h3>
                    )}
                  </div>
                  <div className="col-12 col-md-6 col-lg-6 col-sm-12">
                    <Link to="/courses">
                      <ToolTip title="Join For Courses" placement="top">
                        <button className="btn-courses  col-md-6">
                          <span>Browse All Courses</span>
                        </button>
                      </ToolTip>
                    </Link>
                  </div>
                </div>
                <div className="line">
                  <div className="line-double"></div>
                </div>
              </div>
              <AllUserCourseList
                coursesList={usercoursesList}
                userCourseList={userCourseList}
              />
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default UserCourseDashboard;
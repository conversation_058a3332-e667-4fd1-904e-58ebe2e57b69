import React, { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";

import { getCodeClasses } from "../../../../actions";
import LayoutContainer from "../../LayoutContainer";
import CustomLoader from "../../../../../../components/sharedComponents/CustomLoader";
import CodeClassCard from "./CodeClassCard";

const CodeClassesList = () => {
  
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const { codeClassess, codeClassesLoading } =
    useSelector((state) => state.dayscode) || {};

  useEffect(() => {
    dispatch(getCodeClasses());
  }, [dispatch]);
  return (
    <>
      <LayoutContainer>
        <div className="row mx-0 solution-nav">
          <div className="col-md-8 col-6 mx-0 d-flex align-items-center">
            <h4 className="py-md-3 py-2 mb-0">
              <i className="fas fa-code mr-2" />
              Code Classes
            </h4>
          </div>
          <div className="col-md-4 col-6 d-flex justify-content-end align-items-center">
            <button
              onClick={() => navigate("/admin/days_code/codeclass/new")}
              type="button"
              className="btn enroll-small-btn"
            >
              <small>
                <i className="fas fa-plus-circle mr-2" />
                Create Code Class
              </small>
            </button>
          </div>
        </div>
        <div className="row m-0 mx-0 d-flex align-items-stretch px-md-0 px-1">
          {codeClassesLoading ? (
            <div className="d-flex justify-content-center">
              <CustomLoader />
            </div>
          ) : (
            codeClassess &&
            codeClassess.map((codeClass, i) =>{
              return (
              <CodeClassCard codeClass={codeClass} key={codeClass._id} />
            )})
          )}
        </div>
      </LayoutContainer>
    </>
  );
};

export default CodeClassesList;

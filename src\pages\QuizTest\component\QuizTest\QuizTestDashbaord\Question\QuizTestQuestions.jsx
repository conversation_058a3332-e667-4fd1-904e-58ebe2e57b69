import React, { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useParams } from "react-router-dom";
// import CreateQuestionForm from './create_question_form/CreateQuestionForm'
import CreatedQuestionsList from "./created_questions_list/CreatedQuestionsList";
import QuestionsHeader from "./QuestionsHeader";
import "../../../../../../assets/scss/quiz_Dashboard/quiz_test_question.scss";
import { getQuizQuestionsList } from "../../../../actions/operations.js";
import CustomLoader from "../../../../../../components/sharedComponents/CustomLoader.jsx";

const QuizTestQuestions = ({ setActiveTab, activeTab }) => {
  
  const dispatch = useDispatch();
  const { id } = useParams();
  
  const { quizTestDataLoading } = useSelector((state) => state.quizTest);

  useEffect(() => {
    dispatch(getQuizQuestionsList(id));
  }, [id]);

  return (
    <>
      <div className='row mx-0 p-3 '>
        <QuestionsHeader />
        {quizTestDataLoading ? (
          <div className='w-100 text-center'>
            <CustomLoader />
          </div>
        ) : (
          <>
            <CreatedQuestionsList />
          </>
        )}
      </div>
    </>
  );
};

export default QuizTestQuestions;

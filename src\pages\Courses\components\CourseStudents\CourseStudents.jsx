import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Progress,Collapse, Button } from "reactstrap";
import { IoIosArrowDown, IoIosArrowUp } from "react-icons/io";

import CourseLayoutContainer from "../CourseLayoutContainer";
import {
  getCourseUsers,
  getCourses,
  enrollCourseToUser,
  deleteAsignedCourse,
  deleteCourseUsers
} from "../../actions/operations";
import teacherProfile from "../../../../assets/images/svg/workshop-page-testimonial/profile.png";
import AsignCourseModal from "./AsignCourseModal";
import CustomLoader from "../../../../components/sharedComponents/CustomLoader";
import DeleteModal from "../../../../components/sharedComponents/DeleteModal";

const CourseStudents = () => {
  
  const dispatch = useDispatch();

  const { coursesList, courseUsersListLoading, courseUsersList: { courseUsers } } = useSelector(({ course }) => course);

  const [collapse, setCollapse] = useState([]);
  const [showModel, setShowModel] = useState(false);
  const [courseUserId, setCourseUserId] = useState(null);
  const [openDeleteModal, setOpenDeleteModal] = useState(false);

  useEffect(() => {
    dispatch(getCourseUsers());
    dispatch(getCourses());
  }, [dispatch]);

  //---------Course Asign to User---------
  const handleEnrollCourseByAdmin = (courseId, courseUserId) => {
    dispatch(enrollCourseToUser({ courseId, courseUserId })).then((res) => {
      if (res && res.success) {
        closeModal();
        dispatch(getCourseUsers());
      }
    });
  };
  const openModal = (id) => {
    setCourseUserId(id); // Store the user ID in state
    setShowModel(true); // Open the modal
  };

  // Function to close the modal
  const closeModal = () => {
    setShowModel(false);
    setCourseUserId(null); // Reset the user ID
  };

  const toggleCollapse = (index) => {
    let collapseCopy = [...collapse];
    collapseCopy[index] = !collapseCopy[index];
    setCollapse(collapseCopy);
  };

  //---------UnAssign Course to user -------------
  const removeCourse = (courseId, courseUserId) => {
    dispatch(deleteAsignedCourse({ courseId, courseUserId })).then((res) => {
      if (res && res.success) {
        alert("Course Removed");
        setCollapse([]);
        dispatch(getCourseUsers());
      }
    });
  }

  const toggleModal = (courseUserID) => {
    setOpenDeleteModal(!openDeleteModal);
    setCourseUserId(courseUserID);
  };

  //-----------Delete Course User--------
  const handleCourseUserDeleteModal = () => {
    dispatch(deleteCourseUsers(courseUserId)).then((res) => {
      if (res) {
        dispatch(getCourseUsers());
        setOpenDeleteModal(!openDeleteModal);
      }
    });
  };

  return (
    <>
      <CourseLayoutContainer>
        <h1>Course Students {courseUsers?.length}</h1>
        {courseUsersListLoading ? (
          <CustomLoader />
        ) : (
          <div className="container-fluid">
            {courseUsers &&
              courseUsers?.map((courseUser, index) => {
                return (
                  <>
                    <div
                      className="row user_list mx-0 bg-light rounded p-3 mb-2"
                      key={courseUser._id}
                    >
                      <div className="col-12 col-md-2 col-lg-2 col-sm-12">
                        {courseUser.user?.imgUrl ? (
                          <img
                            src={courseUser.user?.imgUrl}
                            alt="avtar"
                            className="img-fluid user-img rounded-circle shadow"
                            width="50"
                            height="50"
                          />
                        ) : (
                          <img
                            src={teacherProfile}
                            alt="logo"
                            width="50"
                            height="50"
                          />
                        )}
                      </div>
                      <div className="col-12 col-md-2 col-lg-2 col-sm-12">
                        {courseUser.user?.firstName}
                      </div>
                      <div className="col-12 col-md-4 col-lg-4 col-sm-12">
                        {Math.floor(courseUser?.totalOverAllCoursesProgress)}%
                        <Progress
                          style={{
                            height: "8px",
                            backgroundColor: "#c3c3c3",
                            width: "304px",
                          }}
                          value={Math.floor(courseUser?.totalOverAllCoursesProgress)}
                        />
                      </div>
                      <div className="col-12 col-md-4 col-lg-4 col-sm-12">
                        <div className="row mx-0">
                          <div className="col-12 col-md-4 col-lg-4 col-sm-12 d-flex">
                            <button
                              className="course_asign_button mb-3 bg-success"
                              onClick={() => openModal(courseUser._id)}
                            >
                              Assign
                            </button>
                          </div>
                          <div className="col-12 col-md-4 col-lg-4 col-sm-12">
                            <Button>
                              {collapse === index ? (
                                <IoIosArrowUp
                                  className="up-arrow"
                                  onClick={() => toggleCollapse(index)}
                                />
                              ) : (
                                <IoIosArrowDown
                                  className="down-arrow"
                                  onClick={() => toggleCollapse(index)}
                                />
                              )}
                            </Button>
                          </div>
                          <div className="col-12 col-md-4 col-lg-4 col-sm-12 d-flex">
                            
                            <button className="course_denie_button mb-3 bg-danger"onClick={() => toggleModal(courseUser._id)} >
                                    Delete
                                    </button>
                          </div>
                        </div>
                      </div>
                    </div>
                    <Collapse isOpen={collapse[index]}>
                      <div className="container bg-white ">
                        {courseUser.enrollments &&
                          courseUser.enrollments.map((course, innerIndex) => {
                            return (
                              <>
                                <div className="row mx-0" key={course._id}>
                                  <div className="col-12 col-md-6 col-lg-6 col-sm-12 d-flex align-items-start justify-content-start">
                                    {innerIndex + 1}. <p>{course?.name}</p>
                                  </div>

                                  <div className="col-12 col-md-6 col-lg-6 col-sm-12">
                                    <button className="course_denie_button mb-3 bg-danger" onClick={() => removeCourse(course._id, courseUser._id)}>
                                    Unassign
                                    </button>
                                  </div>
                                  <hr></hr>
                                </div>
                              </>
                            );
                          })}
                      </div>
                    </Collapse>
                    <AsignCourseModal
                      model={showModel}
                      openModal={openModal}
                      closeModal={closeModal}
                      courseUserId={courseUserId}
                      coursesList={coursesList}
                      handleEnrollCourseByAdmin={handleEnrollCourseByAdmin}
                    />
                  </>
                );
              })}
          </div>
        )}
      </CourseLayoutContainer>
      <DeleteModal
        open={openDeleteModal}
        toggle={toggleModal}
        onSubmit={handleCourseUserDeleteModal}
        submitButtonName={"Delete"}
        message={" Are you sure want to delete this course User"}
        title={"Delete Course User"}
      />
    </>
  );
};

export default CourseStudents;

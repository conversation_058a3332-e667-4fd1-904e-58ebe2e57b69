import React, { useState } from "react";
import { useDispatch } from "react-redux";

import ContentBlockLayout from "../ContentBlockLayout";
import { editContent } from "../../../../../actions";
import BannerImageUpload from "../../../../../../../components/sharedComponents/BannerImageUpload";

const ImageUpload = ({ item }) => {

  const dispatch = useDispatch();

  const [uplodedImg, setUploadedImg] = useState("");
  
  const handleSubmit = () => {
    const data = {
      id: item._id,
      payload: uplodedImg,
    };
    dispatch(editContent(data));
  };

  const headerHtml = () => {
    return (
      <>
        <div className="embeds-block-header d-flex justify-content-between">
          <div>
            <div className="d-flex embeds-block-title">
              <i className="fa-regular fa-image" />
              <p className="mb-0">Image Upload</p>
            </div>
          </div>
        </div>
      </>
    );
  };

  return (
    <>
      <ContentBlockLayout
        item={item}
        blockHeader={headerHtml()}
        previewVisibility={true}
      >
        <div className="row mx-0 border m-2">
          <div className="col-12">
            <BannerImageUpload setUploadedImg={setUploadedImg} />
            <div className="d-flex justify-content-end pb-2">
              <button
                onClick={() => handleSubmit()}
                type="submit"
                className="btn btn-primary"
              >
                Save
              </button>
            </div>
          </div>
        </div>
      </ContentBlockLayout>
    </>
  );
};

export default ImageUpload;

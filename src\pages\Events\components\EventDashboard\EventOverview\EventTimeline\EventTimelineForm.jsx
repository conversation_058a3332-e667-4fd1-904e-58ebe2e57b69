import React, { useState } from "react";
import { Field, reduxForm } from "redux-form";
import { useDispatch, useSelector } from "react-redux";
import { useParams } from "react-router-dom";
import toast from "react-hot-toast";

import { renderInputField } from "../../../../../../components/sharedComponents/ReduxFormFields";
import { required } from "../../../../../../components/utils/validators";
import { addEventTimeline } from "../../../../actions";
import ToolTip from "../../../../../../components/sharedComponents/ToolTip";

const EventTimelineForm = ({
  setActiveTab,
  speakersLoading,
  reset,
  handleSubmit,
  submitting,
  initialize,
  imageUploading,
  onCloseDrawer,
}) => {
  
  const dispatch = useDispatch();
  const { id } = useParams();
  const { eventDetails } = useSelector((state) => state.event) || {};

  const [imgUrl, setImgUrl] = useState("");

  /*   const getUploadImgUrl = (url) => {
    if (url) {
      setImgUrl(url);
    }
  };
 */
  const onSubmit = (values) => {
    if (values) {
      const timeline = { ...values };
      timeline["img"] = imgUrl;
      timeline["eventId"] = (eventDetails && eventDetails._id) || id;
      toast.promise(
        dispatch(addEventTimeline(timeline)).then((res) => {
          if (res && res.success) {
            reset("event-timeline");
          }
        }),
        {
          loading: "Adding Timline",
          success: <b>Timeline Added!</b>,
          error: <b>Could not save.</b>,
        },
      );
    }
  };

  return (
    <>
      <div className='row d-flex justify-content-center'>
        <div className='col-12'>
          <form className='add-new-timeline' onSubmit={handleSubmit(onSubmit)}>
            <div className='row mx-0 p-2 border rounded-lg bg-white event-reg'>
              <div className='col-12 p-0' id='form'>
                <div className='px-2 login-card '>
                  <Field
                    type='text'
                    name='title'
                    label='Title'
                    placeholder=''
                    component={renderInputField}
                    validate={[required]}
                  />
                  <Field
                    type='textarea'
                    name='description'
                    label='Description'
                    placeholder=''
                    component={renderInputField}
                    validate={[required]}
                  />
                  <Field
                    type='text'
                    name='day'
                    label='Day'
                    placeholder=''
                    component={renderInputField}
                    validate={[required]}
                  />
                  <div className='row'>
                    <div className='col-6'>
                      <Field
                        type='date'
                        name='start_date'
                        label='Start Date'
                        placeholder=''
                        component={renderInputField}
                        validate={[required]}
                      />
                    </div>
                    <div className='col-6'>
                      <Field
                        type='time'
                        name='start_time'
                        label='Start Time'
                        placeholder=''
                        component={renderInputField}
                        validate={[required]}
                      />
                    </div>
                  </div>
                  <div className='row mb-3'>
                    <div className='col-6'>
                      <Field
                        type='date'
                        name='end_date'
                        label='End Date'
                        placeholder='<EMAIL>'
                        component={renderInputField}
                        // validate={[required]}
                      />
                    </div>
                    <div className='col-6'>
                      <Field
                        type='time'
                        name='end_time'
                        label='End Time'
                        placeholder=''
                        component={renderInputField}
                        // validate={[required]}
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className='row my-4'>
              <div className='col-12 btn-list'>
                <ToolTip title='Cancel' placement='bottom'>
                  <button
                    className='btn btn-secondary mr-2'
                    onClick={() => onCloseDrawer()}
                  >
                    <span>Cancel</span>
                  </button>
                </ToolTip>
                <ToolTip title=' Add timeline' placement='bottom'>
                  <button
                    type='submit'
                    className='btn btn-primary'
                    // disabled={submitting}
                  >
                    <span>Add Timeline</span>
                  </button>
                </ToolTip>
              </div>
            </div>
          </form>
        </div>
      </div>
    </>
  );
};

export default reduxForm({
  form: "event-timeline",
})(EventTimelineForm);

import QuizBlockMultiOptions from "./QuizBlock/QuizBlockMultiOptions";
import QuizBlockSingleOption from "./QuizBlock/QuizBlockSingleOption";
import TextEditorBlock from "./TextBlocks/TextEditorBlock";
import TextEditorElementsBlock from "./TextBlocks/TextEditorElementsBlock";
import EmbedBlock from "./EmbedBlocks/EmbedBlock";
import ImageUploadBlock from "./ImageBlock/ImageUploadBlock";
import CodeBlock from "./CodeBlock/CodeBlock";

export const BlocksFramDictionry = (item) => {
  const componentDictionary = {
    quizMultiOption: <QuizBlockMultiOptions item={ item } form={ `form-${item._id}` } />,
    quizSingleOption: <QuizBlockSingleOption item={ item } form={ `form-${item._id}` } />,
    textEditor: <TextEditorBlock item={ item } />,
    textEditorElements: <TextEditorElementsBlock item={ item } />,
    youtubeBlock: <EmbedBlock item={ item } placeholder="Paste in https://..." />,
    codePenBlock: <EmbedBlock item={ item } placeholder="https://codepen.io/..." />,
    githubGistBlock: <EmbedBlock item={ item } placeholder="https://gist.github.com/..." />,
    imageBlock: <ImageUploadBlock item={item} placeholder="upload image"/>,
    codeBlock: <CodeBlock item={item}/>
  };

  return componentDictionary[ item.type ];
};
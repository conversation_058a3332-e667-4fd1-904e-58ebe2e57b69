import React from "react";
import { <PERSON><PERSON>, Draw<PERSON>, <PERSON> } from "antd";
import { useState } from "react";

import ParticipantFilter from "./ParticipantFilter";

const AddParticipantDrawer = ({ handleSelectedParticipant, btnName }) => {

  const [open, setOpen] = useState(false);
  const [placement, setPlacement] = useState("right");

  const showDrawer = () => {
    setOpen(true);
  };
  
  const onClose = () => {
    setOpen(false);
  };

  return (
    <>
      <Space>
        <Button className='add-speaker-btn' type='primary' onClick={showDrawer}>
          {btnName}
        </Button>
      </Space>
      <Drawer
        title='Select Participant'
        placement={placement}
        closable={false}
        onClose={onClose}
        open={open}
        key={placement}
      >
        <ParticipantFilter
          handleSelectedParticipant={handleSelectedParticipant}
          onClose={onClose}
        />
      </Drawer>
    </>
  );
};
export default AddParticipantDrawer;

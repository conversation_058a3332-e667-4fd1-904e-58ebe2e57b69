* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.creator-dashboard {
  height: 100vh;
  background: #f5f6f7;
}

.header {
  height: 8vh;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.header .header-logo img {
  width: 180px;
  height: 55px;
}
.header .header-menu {
  width: 100%;
  padding: 16px 40px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.header .header-menu .search-input label {
  width: 240px;
  height: 40px;
  padding-left: 16px;
  background-color: #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.header .header-menu .search-input input {
  background-color: transparent;
  border: none;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
}
.header .header-menu .search-input input:focus {
  outline: none;
}
.header .header-menu .information {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
  padding: 16px;
}
.header .header-menu .information img {
  width: 40px;
  height: 40px;
}

.creator-side-nav {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  flex-shrink: 0;
}
.creator-side-nav .creator-side-bar {
  padding: 0;
  margin: 0;
}
.creator-side-nav .creator-side-bar .side-nav-dashboard {
  padding: 10px;
}
.creator-side-nav .creator-side-bar .side-nav-dashboard p {
  font-size: 12px;
  color: #959fa3;
  font-size: 12px;
  font-style: normal;
  font-weight: 500;
  line-height: 100%;
  padding: 10px 24px;
}
.creator-side-nav .creator-side-bar .side-nav-dashboard .nav-profile {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.creator-side-nav .creator-side-bar .side-nav-dashboard .nav-profile .profile {
  display: flex;
  align-items: center;
  gap: 15px;
}
.creator-side-nav .creator-side-bar .side-nav-categories {
  margin: 10px;
}
.creator-side-nav .creator-side-bar .side-nav-categories p {
  font-size: 12px;
  color: #959fa3;
  font-size: 12px;
  font-style: normal;
  font-weight: 500;
  line-height: 100%;
  padding: 10px 24px;
}
.creator-side-nav .creator-side-bar ul {
  padding-left: 0;
}
.creator-side-nav .creator-side-bar ul li {
  list-style: none;
}
.creator-side-nav .creator-side-bar ul li a {
  width: 100%;
  padding: 10px 24px;
  display: flex;
  align-items: center;
  gap: 12px;
  color: #626c70;
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: 20px;
}
.creator-side-nav .creator-side-bar ul li a:hover {
  background-color: rgba(195, 195, 195, 0.1411764706);
}
.creator-side-nav .creator-side-bar ul li .active-tab {
  color: #191b1c;
  background: #f0f6ff;
  box-shadow: 3px 0px 0px 0px #0e5fd9 inset;
}
.creator-side-nav .creator-side-bar ul li .logout-btn, .creator-side-nav .creator-side-bar ul li .logout-btn:hover {
  background-color: rgb(236, 93, 71);
  color: white;
  margin: 10px 0;
  border-radius: 5px;
}

.nav-speaker-header {
  margin-top: 20px;
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 5px 5px 5px #bebebe;
}
.nav-speaker-header .add-speaker-btn {
  font-weight: 600;
  padding: 5px 15px;
  background-color: #673de6;
  color: #fff;
  border: 1px solid #673de6;
  border-radius: 5px;
}
.nav-speaker-header .add-speaker-btn:hover {
  background-color: transparent;
  color: #673de6;
}

.nav-speaker-icon {
  margin: 20px;
  display: flex;
  align-items: center;
  gap: 30px;
}
.nav-speaker-icon .active-icon {
  cursor: pointer;
  width: 30px;
  height: 30px;
  color: #673de6;
}
.nav-speaker-icon .icon {
  cursor: pointer;
  width: 30px;
  height: 30px;
  color: #808080;
}

.custom-card .nav-speaker-card {
  padding: 20px;
  box-shadow: 0 0 5px #808080;
  border-radius: 10px;
}
.custom-card .nav-speaker-card .icon {
  cursor: pointer;
  margin-right: 5px;
}

.nav-sponsor-header {
  margin-top: 20px;
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 5px 5px 5px #bebebe;
}
.nav-sponsor-header .add-sponsor-btn {
  font-weight: 600;
  padding: 5px 15px;
  background-color: #673de6;
  color: #fff;
  border: 1px solid #673de6;
  border-radius: 5px;
}
.nav-sponsor-header .add-sponsor-btn:hover {
  background-color: transparent;
  color: #673de6;
}

.nav-sponsor-icon {
  margin: 20px;
  display: flex;
  align-items: center;
  gap: 30px;
}
.nav-sponsor-icon .active-icon {
  cursor: pointer;
  width: 30px;
  height: 30px;
  color: #673de6;
}
.nav-sponsor-icon .icon {
  cursor: pointer;
  width: 30px;
  height: 30px;
  color: #808080;
}

.custom-card .nav-sponsor-card {
  padding: 20px;
  box-shadow: 0 0 5px #808080;
  border-radius: 10px;
}
.custom-card .nav-sponsor-card .icon {
  cursor: pointer;
  margin-right: 5px;
}/*# sourceMappingURL=creator.css.map */
import { useState } from "react";
import { Collapse } from "reactstrap";
import { FaCircle } from "react-icons/fa";
import { VscTriangleRight } from "react-icons/vsc";
import { RiPencilRulerLine } from "react-icons/ri";
import { FaRegClock } from "react-icons/fa6";
import { RiDeleteBinLine } from "react-icons/ri";
import { LuPen } from "react-icons/lu";
import { FaRegEdit } from "react-icons/fa";
import { TiArrowSortedDown } from "react-icons/ti";


const AppointmentListRow = ({
  appointment, saveMeetingNotes, handleDeleteAppointment
}) => {

  const [open, setOpen] = useState("");
  const [showNoteMeeting, setShowNoteMeeting] = useState(false);
  const [meetingNote, setMeetingNote] = useState("");
  const [editNote, setEditNote] = useState(false);

  const handleCollapse = (id) => {
    if (open === id) {
      setOpen("");
    } else {
      setOpen(id);
    }
  };

  const handleAddMeetingNotes = () => {
    setShowNoteMeeting(!showNoteMeeting);
  };

  const handleEditNote = (remark) => {
    setEditNote(true);
    setMeetingNote(remark);
  };

  return (
    <>
      <div>
        <div
          className={`row mx-0 d-flex justify-content-center ${appointment._id !== open
            ? "border-bottom"
            : "border-none"
            }`}
        >

          <div className="col-12 pt-4 pb-3 px-4 header-1-detailpage">
            <div className="row mx-0">
              <div className="col-4 header-2-detailpage">
                <FaCircle className="circle-icon" />
                <span className="detailpage-time">
                  {appointment.date}
                </span>
                <span className="detailpage-time ml-3">
                  <FaRegClock className="mr-1 clock-icon" />
                  {appointment.startTime}
                </span>
              </div>
              <div className="col-6 d-flex justify-content-left">
                <span className="ml-3 text-left">{appointment?.service}</span>
              </div>
              <div className="col-2">
                <span
                  className="detailpage-detail mx-2"
                  onClick={() =>
                    handleCollapse(appointment._id)
                  }
                  aria-controls="example-collapse-text"
                  aria-expanded={open}
                >
                  Details
                  {!open ? (
                    <VscTriangleRight className="ml-2" />
                  ) : (
                    <TiArrowSortedDown className="ml-2 arrow-down" />
                  )}
                </span>
              </div>
            </div>
          </div>
        </div>
        <Collapse isOpen={appointment._id === open}>
          <div className="row mx-0 border-bottom pb-5 d-flex justify-content-center detailpage-collapse-section">
            <div className="col-8 px-md-5 px-2">
              <LuPen className="pen-icon" />
              <div className="row mx-0">
                <div className="col-4 px-0">Email</div>
                <div className="col-8 px-0 text-secondary">{appointment.email}</div>
              </div>
              <div className="row mx-0">
                <div className="col-4 px-0">Name</div>
                <div className="col-8 px-0 text-secondary">{appointment.fullName}</div>
              </div>
              <div className="row mx-0">
                <div className="col-4 px-0">Phone</div>
                <div className="col-8 px-0 text-secondary">{appointment.phone}</div>
              </div>
              <div className="row mx-0">
                <div className="col-4 px-0">Message</div>
                <div className="col-8 px-0 text-secondary"><p>{appointment.message}</p></div>
              </div>
              <p>{appointment?.user?.userName}</p>
              <div className="mt-4">
                <h6>LOCATION</h6>
                <p>This is a {appointment.platform}</p>
              </div>
              <div className="detailpage-note my-4">
                {!appointment.remark.length && (
                  <span
                    className="note-space"
                    onClick={handleAddMeetingNotes}
                  >
                    <RiPencilRulerLine className="mr-2" />
                    Add meeting notes
                  </span>
                )}
                {(showNoteMeeting ||
                  appointment.remark.length) && (
                    <div className="">
                      <h6>
                        MEETING NOTES{" "}
                        <FaRegEdit
                          className="ml-2"
                          onClick={() =>
                            handleEditNote(
                              appointment.remark
                            )
                          }
                        />
                      </h6>
                      {!appointment.remark.length ||
                        editNote ? (
                        <textarea
                          className="detailpage-text-field pt-2 pl-2"
                          name="note"
                          cols="30"
                          rows="10"
                          placeholder="Add Meeting Notes"
                          value={meetingNote}
                          onChange={(e) =>
                            setMeetingNote(e.target.value)
                          }
                          onBlur={() =>
                            saveMeetingNotes(
                              appointment._id
                            )
                          }
                        />
                      ) : (
                        <span>{appointment.remark}</span>
                      )}
                    </div>
                  )}
              </div>
              <span className="">
                Created required from backend
              </span>
            </div>
            <div className="col-4 d-flex justify-content-end align-items-center pr-4 pb-5">
              <div>
                <button className="join-button d-block">
                  Join Now
                </button>
                <button className="cancel-button d-block mt-4" onClick={() => handleDeleteAppointment(appointment._id)}>
                  <RiDeleteBinLine className="delete-icon mr-2" />
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </Collapse>
      </div>
    </>
  )
}

export default AppointmentListRow
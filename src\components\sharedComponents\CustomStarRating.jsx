import React, { useState } from 'react';
import PropTypes from 'prop-types'
import { Rating } from 'react-simple-star-rating'

const CustomStarRating = ({initialValue = '0',readonly=true , onRatingChange}) => {
  const [rating, setRating] = useState(0)

  // Catch Rating value
  const handleRating = (rate) => {
    setRating(rate)
    if (onRatingChange) {
      onRatingChange(rate);
    }
    // other logic
  }
  // Optinal callback functions
  const onPointerEnter = () => console.log('Enter')
  const onPointerLeave = () => console.log('Leave')
  const onPointerMove = (value, index) => console.log(value, index)
  console.log("initialvalue" ,initialValue)

  return (
    <div className='App'>
      <Rating
        onClick={handleRating}
        onPointerEnter={onPointerEnter}
        onPointerLeave={onPointerLeave}
        onPointerMove={onPointerMove}
        initialValue={initialValue}
        readonly={readonly}
      /* Available Props */
      />
    </div>
  )
}

CustomStarRating.propTypes = {
  initialValue: PropTypes.string,
  readonly: PropTypes.bool,
  onRatingChange: PropTypes.func,
}

export default CustomStarRating
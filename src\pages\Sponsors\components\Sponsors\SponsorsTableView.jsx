import BootstrapTable from 'react-bootstrap-table-next'

import { getColumns } from './helpers'

const SponsorsTableView = ({
  handleDeleteSponsor,
  handleEditSponsor,
  sponsorsList,
}) => {

  let columns = getColumns(handleEditSponsor, handleDeleteSponsor)

  return (
    <>
      <h1>Sponsors Table</h1>
      {sponsorsList?.length ?
        <div className="table table-responsive">
          <BootstrapTable
            keyField='id'
            bordered={false}
            data={sponsorsList}
            columns={columns}
          />
        </div>
        :
        <span>No Sponsor Available</span>
      }
    </>
  )
}

export default SponsorsTableView
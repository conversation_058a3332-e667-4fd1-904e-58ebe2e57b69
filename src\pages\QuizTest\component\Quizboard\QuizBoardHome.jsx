//import { useSelector } from "react-redux";

import QuizBoardCards from "./QuizBoardCards.jsx";
import QuizTestDetails from "./QuizTestDetails.jsx";


const QuizBoardHome = () => {
  
 // const { quizTestsList } = useSelector((state) => state.quizTest);
 // const { quizTestsListLoading } = useSelector((state) => state.quizTest);
  return (
    <>
      <div className="container-fluid px-0 m-0 quiz-board">
        <div className="row m-0 p-0 quiz-main-section ">
          <QuizBoardCards />
          <QuizTestDetails />
        </div>
      </div>
    </>
  )
};

export default QuizBoardHome;

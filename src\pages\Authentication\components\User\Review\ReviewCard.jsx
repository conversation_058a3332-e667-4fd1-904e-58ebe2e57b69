import { DownOutlined } from '@ant-design/icons';
import { Dropdown, Space } from "antd";

import CustomStarRating from "../../../../../components/sharedComponents/CustomStarRating"
import { FaEllipsisV } from 'react-icons/fa';


const ReviewCard = ({review, items, selectedReview}) => {
 
    return (     
       <>
       <div className="card-body p-3">

        <div className="d-flex my-2 justify-content-between"><span>{review?.user?.firstName} </span>
        <span>
        <Dropdown
        menu={{ items }}
        trigger={'click'}
        key={`actions-${review._id}`}
      >
        <div  onClick={(e) => {
            console.log(selectedReview.current)
            selectedReview.current = review;
          }}>
        <FaEllipsisV /> 
        </div>
      </Dropdown>
      </span></div>
        <div className="d-flex"><span>Category : {review?.category}</span> </div>
        <div className="d-flex my-2">Review : {review?.review}</div>
        <div className="d-flex my-2"><CustomStarRating initialValue={review?.rating} readonly={true}/></div>
        </div>
      </>
    )
  }
  
  export default ReviewCard
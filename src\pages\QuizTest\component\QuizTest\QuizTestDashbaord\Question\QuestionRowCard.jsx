import { useState } from "react";
import { useDispatch } from "react-redux";
import { useParams } from "react-router-dom";
import { Dropdown, Menu, Modal } from "antd";
import { PiDotsThreeOutlineVerticalFill } from "react-icons/pi";
import Parse from "html-react-parser";

import QuestionViewDrawer from "./Drawers/QuestionViewDrawer";
import {
  deleteQuizQuestion,
  getQuizQuestionsList,
} from "../../../../actions/operations";

const QuestionRowCard = ({ item, index }) => {

  const dispatch = useDispatch();
  const { id } = useParams();
  
  const [open, setOpen] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [selectedItemId, setSelectedItemId] = useState(null);

  const showDeleteConfirm = (quizid) => {
    setSelectedItemId(quizid);
    setIsModalVisible(true);
  };

  const handleDelete = () => {
    dispatch(deleteQuizQuestion(selectedItemId)).then((res) => {
      if (res.success) {
        dispatch(getQuizQuestionsList(id));
        setIsModalVisible(false);
      }
    });
  };

  const handleCancel = () => {
    setIsModalVisible(false);
  };

  const handleView = () => {
    setOpen(true);
  };

  const menu = (
    <Menu>
      <Menu.Item key='view' onClick={handleView}>
        View
      </Menu.Item>
      <Menu.Item
        key='delete'
        danger
        onClick={() => showDeleteConfirm(item._id)}
      >
        Delete
      </Menu.Item>
    </Menu>
  );

  const levelClass = (level) => {
    switch (level) {
      case "easy":
        return "level-badge  level-easy";
      case "medium":
        return "level-badge  level-medium";
      case "hard":
        return "level-badge  level-hard";
      default:
        return "level-badge  level-default";
    }
  };
  return (
    <>
      <div className='row mx-0 p-3 question-card'>
        <div className='col-12 d-flex justify-content-between align-items-center pb-3'>
          <div>
            <h6 className='mr-2'>{`0${index + 1}`}</h6>
            {item?.title && Parse(item?.title)}
          </div>
          <div>
            <Dropdown overlay={menu} trigger={["click"]}>
              <PiDotsThreeOutlineVerticalFill
                style={{ fontSize: "24px", cursor: "pointer" }}
              />
            </Dropdown>
          </div>
        </div>
        <div className='col-12 d-flex justify-content-between pb-3'>
          <div className='d-flex align-items-center'>
            <span className='text-muted mr-2'>Level:</span>
            <span className={levelClass(item?.level)}>
              {item?.level ?? "Easy"}
            </span>
          </div>
          <span className='score-badge'>Score: {item?.maxScore ?? 0}</span>
        </div>
      </div>

      <QuestionViewDrawer open={open} setOpen={setOpen} questionId={item._id} />

      <Modal
        title='Confirm Deletion'
        visible={isModalVisible}
        onOk={handleDelete}
        onCancel={handleCancel}
        okText='Yes'
        cancelText='No'
      >
        <p>Are you sure you want to delete this question?</p>
      </Modal>
    </>
  );
};

export default QuestionRowCard;

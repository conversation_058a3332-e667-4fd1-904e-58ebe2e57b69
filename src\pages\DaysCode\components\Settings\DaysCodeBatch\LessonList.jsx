import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useParams } from "react-router-dom";

import ProblemListCard from "../Problems/ProblemsList/ProblemListCard";
import {
  deleteLessonFromBatch,
  editLessonIntoBatch,
  getBatchLessons,
} from "../../../actions";
import CustomLoader from "../../../../../components/sharedComponents/CustomLoader";
import BatchContentHeader from "./BatchContentHeader";
import NoDataBlock from "../../../../../components/sharedComponents/NoDataBlock";
import AddBatchLessonDrawer from "./AddBatchLessonDrawer";

const LessonList = ({
  batch,
  codeBatchDetails,
  codeBatchesLoading,
  setActiveTab,
}) => {

  const dispatch = useDispatch();
  const { id } = useParams();

  const { batchLessonList, problemsListLoading } = useSelector(
    ({ dayscode }) => dayscode,
  );

  const [lessonStatus, setLessonStatus] = useState(null);
  const [open, setOpen] = useState(false);
  const [openAddDay, setOpenAddDay] = useState("");

  useEffect(() => {
    dispatch(
      getBatchLessons({
        id: id,
        status: lessonStatus,
      }),
    );
  }, [lessonStatus ,id , dispatch]);

  const showDrawer = () => {
    setOpen(true);
  };
  const onClose = () => {
    setOpen(false);
    setOpenAddDay("");
  };

  const handleDeleteLessonFromBatch = (lesson) => {
    dispatch(deleteLessonFromBatch({ batch: batch, lesson }));
  };
  const handleEditLessonIntoBatch = (contentData) => {
    dispatch(editLessonIntoBatch(contentData));
  };

  return (
    <>
      <BatchContentHeader
        batch={batch}
        type={"lesson"}
        setBatchContentStatus={setLessonStatus}
        setActiveTab={setActiveTab}
        open={open}
        setOpen={setOpen}
        showDrawer={showDrawer}
        setOpenAddDay={setOpenAddDay}
        openAddDay={openAddDay}
        onClose={onClose}
      />
      {problemsListLoading ? (
        <div className='row d-flex justify-items                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            -center'>
          <div className='col-12 align-items-center text-center '>
            <CustomLoader />
          </div>
        </div>
      ) : (
        <div className='row mx-4 mt-3 border-top'>
          {batchLessonList && batchLessonList.length > 0 ? (
            batchLessonList.map((lesson, key) => (
              <div className='col-md-4 col-12 p-2' key={key}>
                <ProblemListCard
                  batch={batch}
                  day={lesson?.day}
                  batchContent={lesson}
                  item={lesson.lessonDetails}
                  showDeleteFromBatchIcon={true}
                  handleDeleteFromBatch={handleDeleteLessonFromBatch}
                  handleAddIntoBatch={handleEditLessonIntoBatch}
                  type={"lesson"}
                />
              </div>
            ))
          ) : (
            <div className='w-100 h-100'>
              <NoDataBlock
                route={
                  <AddBatchLessonDrawer
                    batch={batch}
                    open={open}
                    setOpen={setOpen}
                    showDrawer={showDrawer}
                    setOpenAddDay={setOpenAddDay}
                    openAddDay={openAddDay}
                    onClose={onClose}
                  />
                }
              />
            </div>
          )}
        </div>
      )}
    </>
  );
};

export default LessonList;

import { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import {
  DropdownItem,
  DropdownMenu,
  DropdownToggle,
  UncontrolledDropdown,
} from "reactstrap";
import {
  FaArrowDown,
  FaArrowUp,
  FaEllipsisV,
} from "react-icons/fa";
import { BiSolidRightArrow } from "react-icons/bi";

import ToolTip from "../../../../../../components/sharedComponents/ToolTip";
import { handleListOrder } from "../../../../../../components/utils";
import { editEvent } from "../../../../actions";


const EventTimelineCard = ({ timeline, handleEventTimelineDelete ,eventId,eventDetails}) => {

  const dispatch= useDispatch();

  const [contentsCount,setContentsCount] = useState();
  const index = eventDetails.timelines.indexOf(timeline);

  useEffect(()=>{
    if(eventDetails){
    setContentsCount(eventDetails.timelines.length)}
    console.log(eventDetails.timelines.length)
    console.log(eventDetails)
  },[eventDetails])

  const handleContentOrder = (index, signal) => {
    if (eventDetails.timelines) {
      const eventTimelines = eventDetails.timelines;
      let orderedContents = handleListOrder(eventTimelines, index, signal);
      dispatch(editEvent({_id:eventDetails._id , timelines:orderedContents}));
    }
  };
  
  return (
    <>
      <div className="row mx-0 timeline-card">
        <div className="col-10 main-card">
          <div className="title">
            <div className="timeline-delete">
              <h4>{timeline.title}</h4>
              <UncontrolledDropdown
                setActiveFromChild
                className="event-host-action-toggle"
              >
                 <DropdownToggle tag="a">
                <ToolTip title="Edit" placement="bottom">
                  <FaEllipsisV />
                </ToolTip>
              </DropdownToggle>
                <DropdownMenu
                  style={{ margin: 0 }}
                  className="dropdown-menu mt-3 card-schadow"
                  left={"true"}
                >
                  <DropdownItem header>
                    <span>Host Actions</span>
                  </DropdownItem>
                  <DropdownItem>
                    <span>
                      <i className="fal fa-pencil mr-2" />
                      Edit Event
                    </span>
                  </DropdownItem>
                  <DropdownItem
                    onClick={() => handleEventTimelineDelete(timeline._id)}
                  >
                    <span>
                      <i className="fal fa-trash mr-2" />
                      Delete Event
                    </span>
                  </DropdownItem>
                  {contentsCount > 1 &&
                  (index > 0 && index < contentsCount - 1 ? (
                    <>
                      <DropdownItem
                        onClick={() => handleContentOrder(index, 1)}
                      >
                        <FaArrowUp className="mr-2" />
                        Move up
                      </DropdownItem>
                      <DropdownItem
                        onClick={() => handleContentOrder(index, -1)}
                      >
                        <FaArrowDown className="mr-2" />
                        Move down
                      </DropdownItem>
                    </>
                  ) : index === 0 ? (
                    <DropdownItem onClick={() => handleContentOrder(index, -1)}>
                      <FaArrowDown className="mr-2" />
                      Move down
                    </DropdownItem>
                  ) : (
                    <DropdownItem onClick={() => handleContentOrder(index, 1)}>
                      <FaArrowUp />
                      Move up
                    </DropdownItem>
                  ))}
                </DropdownMenu>
              </UncontrolledDropdown>
            </div>
            <h6>{timeline.description}</h6>
          </div>
          <div className="mid-style">
            <BiSolidRightArrow />
            <div className="vertical-line"></div>
          </div>
          <div className="date-time">
            <div className="date">
              <p>{timeline.start_date}</p>
              <p>{timeline.end_date}</p>
            </div>
            <div className="time">
              <p>{timeline.start_time}</p>
              <p>{timeline.end_time}</p>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default EventTimelineCard;

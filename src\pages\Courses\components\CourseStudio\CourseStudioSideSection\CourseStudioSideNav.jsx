import React from "react";
import { FaFileAlt, FaShapes, FaCog, FaArrowAltCircleLeft } from "react-icons/fa";
import { useNavigate } from "react-router";

import ToolTip from "../../../../../components/sharedComponents/ToolTip";

const CourseStudioSideNav = ({ setActiveTab, activeTab, type }) => {
  const navigate = useNavigate()

  return (
    <>
      <ul className="course-studio-side-nav mb-0">
        {type === "course" && (
          <ToolTip title="Add Section" placement="right">
            <li
              onClick={() => setActiveTab("lessons")}
              className={activeTab === "lessons" ? "active" : ""}
            >
              <FaFileAlt />
            </li>
          </ToolTip>
        )}
        <ToolTip title="Back" placement="right">
          <li
            onClick={() => navigate(`/blogs`)}
            className={activeTab === "back" ? "active" : ""}
          >
            <FaArrowAltCircleLeft />
          </li>
        </ToolTip>
        <ToolTip title="Choose Blocks" placement="right">
          <li
            onClick={() => setActiveTab("blocks")}
            className={activeTab === "blocks" ? "active" : ""}
          >
            <FaShapes />
          </li>
        </ToolTip>
        <ToolTip title="Edit Course Info" placement="right">
          <li
            onClick={() => setActiveTab("settings")}
            className={activeTab === "settings" ? "active" : ""}
          >
            <FaCog />
          </li>
        </ToolTip>
      </ul>
    </>
  );
};

export default CourseStudioSideNav;

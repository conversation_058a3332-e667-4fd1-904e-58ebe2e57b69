export const FaqBlockFormFields = ({ formik }) => {
  return (
    <>
      <div className="row mx-0">
        <div className="col-md-6 col-12">
          <div className="form-group">
            <label htmlFor="heading">Heading</label>
            <input
              type="text"
              name="heading"
              onChange={formik.handleChange}
              value={formik.values.heading}
              className="form-control"
              id="heading"
              placeholder="Enter heading"
            />
          </div>
        </div>
        <div className="col-md-6 col-12">
          <div className="form-group">
            <label htmlFor="description">Description</label>
            <textarea
              name="description"
              onChange={formik.handleChange}
              value={formik.values.description}
              className="form-control"
              id="description"
              placeholder="Enter description"
              rows="1"
            />
          </div>
        </div>
      </div>
    </>
  );
};

// hero section block -  community page
export const HeroSectionBlockFormFeilds = ({ formik }) => {
  return (
    <>
      <div className="row mx-0">
        {/* heading */}
        <div className="col-md-4 col-12">
          <div className="form-group">
            <label htmlFor="heading">Heading</label>
            <input
              type="text"
              name="heading"
              onChange={formik.handleChange}
              value={formik.values.heading}
              className="form-control"
              id="heading"
              placeholder="Enter Heading"
            />
          </div>
        </div>
        {/* description */}
        <div className="col-md-4 col-12">
          <div className="form-group">
            <label htmlFor="description">Description</label>
            <textarea
              name="description"
              onChange={formik.handleChange}
              value={formik.values.description}
              className="form-control"
              id="description"
              placeholder="Enter description"
              rows="1"
            />
          </div>
        </div>
        {/* cta title */}
        <div className="col-md-4 col-12">
          <div className="form-group">
            <label htmlFor="cta">CTA Title</label>
            <input
              type="text"
              name="cta"
              onChange={formik.handleChange}
              value={formik.values.cta}
              className="form-control"
              id="cta"
              placeholder="Enter CTA Title"
            />
          </div>
        </div>
      </div>
    </>
  );
};

export const AboutUsBlockFormFields = ({ formik }) => {
  return (
    <>
      <div className="row mx-0">
        <div className="col-md-6 col-12">
          <div className="form-group">
            <label htmlFor="heading1">Heading1</label>
            <input
              type="text"
              name="heading1"
              onChange={formik.handleChange}
              value={formik.values.heading1}
              className="form-control"
              id="heading1"
              placeholder="Enter heading"
            />
          </div>
        </div>
        <div className="col-md-6 col-12">
          <div className="form-group">
            <label htmlFor="description1">Description1</label>
            <textarea
              name="description1"
              onChange={formik.handleChange}
              value={formik.values.description1}
              className="form-control"
              id="description1"
              placeholder="Enter description"
              rows="1"
            />
          </div>
        </div>
      </div>
      <div className="row mx-0">
        <div className="col-md-6 col-12">
          <div className="form-group">
            <label htmlFor="heading2">Heading2</label>
            <input
              type="text"
              name="heading2"
              onChange={formik.handleChange}
              value={formik.values.heading2}
              className="form-control"
              id="heading2"
              placeholder="Enter heading"
            />
          </div>
        </div>
        <div className="col-md-6 col-12">
          <div className="form-group">
            <label htmlFor="description2">Description2</label>
            <textarea
              name="description"
              onChange={formik.handleChange}
              value={formik.values.description2}
              className="form-control"
              id="description2"
              placeholder="Enter description2"
              rows="1"
            />
          </div>
        </div>
      </div>
    </>
  );
};

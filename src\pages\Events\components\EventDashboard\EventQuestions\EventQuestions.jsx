import React, { useEffect, useState } from 'react'
import BootstrapTable from 'react-bootstrap-table-next'
import { useParams } from 'react-router'
import { useDispatch, useSelector } from 'react-redux'

import { getColumns } from './helpers'
import { getEventDetails } from '../../../actions'

const EventQuestions = () => {

  const dispatch = useDispatch();
  const {id} = useParams()

  const { eventDetails} = useSelector((state) => state.event) || {}

  const [questionsData, setQuestionsData] = useState()
  let columns = getColumns()

  useEffect(() => {
    dispatch(getEventDetails(id))
  }, [dispatch , id]);
  


  useEffect(() => {
    if (eventDetails) {
      setQuestionsData(eventDetails.questions)
    }
  },[eventDetails])
  
  return (
    <>
      <h1>Question Details</h1>
      {questionsData && questionsData.length &&
        <div className="table table-responsive">
          <>
            <h1>Table</h1>
            <BootstrapTable
              keyField='id'
              bordered={true}
              data={questionsData}
              columns={columns}
              hover={true}
            />
          </>
        </div>
      }
    </>
  )
}


export default EventQuestions
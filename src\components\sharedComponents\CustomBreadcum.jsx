import React from "react";
import { Link } from "react-router-dom";

const CustomBreadcumb = ({ items }) => {
  return (
    <div className='custom-breadcum'>
      {items.map((item, index) => (
        <span key={item.title}>
          {item.href ? (
            <Link to={item.href}>
              <span>{item.title} </span>
            </Link>
          ) : (
            <span onClick={item.onClick}>{item.title} </span>
          )}
          {index < items.length - 1 && <span> / </span>}
        </span>
      ))}
    </div>
  );
};

export default CustomBreadcumb;

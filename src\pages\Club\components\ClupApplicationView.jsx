import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Link, useParams } from "react-router-dom";
import moment from "moment";
import Combobox from "react-widgets/Combobox";

import { deleteClubApplication, getMemberClubApplication, editClubApplication } from "../actions";
import StatusUpdateModal from "./StatusUpdateModal";
import StatusBadge from "../../../components/sharedComponents/StatusBadge";
import CustomLoader from "../../../components/sharedComponents/CustomLoader";
import { handlePermissionOption } from "./helpers";
import { statusOptions } from "../../../components/utils/selectOptions";
import usePermissions from "../../../hooks/userPermission";

const ClupApplicationView = () => {
  
  const dispatch = useDispatch()
  let { id } = useParams();
  const { hasPermission } = usePermissions()

  const { clubApplication, clubApplicationLoading } = useSelector((state) => state.club) || {};

  const [openModal, setOpenModal] = useState(false)
  const [tempStatus, setTempStatus] = useState()

  useEffect(() => {
    if (id) {
      dispatch(getMemberClubApplication(id))
    }
  }, [id])

  const toggleModal = () => {
    setOpenModal(!openModal)
  }

  const handleStatus = (value, application) => {
    setTempStatus({ status: value.status, _id: application._id })
    setOpenModal(!openModal)
  };

  const handleUpdateStatusModal = (data) => {
    dispatch(editClubApplication({ _id: data._id, status: data.status })).then((res) => {
      if (res.success) {
        dispatch(getMemberClubApplication(id))
        toggleModal()
      }
    })
  }

  const handleUserDeleteAction = (id) => {
    dispatch(deleteClubApplication(id)).then((res) => {
      if (res) {
        dispatch(getMemberClubApplication(id))
      }
    })
  };

  const tableAction = (clubApplication) => {
    return (
      <div className='text-left d-flex application-table'>
        {/* <i type="button" className="mr-2 fas fa-user-check"
          onClick={() => handleStatus(row._id, 'Approve')}
        />
        <i type="button" className="mr-2 fas fa-user-times"
          onClick={() => handleStatus(row._id, 'Reject')}
        /> */}
        <Combobox
          data={handlePermissionOption(statusOptions, hasPermission)}
          dataKey={"value"}
          textField='status'
          placeholder={"Select Status"}
          value={{ status: clubApplication.status }}
          onChange={(value) => handleStatus(value, clubApplication)}
        />
      </div>
    )
  }

  return (
    <>
      <div className="row mx-0 border-bottom shadow-sm">
        <div className="col-8  d-flex align-items-center">
          <Link to="/admin/club/application"><span className="">Back</span></Link>
          <h2 className="p-3 mb-0">{clubApplication?.name}| Club Application</h2>
          <StatusBadge status={clubApplication.status} />
        </div>
        <div className="col-4 d-flex justify-content-end align-items-center">
          <div className="mr-2">{tableAction(clubApplication)}</div>
          <div className="btn btn-danger">Delete Application</div>
        </div>
      </div>
      {
        clubApplicationLoading ?
          <CustomLoader />
          :
          clubApplication ?
            <div className="row mx-0 d-flex justify-content-center">
              <div className="col-2 pt-5 text-center">
                <div className="btn btn-primary">Prev</div>
              </div>
              <div className="col-8 px-0 shadow p-5">
                <div className="block border rounded-lg p-4 my-3">
                  <h5 className="border-bottom mb-3">Personal Information</h5>
                  <div className="row">
                    <div className="col-12 col-md-3">
                      <h6>Name:</h6>
                      <p>{clubApplication.name}</p>
                    </div>
                    <div className="col-12 col-md-3">
                      <h6>Email:</h6>
                      <p>{clubApplication.email}</p>
                    </div>
                    <div className="col-12 col-md-3">
                      <h6>Phone:</h6>
                      <p>{clubApplication.phone}</p>
                    </div>
                    <div className="col-12 col-md-3">
                      <h6>Gender:</h6>
                      <p>{clubApplication.gender}</p>
                    </div>
                    <div className="col-12 col-md-3">
                      <h6>Location:</h6>
                      <p>{clubApplication.location}</p>
                    </div>
                    <div className="col-12 col-md-3">
                      <h6>Application Date:</h6>
                      <p><span className="bg-primary text-white px-3">
                        {clubApplication.createdAt === null
                          ? "Invalid date"
                          : moment(clubApplication.createdAt).format("LLL")}
                      </span></p>
                    </div>
                  </div>
                </div>
                <div className="block border rounded-lg p-4 my-3">
                  <h5 className="border-bottom mb-3">Academic Details</h5>
                  <div className="row">
                    <div className="col-12 col-md-3">
                      <h6>University / Institution:</h6>
                      <p>{clubApplication.university}</p>
                    </div>
                    <div className="col-12 col-md-3">
                      <h6>Degree Program:</h6>
                      <p>{clubApplication.degree}</p>
                    </div>
                    <div className="col-12 col-md-3">
                      <h6>Graduation Year:</h6>
                      <p>{clubApplication.graduation_year}</p>
                    </div>
                    <div className="col-12 col-md-3">
                      <h6>Year of Study:</h6>
                      <p>{clubApplication.year}</p>
                    </div>
                  </div>
                </div>
                <div className="block border rounded-lg p-4 my-3">
                  <h5 className="border-bottom mb-3">Tell us More</h5>
                  <div className="row">
                    <div className="col-12">
                      <h6>Why are you interested in joining the Datacode Student Learning Club?</h6>
                      <p>{clubApplication.why_are_you_interested}</p>
                    </div>
                    <div className="col-12">
                      <h6>What specific technology or areas you interested in?:</h6>
                      <p>{clubApplication.interested_topics}</p>
                    </div>
                    <div className="col-12">
                      <h6>What are your goals or expectations from joining the Student Learning Group?</h6>
                      <p>{clubApplication.goals_expectations}</p>
                    </div>
                    <div className="col-12">
                      <h6>Have you participated in any other student clubs or organizations related to computer science?</h6>
                      <p>{clubApplication.participated_before}</p>
                    </div>
                  </div>
                </div>
                <div className="block border rounded-lg p-4 my-3">
                  <h5 className="border-bottom mb-3">Contribution</h5>
                  <div className="row">
                    <div className="col-12">
                      <h6>Are you interested in participating in workshops, guest lectures, or other interactive events?, Let us know what do you want to join</h6>
                      <p>{clubApplication.interest}</p>
                    </div>
                    <div className="col-12">
                      <h6>How would you like to contribute to the group by sharing your knowledge, organizing events, or mentoring other members?</h6>
                      <p>{clubApplication.shared_ideas}</p>
                    </div>
                    <div className="col-12">
                      <h6>Do you have any suggestions or ideas for activities or topics you'd like to see in the group?</h6>
                      <p>{clubApplication.suggestions}</p>
                    </div>
                  </div>
                </div>
              </div>
              <div className="col-2 pt-5 text-center">
                <div className="btn btn-primary">Next</div>
              </div>
            </div>
            :
            <h1>Application Not Found</h1>
      }
      <StatusUpdateModal
        open={openModal}
        toggle={toggleModal}
        onSubmit={(e) => handleUpdateStatusModal(e)}
        tempStatus={tempStatus}
      />
    </>
  )
}
export default ClupApplicationView;

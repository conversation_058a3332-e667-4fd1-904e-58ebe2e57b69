import { withErrorBoundary } from 'react-error-boundary';
import ErrorFallback from '../components/sharedComponents/ErrorFallback';

function logErrorToService(error, info) {
  // Log the error to an error logging service
  console.error("Logging error:", error, info);
}

function withErrorBoundaryHOC(WrappedComponent) {
  return withErrorBoundary(WrappedComponent, {
    FallbackComponent: ErrorFallback,
    onError: logErrorToService,
  });
}

export default withErrorBoundaryHOC;

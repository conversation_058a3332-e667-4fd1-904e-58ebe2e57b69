import React, { useState } from "react";
import { useDispatch } from "react-redux";
import { useParams } from "react-router";
import {
  UncontrolledDropdown,
  DropdownToggle,
  DropdownMenu,
  DropdownItem,
} from "reactstrap";

import InputDoubleClickElement from "../../../../../../components/services/InputDoubleClickElement";
import {
  createLesson,
  editSection,
  getLessonDetails,
  getSectionDetails,
} from "../../../../actions/operations";
import { FaArrowUp, FaEllipsisV, FaPlus, FaTrash } from "react-icons/fa";
import { FaArrowDown } from "react-icons/fa";
import { IoChevronDownSharp } from "react-icons/io5";
import { GoChevronRight } from "react-icons/go";

const Section = ({
  index,
  section,
  sectionsCount,
  handleLessonCollapse,
  isOpen,
  handleSectionOrder,
  handleSectionDelete,
}) => {
  const dispatch = useDispatch();
  const { id } = useParams();

  const [showAction, setShowAction] = useState(false);
  const [sectionName, setSectionName] = useState(
    section.name === "" ? `Untitled Section` : section.name
  );
  const [showInputEle, setShowInputEle] = useState(false);

  const handleEditSection = () => {
    dispatch(
      editSection({ id: section._id, name: sectionName, course: id })
    ).then((res) => {
      if (res) {
        setShowInputEle(false);
      }
    });
  };

  const handleAddLesson = () => {
    const lesson = {
      course: id,
      section: section._id,
      author: process.env.REACT_APP_ADMIN_AUTHOR_ID,
      name: "",
      duration: "10" //min
    };
    dispatch(createLesson(lesson)).then((res) => {
      if (res) {
        alert("Lesson Added");
        dispatch(getLessonDetails(res.lesson._id));
        dispatch(getSectionDetails(section._id));
      }
    });
  };

  return (
    <>
      <div
        className={`row mx-0 py-2 section-title ${isOpen === section._id && "section-open"
          }`}
        onMouseEnter={() => setShowAction(true)}
        onMouseLeave={() => setShowAction(false)}
      >
        <div className="col-10 px-0 d-flex align-items-center">
          {isOpen === section._id ? (
            <div className="" onClick={() => handleLessonCollapse(section._id)}>
              <IoChevronDownSharp />
            </div>
          ) : (
            <div className="" onClick={() => handleLessonCollapse(section._id)}>
              <GoChevronRight />
            </div>
          )}
          <p className="mb-0 ml-3">
            <InputDoubleClickElement
              value={sectionName}
              handleChange={(e) => setSectionName(e.target.value)}
              handleDoubleClick={() => setShowInputEle(true)}
              handleBlur={() => handleEditSection()}
              showInputEle={showInputEle}
            />
          </p>
        </div>
        <div className="col-2 text-right">
          {showAction && (
            <UncontrolledDropdown setActiveFromChild>
              <DropdownToggle tag="a">
                <FaEllipsisV />
              </DropdownToggle>
              <DropdownMenu>
                {sectionsCount > 1 &&
                  (index > 0 && index < sectionsCount - 1 ? (
                    <>
                      <DropdownItem
                        onClick={() => handleSectionOrder(index, 1)}
                      >
                        <FaArrowUp className="mr-2" />
                        Move up
                      </DropdownItem>
                      <DropdownItem
                        onClick={() => handleSectionOrder(index, -1)}
                      >
                        <FaArrowDown className="mr-2" />
                        Move down
                      </DropdownItem>
                    </>
                  ) : index === 0 ? (
                    <DropdownItem onClick={() => handleSectionOrder(index, -1)}>
                      <FaArrowDown className="mr-2" />
                      Move down
                    </DropdownItem>
                  ) : (
                    <DropdownItem onClick={() => handleSectionOrder(index, 1)}>
                      <FaArrowUp className="mr-2" />
                      Move up
                    </DropdownItem>
                  ))}
                <DropdownItem onClick={() => handleAddLesson()}>
                  <FaPlus className="mr-2" />
                  Add a Lesson
                </DropdownItem>
                <DropdownItem onClick={() => handleSectionDelete(section._id)}>
                  <FaTrash className="mr-2" />
                  Delete
                </DropdownItem>
              </DropdownMenu>
            </UncontrolledDropdown>
          )}
        </div>
      </div>
    </>
  );
};

export default Section;

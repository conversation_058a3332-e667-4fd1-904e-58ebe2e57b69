import { Field, reduxForm } from "redux-form";
import { useDispatch, useSelector } from "react-redux";
import { useEffect } from "react";
import { required } from "../../../../../../components/utils/validators";
import {
  renderInputField,
  renderSelectField,
} from "../../../../../../components/sharedComponents/ReduxFormFields";
import {
  inviteCandidate,
  getQuizTestsList,
  getQuizTestInvitesList,
  editInviteCandidate
} from "../../../../actions/operations";

const QuizInviteForm = ({
  toggle,
  handleSubmit,
  type,
  formData,
  initialize,
}) => {
  const dispatch = useDispatch();

  const { currentQuizTest, quizTestsList } = useSelector(
    (state) => state.quizTest || {}
  );

  useEffect(() => {
    dispatch(getQuizTestsList({}));
    initialize(formData);
  }, [dispatch]);

  const quizOptions = Array.isArray(quizTestsList)
    ? quizTestsList.map((quiz) => ({
        value: quiz._id,
        label: quiz.quizTitle,
      }))
    : [];

  const handleInvite = (values) => {
    const formdata = { ...values };
    let quizInvite = formdata;

    dispatch(inviteCandidate(quizInvite))
      .then((res) => {
        if (res && res.success) {
          dispatch(getQuizTestInvitesList({}));
          toggle();
        }
      })
      .catch((error) => {
        console.error("Error inviting candidate:", error);
      });
  };

  const handleEdit = (values) => {
    const formdata = { ...values };
    let quizInvite = formdata;

    dispatch(editInviteCandidate(quizInvite))
      .then((res) => {
        if (res && res.success) {
          dispatch(getQuizTestInvitesList({}));
          toggle();
        }
      })
      .catch((error) => {
        console.error("Error updating candidate:", error);
      });
  };

  return (
    <>
      <div className="row mx-0">
        <div className="col-12">
          <form className="w-100">
            <Field
              name="quizId"
              className="form-control mb-3"
              placeholder="Select Quiz"
              component={renderSelectField}
              validate={[required]}
              textField="label"
              options={quizOptions}
              label="Select Quiz"
            />

            <Field
              name="email"
              type="text"
              className="form-control mb-3"
              placeholder="Enter Email"
              component={renderInputField}
              validate={[required]}
              label="Email"
            />
            <Field
              name="expDate"
              type="date"
              className="form-control mb-3"
              placeholder="Enter Expiry Date"
              component={renderInputField}
              validate={[required]}
              label="Expiry Date"
            />
            <div className="d-flex justify-content-end mt-3">
              {type === "edit" ? (
                <button
                  type="submit"
                  className="btn btn-primary mr-3"
                  onClick={handleSubmit(handleEdit)}
                >
                  <span>Edit</span>
                </button>
              ) : (
                <button
                  type="submit"
                  className="btn btn-primary mr-3"
                  onClick={handleSubmit(handleInvite)}
                >
                  <span>Invite</span>
                </button>
              )}
              <button
                className="btn btn-secondary"
                onClick={(e) => {
                  e.preventDefault();
                  toggle();
                }}
              >
                <span>Close</span>
              </button>
            </div>
          </form>
        </div>
      </div>
    </>
  );
};

export default reduxForm({
  form: "quizInviteForm",
})(QuizInviteForm);

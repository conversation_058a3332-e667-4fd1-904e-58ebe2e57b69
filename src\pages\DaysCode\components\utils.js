import { getSort } from "../../../components/utils";

const currentBatches = ['2'];

export const getUserAttendanceList = (user) => {
  const attendanceList = [];
  user &&
    user.attendance &&
    user.attendance.forEach((x) => {
      if (currentBatches.includes(x.batchId)) {
        attendanceList.push(x);
      }
    });

  return attendanceList;
};


export const getLeaderBoardPosition = (leaderBoard, userName) => {
  if (leaderBoard && leaderBoard.length) {
    let position = getSort(leaderBoard, 'score').reverse().findIndex(x => x.userName === userName);
    return position + 1
  }
}

export const getTopicOptions = (DSAContent) => {
  const options = [];
  DSAContent && DSAContent.forEach((item, i) => {
    options.push({ name: item.name, value: i });
  });
  return options;
};

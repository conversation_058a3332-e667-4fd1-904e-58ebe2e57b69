import React, { useState } from "react";

import CreateQuestionModal from "./created_questions_list/CreateQuestionModal";
import { ReactComponent as NoData } from "../../../../../../assets/images/svg/No_data_re_kwbl.svg";

const QuestionNodataCard = () => {
  
  const [openModal, setOpenModal] = useState(false);

  const toggleModal = () => {
    setOpenModal(!openModal);
  };

  return (
    <div className='text-center py-5'>
      <NoData className='no-data' style={{ width: "200px", height: "200px" }} />
      <h2 className='mt-3'>No Questions Available</h2>
      <p className='mt-3'>
        It looks like there are no questions in the system. You can create new
        ones.
      </p>
      {/* <div className='d-flex justify-content-center align-items-center gap-3 mt-4'>
        <button
          onClick={toggleModal}
          className='btn btn-primary'
          style={{
            padding: "10px 20px",
            borderRadius: "5px",
            fontWeight: "bold",
          }}
        >
          Create New Question
        </button>

        <span>Or</span>
        <a
          href='#'
          onClick={toggleModal}
          className='text-decoration-none text-primary'
        >
          Create a New Question
        </a>
      </div> */}

      {/* <CreateQuestionModal open={openModal} toggle={toggleModal} /> */}
    </div>
  );
};

export default QuestionNodataCard;

import { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Link } from "react-router-dom";
import { Combobox } from "react-widgets";
import { FaPlus } from "react-icons/fa6";

import { getSpeakers, setSpeakersList } from "../../../pages/Speakers/actions";
import { technologyOptions } from "../../utils/selectOptions";
import ToolTip from "../../sharedComponents/ToolTip";
import CustomLoader from "../../sharedComponents/CustomLoader";
import AddSpeakerModal from "./AddSpeakerModal";

const EventSpeakerFilter = ({ handleSelectedSpeaker }) => {

  const dispatch = useDispatch();

  const { speakersList, eventSpeakerFilterLoading } =
    useSelector(({ speaker }) => speaker) || {};

  const [isOpenModal, setIsOpenModal] = useState(false);
  const [speakerId, setSpeakerId] = useState("");

  const [filterQuery, setFilterQuery] = useState({
    search: "",
    technology: undefined,
  });

  

  useEffect(() => {
    const debounceTimeout = setTimeout(() => {
      if (
        filterQuery.search?.trim() === "" &&
        filterQuery.technology === undefined
      ) {
        dispatch(setSpeakersList({ Speakers: [] }));
      } else {
        dispatch(getSpeakers(filterQuery));
      }
    }, 1000);

    return () => clearTimeout(debounceTimeout);
  }, [filterQuery, dispatch]);

  const handleChangeFilterQuery = (event) => {
    const { name, value } = event.target;
    setFilterQuery({ ...filterQuery, [name]: value });
  };
  
  return (
    <>
      <div className='search-section'>
        <Link
          to={{
            pathname: "/speaker/new",
            search: "?type=guest",
          }}
        >
          <ToolTip title='Add guest speaker' placement='bottom'>
            <button type='button' className='add-guest'>
              Add Guest Speaker
            </button>
          </ToolTip>
        </Link>
      </div>
      <div className='row mx-0'>
        <div className='col-12 px-0'>
          <div className='row mx-0'>
            <div className='col-12'>
              <label className='form-label'>Search</label>
              <input
                className='form-control'
                type='type'
                name='search'
                value={filterQuery.search}
                onChange={(e) => handleChangeFilterQuery(e)}
              />
            </div>
            <div className='col-12 mt-3'>
              <div className='text-left form-set'>
                <label className='form-label'>Technology</label>
                <Combobox
                  data={technologyOptions}
                  dataKey={"value"}
                  textField={"tech"}
                  placeholder={"Select Technology"}
                  value={filterQuery.technology}
                  onChange={(e) =>
                    handleChangeFilterQuery({
                      target: { name: "technology", value: e.value },
                    })
                  }
                />
              </div>
            </div>
          </div>
        </div>
        <div className='col-12 px-0'>
          {eventSpeakerFilterLoading ? (
            <CustomLoader />
          ) : (
            <>
              <div className='row mx-0 event-speaker-search-table'>
                {speakersList &&
                  speakersList.map((speaker) => {
                    console.log(speaker, "speaker");
                    return (
                      <div className='col-12 row speaker-search-card '>
                        <h6 className='col-2 mb-0'>{speaker.name}</h6>
                        <p className='col-3 text-truncate'>{speaker.bio}</p>
                        <div className='col-5 mx-2'>
                          {speaker.technologies &&
                            speaker.technologies.map((item) => (
                              <span className='border badge bg-secondary text-white mx-2'>
                                {item.tech}
                              </span>
                            ))}
                        </div>
                        <FaPlus
                          className='add'
                          // onClick={() => handleSelectedSpeaker(speaker._id)}
                          onClick={() => {
                            setSpeakerId(speaker._id);
                            setIsOpenModal(true);
                          }}
                        />
                      </div>
                    );
                  })}
              </div>
            </>
          )}
        </div>
      </div>
      {isOpenModal && (
        <AddSpeakerModal
          isOpenModal={isOpenModal}
          setIsOpenModal={setIsOpenModal}
          speakerId={speakerId}
        />
      )}
    </>
  );
};

export default EventSpeakerFilter;

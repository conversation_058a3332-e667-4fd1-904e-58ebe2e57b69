import React from "react";
import CustomStarRating from "../../../components/sharedComponents/CustomStarRating";

const WorkshopCard = ({
  item
}) => {
  return (
    <>
      <div className="row mx-0">
        <div className="col-12 px-0 py-0 rounded-lg card-body">
          <div className="">
            <img
              className="img-fluid"
              loading="lazy"
              src={item.img}
              alt="avatar"
            />
          </div>
          <div className="px-3 workshopcard-body">
            <div className="my-3 d-flex justify-content-between card-body-header">
              <div>
                <img alt=""/>
                <span className="item-days">{item.days}</span>
              </div>
              <span className="item-category">
                {item.category}
              </span>
            </div>
            <div className="">
              <h1 className="p-2 item-title">{item.title}</h1>
            </div>
            <div className="d-flex justify-content-between speaker-section-border">
              <div className="d-flex justify-content-start speaker-section">
                <img
                  className="rounded-circle border mr-2"
                  height="40"
                  width="40"
                  loading="lazy"
                  src="http://res.cloudinary.com/datacode/image/upload/v1691170388/natghz9cxos2uyp4osxf.png"
                  alt="avatar"
                />
                <div className="">
                  <h6 className="mb-0 item-speaker-name">Akshay Mandliya</h6>
                  <p className="mb-0 item-speaker-bio">Fullstack Developer</p>
                </div>
              </div>
              <div className="speaker-section-2 d-flex justify-content-center align-items-center">
                <span className="item-students">{item.students}</span>
              </div>
            </div>
            <div className="d-flex justify-content-between align-items-center my-2 py-2 rating">
              <div className="rating">
                <CustomStarRating initialValue={item.rating} readonly={true} />
              </div>
              <button className="enroll-now-button">
                Enroll Now
              </button>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}

export default WorkshopCard
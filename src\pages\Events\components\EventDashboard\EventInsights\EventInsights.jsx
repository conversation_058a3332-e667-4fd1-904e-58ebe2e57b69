import { useState } from "react";
import { useSelector } from "react-redux";

import EventTrafficCard from "./EventTrafficCard";
import LineGraph from "./LineGraph";

const EventInsights = () => {
  
  const { eventDetails } = useSelector((state) => state.event);

  const [timeRange, setTimeRange] = useState("lastMonth");

  //get the time range for which graph to be shown
  const handleTimeRangeChange = (event) => {
    const value = event.target.value;
    switch (value) {
      case "1":
        setTimeRange("lastWeek");
        break;
      case "2":
        setTimeRange("lastTwoWeeks");
        break;
      case "3":
        setTimeRange("lastThreeWeeks");
        break;
      default:
        setTimeRange("lastMonth");
        break;
    }
  };

  //get yeserday past month visit
  const getVisitCounts = (visits) => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const yesterday = new Date(today);
    yesterday.setDate(today.getDate() - 1);

    const startOfLastMonth = new Date(today);
    startOfLastMonth.setMonth(today.getMonth() - 1);

    let yesterdayCount = 0;
    let lastMonthCount = 0;

    visits.forEach((visit) => {
      const visitDate = new Date(visit.timestamp);
      visitDate.setHours(0, 0, 0, 0);

      if (visitDate.getTime() === yesterday.getTime()) {
        yesterdayCount++;
      }

      if (visitDate >= startOfLastMonth && visitDate < today) {
        lastMonthCount++;
      }
    });

    return { yesterdayCount, lastMonthCount };
  };

  const { yesterdayCount, lastMonthCount } = getVisitCounts(
    eventDetails.pageVisits
  );

  return (
    <>
      <div className="py-4">
        <div className="row mx-0 py-4 px-2 event-insight">
          <div className="col-12 px-0 d-flex flex-wrap justify-content-between">
            <h4>Page Views ({eventDetails.pageVisits.length})</h4> 
            <select
              onChange={handleTimeRangeChange}
            >
              <option selected>Past Month</option>
              <option value="1">Last Week</option>
              <option value="2">Last Two Weeks</option>
              <option value="3">Last Three Weeks</option>
            </select>
          </div>
          <div className="col-12 px-0 ">
            <p>See recent page Views of the event page </p>
          </div>
        </div>
        <div className="row mx-0 p-2 d-flex justify-content-center ">
          <div className="col-12 px-1 pt-2 event-insight-content">
            <div className="row mx-0">
              <LineGraph
                userData={eventDetails.pageVisits}
                timeRange={timeRange}
              />
            </div>
            <div className="row mx-0">
              <div className="col-sm-6 p-1 p-sm-3 border-right border-top">
                <h5>Page views</h5>
                <span className="d-flex justify-content-between pb-2">
                  <span>
                    <p className="p-0 m-0">Yesterday</p>
                    <p className="p-0 m-0">{yesterdayCount}</p>
                  </span>
                  <span>
                    <p className="p-0 m-0">Past Month</p>
                    <p className="p-0 m-0">{lastMonthCount}</p>
                  </span>
                </span>
                <h6>Live Traffic</h6>
                <EventTrafficCard />
              </div>
              <div className="col-sm-6 p-1 p-sm-3 border-top">
                <span>
                  <h6>Top Referer</h6>
                  <span className="d-flex justify-content-between pb-2">
                    <span>Lu.ma</span>
                    <span>100%</span>
                  </span>
                </span>
                <span>
                  <h6>Top Cities</h6>
                  <span className="d-flex justify-content-between pb-2">
                    <span>Indore India</span>
                    <span>100%</span>
                  </span>
                </span>
                <span>
                  <h6>Top Sources</h6>
                  <span>
                    <p>hello here are the various sources</p>
                  </span>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default EventInsights;

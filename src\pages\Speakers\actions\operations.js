import axios from "axios";
import * as actions from "./actionCreators";
import {
  ADD_SPEAKER_LOADING,
  GET_SPEAKER_DETAILS_LOADING,
  SET_SPEAKER_DETAILS,
  GET_EVENT_SPEAKER_FILTER_LOADING,
} from "../constants";
import { generateQueryParams } from "../../../components/utils";
import { triggerNotifier } from "../../../components/utils/notifier";

const baseURL = process.env.REACT_APP_BASE_URL;

export const addSpeaker = (speaker) => (dispatch) => {
  dispatch({ type: ADD_SPEAKER_LOADING });
  return axios
    .post(`${baseURL}/user/speaker/create`, speaker)
    .then((res) => {
      if (res.status === 200) {
        triggerNotifier({
          message: "Speaker details added ",
          type: "success",
          duration: 1000,
          icon: "👏",
        });
        dispatch({ type: ADD_SPEAKER_LOADING });
        return { success: true, speaker:res?.data?.speaker };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: ADD_SPEAKER_LOADING });
      console.log("Add Speaker Error", error);
      triggerNotifier({
        message: "Failed to add Speaker details",
        type: 'error',
        duration: 1000,
        icon: "⚠️",
      });
    });
};

export const getSpeakers = (data) => (dispatch) => {
  dispatch({ type: GET_EVENT_SPEAKER_FILTER_LOADING });
  return axios
    .get(
      `${baseURL}/user/speakers${generateQueryParams({
        limit: data?.limit,
        page: data?.page,
        search: data?.search,
        technology: data?.technology,
      })}`
    )
    .then((res) => {
      if (res.status === 200) {
        triggerNotifier({
          message: "Speaker Details Loaded",
          type: "success",
          duration: 1000,
          icon: "👏",
        });
        dispatch(actions.setSpeakersList(res.data));
        dispatch({ type: GET_EVENT_SPEAKER_FILTER_LOADING });
        return { success: true, speakers: res?.data?.Speakers };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: GET_EVENT_SPEAKER_FILTER_LOADING });
      console.log("Get Speakers Error", error);
      triggerNotifier({
        message: "Failed to Get Speaker Details",
        type: 'error',
        duration: 1000,
        icon: "⚠️",
      });
    });
};

export const getSpeakerDetails = (id) => (dispatch) => {
  dispatch({ type: GET_SPEAKER_DETAILS_LOADING });
  return axios
    .get(`${baseURL}/user/speaker/${id}`)
    .then((res) => {
      if (res.status === 200) {
        triggerNotifier({
          message: "Speaker Details Loaded",
          type: "success",
          duration: 1000,
          icon: "👏",
        });
        dispatch({ type: SET_SPEAKER_DETAILS, payload: res.data.Speaker });
        dispatch({ type: GET_SPEAKER_DETAILS_LOADING });
        return { success: true, Speaker: res?.data?.Speaker };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: GET_SPEAKER_DETAILS_LOADING });
      console.log("Get Speaker Details Error", error);
      triggerNotifier({
        message: "Failed to Get Speaker Details",
        type: 'error',
        duration: 1000,
        icon: "⚠️",
      });
    });
};

export const editSpeaker = (speaker) => (dispatch) => {
  dispatch({ type: ADD_SPEAKER_LOADING });
  return axios
    .patch(`${baseURL}/user/speaker/${speaker._id}`, { data: speaker })
    .then((res) => {
      if (res.status === 200) {
        triggerNotifier({
          message: "Edit Speaker Details Loaded",
          type: "success",
          duration: 1000,
          icon: "👏",
        });
        dispatch(getSpeakers());
        dispatch({ type: ADD_SPEAKER_LOADING });
        return { success: true, speaker: res?.data?.speaker };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: ADD_SPEAKER_LOADING });
      console.log("Edit Speaker Details Error", error);
      triggerNotifier({
        message: "Edit Speaker Details Error",
        type: 'error',
        duration: 1000,
        icon: "⚠️",
      });
    });
};

export const deleteSpeaker = (id) => (dispatch) => {
  return axios
    .delete(`${baseURL}/user/speaker/${id}`)
    .then((res) => {
      if (res.status === 200) {
        triggerNotifier({
          message: "Delete Speaker details",
          type: "success",
          duration: 1000,
          icon: "👏",
        });
        return { success: true, speaker: res?.data?.speaker };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      console.log("Delete Speaker Details Error", error);
      triggerNotifier({
        message: "Delete Speaker Details Error",
        type: 'error',
        duration: 1000,
        icon: "⚠️",
      });
    });
};

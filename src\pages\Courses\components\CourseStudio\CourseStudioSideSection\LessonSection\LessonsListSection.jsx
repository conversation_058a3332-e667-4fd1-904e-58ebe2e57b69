import React, { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useParams } from "react-router";
import { Collapse } from "reactstrap";

import CustomLoader from "../../../../../../components/sharedComponents/CustomLoader";
import {
  createSection,
  deleteSection,
  editCourse,
  editSection,
  getSectionDetails,
  getCourseDetails,
  deleteLesson,
  getLessonDetails,
} from "../../../../actions/operations";
import Section from "./Section";
import Lesson from "./Lesson";
import { handleListOrder } from "../../../../../../components/utils";
import DeleteModal from "../../../../../../components/sharedComponents/DeleteModal";
import { setLessonDetails } from "../../../../actions";
import ToolTip from "../../../../../../components/sharedComponents/ToolTip";

const LessonsListSection = () => {
  const dispatch = useDispatch();
  const { id } = useParams();

  const {
    courseDetails: { sections },
    courseDetailsLoading,
    createSectionLoading,
    sectionDetails: { lessons },
    sectionsDetailsLoad,
  } = useSelector((state) => state.course) || {};

  const [isOpen, setIsOpen] = useState("");
  const [openSectionDeleteModal, setOpenSectionDeleteModal] = useState(false);
  const [openLessonDeleteModal, setOpenLessonDeleteModal] = useState(false);
  const [tempSectionDeleteId, setTempSectionDeleteId] = useState();
  const [tempLessonDeleteId, setTempLessonDeleteId] = useState();

  const handleCreateSection = () => {
    dispatch(
      createSection({
        name: "",
        course: id,
        author: process.env.REACT_APP_ADMIN_AUTHOR_ID,
      })
    ).then((res) => {
      if (res && res.success) {
        dispatch(getCourseDetails(id));
      }
    });
  };

  const handleLessonDetails = (lessonId) => {
    dispatch(getLessonDetails(lessonId));
  };

  const handleLessonCollapse = (sectionId) => {
    if (sectionId === isOpen) {
      return setIsOpen("");
    }
    setIsOpen(sectionId);
    dispatch(getSectionDetails(sectionId));
  };

  const handleSectionOrder = (index, signal) => {
    if (sections) {
      let orderedSection = handleListOrder(sections, index, signal);
      dispatch(editCourse({ id: id, sections: orderedSection }));
    }
  };

  const handleLessonOrder = (index, signal) => {
    if (lessons) {
      let orderedLesson = handleListOrder(lessons, index, signal);
      dispatch(editSection({ id: isOpen, lessons: orderedLesson }));
    }
  };

  //  ----Delete ---
  const toggleSectionDeleteModal = () => {
    setOpenSectionDeleteModal(false);
  };

  const toggleLessonDeleteModal = () => {
    setOpenLessonDeleteModal(false);
  };

  const handleSectionDeleteModal = () => {
    dispatch(deleteSection(tempSectionDeleteId)).then((res) => {
      if (res && res.success) {
        dispatch(setLessonDetails({}));
        dispatch(getCourseDetails(id));
        setOpenSectionDeleteModal(false);
      }
    });
  };

  const handleLessonDeleteModal = () => {
    dispatch(deleteLesson(tempLessonDeleteId)).then((res) => {
      if (res && res.success) {
        dispatch(setLessonDetails({}));
        dispatch(getSectionDetails(isOpen));
        setOpenLessonDeleteModal(false);
      }
    });
  };

  const handleSectionDelete = (id) => {
    setTempSectionDeleteId(id);
    setOpenSectionDeleteModal(true);
  };

  const handleLessionDelete = (id) => {
    setTempLessonDeleteId(id);
    setOpenLessonDeleteModal(true);
  };
  // -XXXX------------------------------------

  return (
    <>
      <div className="row mx-0 lesson-sections border-right">
        <div className="col-12 px-0">
          <div className="lesson-header">
            <h4>Lessons</h4>
            <p>
              Add sections and lessons to compose your course as you'd like.
            </p>
          </div>
          {createSectionLoading || courseDetailsLoading ? (
            <div className="course-studio-body-height d-fex align-items-center">
              <CustomLoader />
            </div>
          ) : (
            <>
              <div className="section-list m-0 p-0">
                {sections && sections.length ? (
                  <div className="row mx-0 lesson-list">
                    <div className="col-12">
                      {sections &&
                        sections.length &&
                        sections.map((item, index) => (
                          <div key={item._id}>
                            <Section
                              key={item._id}
                              index={index}
                              section={item}
                              className="section-list-item"
                              handleLessonCollapse={handleLessonCollapse}
                              isOpen={isOpen}
                              sectionsCount={sections.length}
                              handleSectionOrder={handleSectionOrder}
                              handleSectionDelete={handleSectionDelete}
                            />
                            <Collapse isOpen={item._id === isOpen}>
                              {sectionsDetailsLoad ? (
                                <CustomLoader />
                              ) : (
                                <ul className="pl-0 mb-0">
                                  {lessons &&
                                    lessons.map((ele, index) => (
                                      <Lesson
                                        key={ele._id}
                                        index={index}
                                        lesson={ele}
                                        lessonsCount={lessons.length}
                                        handleLessonOrder={handleLessonOrder}
                                        handleLessionDelete={handleLessionDelete}
                                        handleLessonDetails={handleLessonDetails}
                                      />
                                    ))}
                                </ul>
                              )}
                            </Collapse>
                          </div>
                        ))}
                    </div>
                  </div>
                ) : (
                  <div className="row mx-0 lesson-list course-studio-body-height">
                    <div className="col-12 d-flex justify-content-center align-items-center">
                      <small className="mb-0">
                        There is no lesson in this course
                      </small>
                    </div>
                  </div>
                )}
              </div>
            </>
          )}
          <div className="row mx-0 lesson-footer">
            <div className="col-12 d-flex align-items-center justify-content-end">
              <ToolTip title="Create Section" placement="bottom">
                <div
                  className="btn btn-primary"
                  onClick={() => handleCreateSection()}
                >
                  Create Section
                </div>
              </ToolTip>
            </div>
          </div>
        </div>
      </div>
      <DeleteModal
        open={openSectionDeleteModal}
        toggle={toggleSectionDeleteModal}
        onSubmit={handleSectionDeleteModal}
        submitButtonName={"Delete Section"}
        message={"Are you sure want to delete Section"}
      />
      <DeleteModal
        open={openLessonDeleteModal}
        toggle={toggleLessonDeleteModal}
        onSubmit={handleLessonDeleteModal}
        submitButtonName={"Delete Lession"}
        message={"Are you sure want to delete Lesson"}
      />
    </>
  );
};
export default LessonsListSection;

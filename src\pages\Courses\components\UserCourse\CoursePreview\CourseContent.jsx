import React, { useState } from "react";
import { <PERSON><PERSON><PERSON>, But<PERSON> } from "reactstrap";
import { CiLock } from "react-icons/ci";
import { CgNotes } from "react-icons/cg";
import { IoIosArrowDown, IoIosArrowUp } from "react-icons/io";
import { RiCheckboxCircleLine } from "react-icons/ri";

const CourseContent = ({ courseDetails, handleLessonDetails, courseCurriculum }) => {

  const [collapse, setCollapse] = useState([]);

  const toggleCollapse = (index) => {
    let collapseCopy = [...collapse];
    collapseCopy[index] = !collapseCopy[index];
    setCollapse(collapseCopy);
  }

  return (
    <div>
      <div className="content-header p-3">
        <p className="fw-normal fs-6 ms-10">Course Content</p>
        <h6 className=" fw-bold fs-6 ms-10 mb-4 ">{courseCurriculum?.name}
        </h6>
      </div>
      <div className="content-list">
        {courseCurriculum?.sections && courseCurriculum?.sections.map((section, index) => {
          return (
            <div className="guidlines" key={index}>
              <div className="guidlines-header">
                <div className="d-flex pl-2">
                  <h6 className=" fw-bold fs-6 ms-3">
                    <span className="m-0">{index + 1}.</span> {section.name}
                  </h6>
                  <span className=" fs-6 ml-4">{section.lessons.filter((lesson) => (lesson.isCompleted) === true).length}/{section.lessons.length}</span>
                </div>
                <Button>
                  {collapse === index ? (
                    <IoIosArrowUp
                      className="up-arrow"
                      onClick={() => toggleCollapse(index)}
                    />
                  ) : (
                    <IoIosArrowDown
                      className="down-arrow"
                      onClick={() => toggleCollapse(index)}
                    />
                  )}
                </Button>
              </div>
              <Collapse isOpen={collapse[index]}>
                <div className="guidlines-list pl-2">
                  <ul>
                    {section.lessons.map((lesson, innerIndex) => {
                      return (
                        <div
                          className="d-flex  align-items-start mb-3"
                          key={innerIndex}>
                          {lesson.isCompleted === true ? (
                            <RiCheckboxCircleLine className="tickMark" />
                          ) : lesson.isCompleted === false ? (
                            <CiLock className="lock" />
                          ) : (
                            <CiLock className="lock" />
                          )}
                          <li className="fs-6 ms-2 d-flex align-items-start">
                            <span className="mt-1">{innerIndex + 1}.</span>

                            <div onClick={() => handleLessonDetails(lesson._id)}>
                              <p className="m-0 mt-1 fs-6">
                                {lesson.name}
                              </p>
                              <span className="fs-6 fw-normal  course-time m-0 mt-5">
                                <CgNotes className="mr-3" />
                                {lesson.duration ? lesson.duration : "0"}{" "}mins.
                                {/* {lesson.time} */}
                              </span>
                            </div>
                          </li>
                        </div>
                      );
                    })}
                  </ul>
                </div>
              </Collapse>
            </div>
          );
        })}
      </div>
    </div>
  );
};
export default CourseContent;
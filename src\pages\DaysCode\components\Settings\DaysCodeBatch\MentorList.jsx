import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";

import { createMentorBatch, getBatchMentors } from "../../../actions";
import MentorListCard from "./MentorListCard";
import AddContentToBatchModal from "../../../../../components/sharedComponents/AddContentToBatchModal";
import CustomLoader from "../../../../../components/sharedComponents/CustomLoader";
import BatchContentHeader from "./BatchContentHeader";
import NoDataBlock from "../../../../../components/sharedComponents/NoDataBlock";
import AddSpeakerDrawer from "../../../../../components/Drawers/AddSpeaker/AddSpeakerDrawer";

const MentorList = ({
  batch,
  codeBatchDetails,
  codeBatchesLoading,
  setActiveTab,
}) => {

  const dispatch = useDispatch();
  const { batchMentorList, problemsListLoading } = useSelector(
    ({ dayscode }) => dayscode,
  );

  const [showModal, setShowModal] = useState(false);
  const [speakerId, setSpeakerId] = useState();
  const [isEdit, setIsEdit] = useState(false);
  const [role, setRole] = useState();


  useEffect(() => {
    dispatch(
      getBatchMentors({
        batch: codeBatchDetails._id,
        role: role,
      }),
    );
  }, [role, dispatch, codeBatchDetails._id]);

  const handleSelectedSpeaker = (speakerId) => {
    setShowModal(true);
    setSpeakerId(speakerId);
  };

  const handleAddMentorInToBatch = (mentorData) => {
    dispatch(
      createMentorBatch({
        batch: batch,
        mentor: mentorData.id,
        role: mentorData.role,
      }),
    );
  };

  return (
    <>
      {/* <div className='d-flex justify-content-between'>
        <h1 className='mentor-heading'>Mentor</h1>
        <AddSpeakerDrawer
          batch={batch}
          btnName='Add Speaker'
          handleSelectedSpeaker={handleSelectedSpeaker}
        />
      </div> */}
      <BatchContentHeader
        batch={batch}
        type={"Mentor"}
        setRole={setRole}
        handleSelectedSpeaker={handleSelectedSpeaker}
        setActiveTab={setActiveTab}
      />

      {problemsListLoading ? (
        <div className='row d-flex justify-items                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            -center'>
          <div className='col-12 align-items-center text-center '>
            <CustomLoader />
          </div>
        </div>
      ) : (
        <>
          <div className='mx-4 mt-3 border-top'>
            {batchMentorList && batchMentorList.length > 0 ? (
              batchMentorList.map(
                (mentor, key) =>
                  mentor?.mentor !== null && (
                    <div key={key} className='mt-3'>
                      <MentorListCard
                        c
                        mentor={mentor}
                        batchContent={mentor}
                        batch={batch}
                      />
                    </div>
                  ),
              )
            ) : (
              <NoDataBlock
                route={
                  <AddSpeakerDrawer
                    btnName='Add Speaker'
                    handleSelectedSpeaker={handleSelectedSpeaker}
                    batch={batch}
                  />
                }
              />
            )}
          </div>
        </>
      )}

      {showModal && (
        <AddContentToBatchModal
          batch={batch}
          type='mentor'
          setshowModal={setShowModal}
          showModal={showModal}
          handleAddIntoBatch={handleAddMentorInToBatch}
          batchContent={speakerId}
          isEdit={isEdit}
          setIsEdit={setIsEdit}
        />
      )}
    </>
  );
};

export default MentorList;

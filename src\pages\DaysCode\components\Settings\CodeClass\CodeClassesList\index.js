"use client";

import { withErrorBoundary } from 'react-error-boundary';
import CodeClassesList from './CodeClassesList';
import ErrorFallback from '../../../../../../components/sharedComponents/ErrorFallback';

function logErrorToService(error, info) {
  // Log the error to an error logging service
  console.error("Logging error:", error, info);
}

export default withErrorBoundary(CodeClassesList, {
  FallbackComponent: ErrorFallback,
  onError: logErrorToService,
});
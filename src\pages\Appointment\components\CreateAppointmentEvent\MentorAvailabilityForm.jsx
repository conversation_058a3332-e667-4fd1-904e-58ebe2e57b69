import React, { useEffect, useState, useRef } from "react";
import { connect, useDispatch, useSelector } from "react-redux";
import { getFormValues, reduxForm } from "redux-form";
import moment from "moment";
import { Collapse } from "reactstrap";
import { FaRegEdit } from "react-icons/fa";

import { editMentorAvailabilities } from "../../actions";
import { renderInputField } from "../../../../components/sharedComponents/ReduxFormFields";
import CustomLoader from "../../../../components/sharedComponents/CustomLoader";

const MentorAvailabilityForm = ({
  handleSubmit,
  submitting,
  id,
  appointmentEventDetails,
  mentorAvailabilitiesList,
}) => {
  const dispatch = useDispatch();
  const { editMentorAvaiabilityLoading } =
    useSelector((state) => state.appointment) || {};

  const {  breakDuration, duration } =
    appointmentEventDetails || {};
  const [slotOpen, setSlotOpen] = useState(false);
  const [formData, setFormData] = useState([]);
  const [editMode, setEditMode] = useState(true);
  const [selectedEdit, setSelectedEdit] = useState(null);
  const [editModeDate, setEditModeDate] = useState(null);
  const [tempSaveLoader, setTempSaveLoader] = useState(null);
  const [isSaveLoader, setIsSaveLoader] = useState(null);
  const startDateRef = useRef(null);

  useEffect(() => {
    // Generate an array of dates
    // const dateRange = getDatesBetween(new Date(startDate), new Date(endDate));

    // Create initialFormFields based on the dateRange
    if (mentorAvailabilitiesList) {
      var initialFormFields = mentorAvailabilitiesList.map((availability) => ({
        mentor: availability?.mentor?._id,
        appointmentEvent: availability?.appointmentEvent?._id,
        availabilityId: availability?._id,
        date: new Date(availability?.date),
        startTime: availability.startTime,
        endTime: availability.endTime,
        isNotAvailable: false,
      }));
      setFormData(initialFormFields);
    }
  }, []);

  // const startDate = new Date('2024-01-01');
  // const endDate = new Date('2024-01-05');

  const generateTimeSlots = (startSlot, endSlot, duration, breakDuration) => {
    const timeSlots = [];
    let startTime = new Date(`2000-01-01T${startSlot}`);
    const endTime = new Date(`2000-01-01T${endSlot}`);

    while (startTime < endTime) {
      const formattedStartTime = startTime.toLocaleTimeString([], {
        hour: "2-digit",
        minute: "2-digit",
      });

      const endTimeForSlot = new Date(
        startTime.getTime() + duration.value * 60000
      );
      const formattedEndTime = endTimeForSlot.toLocaleTimeString([], {
        hour: "2-digit",
        minute: "2-digit",
      });

      const timeRange = `${formattedStartTime} - ${formattedEndTime}`;
      timeSlots.push(timeRange);

      startTime.setMinutes(
        startTime.getMinutes() + (duration.value + breakDuration.value)
      );
    }

    const endOfLastSlot = new Date(startTime.getTime() - 10 * 60000);
    if (endOfLastSlot >= endTime) {
      timeSlots.pop();
    }

    return timeSlots;
  };

  const handleCheckboxChange = (value, field) => {
    handleFieldChange("isNotAvailable", !field.isNotAvailable, field.date);
    // handleFieldChange('startTime', '', field.date)
    // handleFieldChange('endTime', '', field.date)
  };

  const handleSlotCollapse = (id) => {
    setSlotOpen(slotOpen === id ? false : id);
  };

  const handleEditClick = (date) => {
    setSelectedEdit(date);
    setEditModeDate(date);
    setEditMode(false);
    if (!editMode && startDateRef.current) {
      startDateRef.current.focus();
    }
  };

  // Function to generate an array of dates between startDate and endDate
  /*  const getDatesBetween = (start, end) => {
    const dates = [];
    let currentDate = new Date(start);

    while (currentDate <= end) {
      dates.push(new Date(currentDate));
      currentDate.setDate(currentDate.getDate() + 1);
    }

    return dates;
  };
 */
  const handleEndTimeChange = (fieldName, endTime, field) => {
    // Call generateTimeSlots to update the slots array
    const updatedSlots = generateTimeSlots(
      field.startTime,
      endTime,
      duration,
      breakDuration
    );

    // Now, update the formData with the new endTime and slots
    const updatedFormData =
      formData &&
      formData.map((fieldRow, i) => {
        if (fieldRow.date === field.date) {
          return {
            ...fieldRow,
            [fieldName]: endTime,
          };
        }
        return fieldRow;
      });

    setFormData(updatedFormData);
  };

  const handleFieldChange = (fieldName, fieldValue, date) => {
    const updatedFormFields =
      formData &&
      formData.map((field) => {
        if (field.date === date) {
          return {
            ...field,
            [fieldName]: fieldValue,
          };
        }
        return field;
      });

    setFormData(updatedFormFields);
  };

  /*   const onSubmit = (values) => {
    // Update the form data state
  }; */

  const editMentorAvailability = (date) => {
    setTempSaveLoader(date);
    setIsSaveLoader(true);
    const dayAvailability = formData.find((item) => item.date === date);
    dispatch(editMentorAvailabilities(dayAvailability));
  };

  const renderFormRows = () => {
    return (
      formData &&
      formData.map((field, index) => (
        <>
          <div
            key={index}
            className="row mx-0 mt-4 d-flex align-items-center select-schedule-section"
          >
            <div className="col-3">
              <input
                type="date"
                name={`date-${field.date}`}
                label="Date"
                placeholder=""
                className="form-input-field"
                component={renderInputField}
                value={moment(field.date).format("YYYY-MM-DD")}
                disabled
                onChange={(e) =>
                  handleFieldChange("date", e.target.value, field.date)
                }
              />
            </div>
            {!field.isNotAvailable && (
              <>
                <div className="col-3">
                  <input
                    type="time"
                    className="form-input-field"
                    name={`startTime-${field.date}`}
                    value={field.startTime}
                    label="Start Time"
                    placeholder="Start Time"
                    component={renderInputField}
                    onChange={(e) =>
                      handleFieldChange("startTime", e.target.value, field.date)
                    }
                    disabled={selectedEdit !== field.date}
                    ref={startDateRef}
                  />
                </div>
                <div className="col-3">
                  <input
                    type="time"
                    name={`endTime-${field.date}`}
                    value={field.endTime}
                    label="End time "
                    placeholder=""
                    className="form-input-field"
                    component={renderInputField}
                    onChange={(e) =>
                      handleEndTimeChange("endTime", e.target.value, field)
                    }
                    disabled={selectedEdit !== field.date}
                  />
                </div>
              </>
            )}
            <div className="col-3 d-flex align-items-center">
              <span className="mr-3 not-available-button">
                <label className="d-flex align-items-center">
                  <input
                    name={`isNotAvailable-${field.date}`}
                    type="checkbox"
                    className="mr-2"
                    checked={field.isNotAvailable}
                    onChange={(e) =>
                      handleCheckboxChange(e.target.value, field)
                    }
                  />
                  <span>Not Available</span>
                </label>
              </span>
              {!field.isNotAvailable && field.startTime && field.endTime && (
                <span
                  className="see-slot-button mr-3"
                  onClick={() => handleSlotCollapse(field.date)}
                >
                  See Slots
                </span>
              )}
              {tempSaveLoader === field.date &&
              editMentorAvaiabilityLoading &&
              isSaveLoader ? (
                <CustomLoader />
              ) : (
                <>
                  {selectedEdit === field.date && !editMode ? (
                    <button
                      type="click"
                      onClick={() => editMentorAvailability(field.date)}
                      className="save-button"
                      disabled={submitting}
                    >
                      <span>Save</span>
                    </button>
                  ) : (
                    <FaRegEdit
                      className="mr-3"
                      onClick={() => handleEditClick(field.date)}
                    />
                  )}
                </>
              )}
            </div>
          </div>
          <Collapse isOpen={field.date === slotOpen}>
            <div className="row mx-0 my-4 px-2 d-flex justify-content-center">
              <div className="col-12 px-3 time-list">
                <ul className="d-flex justify-content-center align-items-center mb-0">
                  {generateTimeSlots(
                    field.startTime,
                    field.endTime,
                    duration,
                    breakDuration
                  ).map((timeSlot, index) => (
                    <li key={index}>{timeSlot}</li>
                  ))}
                </ul>
              </div>
            </div>
          </Collapse>
        </>
      ))
    );
  };

  return <>{renderFormRows()}</>;
};

export default connect((state, ownProps) => ({
  formValues: getFormValues(`manage-availability-${ownProps.id}`)(state),
  form: `manage-availability-${ownProps.id}`,
}))(
  reduxForm({
    destroyOnUnmount: false,
    forceUnregisterOnUnmount: true,
  })(MentorAvailabilityForm)
);

import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useParams } from "react-router-dom";
import { FaLongArrowAltLeft, FaLongArrowAltRight } from "react-icons/fa";
import _ from "lodash";

import CourseStudioHeader from "./CourseStudioHeader";
import {
  getCourseDetails,
  getLessonDetails,
  setLessonDetails,
} from "../../actions";
import CourseStudioSideSection from "./CourseStudioSideSection";
import LessonContentCraftSection from "./LessonContentCraftSection/LessonContentCraftSection";
import CustomLoader from "../../../../components/sharedComponents/CustomLoader";
import { getFirstLessonOfCourse } from "./helper";

const CourseStudio = ({ type }) => {

  const dispatch = useDispatch();
  const { id, blogId } = useParams();
  
  const {
    courseDetails,
    courseDetailsLoading,
    lessonDetailsLoading,
    lessonDetails,
  } = useSelector((state) => state.course) || {};

  const [show, setShow] = useState(true);

  useEffect(() => {
    // Blogs are lessons only which is inside sections of any course
    if (id) {
      dispatch(getCourseDetails(id)).then((res) => {
        console.log(res.data);
        if (res) {
          dispatch(
            getLessonDetails(
              getFirstLessonOfCourse(res.data?.course?.sections)._id

            )
          );
        }
      });
    }
    if (blogId) {
      dispatch(getLessonDetails(blogId));
    }

    return () => dispatch(setLessonDetails({}));
  }, []);

  const handleBlockSectionDrawer = () => {
    setShow(!show);
  };

  return (
    <>
      {
        type !== 'blog' &&
        <CourseStudioHeader
          courseDetailsLoading={courseDetailsLoading}
          courseDetails={courseDetails}
        />
      }
      <div className="d-flex mx-0">
        <div
          className={`px-0 ${show
            ? "course-studio-side-section open"
            : "course-studio-side-section"
            }`}
        >
          <CourseStudioSideSection type={type} />
        </div>
        <div className="course-studio-page-wrapper">
          {show ? (
            <FaLongArrowAltLeft
              className="toggle-icon disp"
              onClick={() => handleBlockSectionDrawer()}
            />
          ) : (
            <FaLongArrowAltRight
              className="toggle-icon disp-2"
              onClick={() => handleBlockSectionDrawer()}
            />
          )}
          <div className="row mx-0 ">
            <div className="col-12 px-0">
              {lessonDetailsLoading || courseDetailsLoading ? (
                <CustomLoader />
              ) : (
                <>

                  {!_.isEmpty(lessonDetails) ? (
                    <LessonContentCraftSection type={type} />
                  ) : (

                    <p>No lesson selected. Choose a lesson from the sidebar</p>

                  )}
                </>
              )}
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default CourseStudio;

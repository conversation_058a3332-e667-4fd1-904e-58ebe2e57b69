import React from "react";
import PropTypes from "prop-types";
import { Link } from "react-router-dom";
import { Dropdown, Space } from "antd";
import moment from "moment";
import { TbPencil } from "react-icons/tb";
import { MdDelete } from "react-icons/md";
import { PiDotsThreeOutlineVerticalFill } from "react-icons/pi";

import { timeConvert } from "../../../../components/utils";
import defaultimg from "../../../../assets/images/bg/Event.png";


const EventCard = ({ data ={}, handleDeleteEvent }) => {

  const items = [
    {
      key: "actions",
      type: "group",
      label: "Event Host Actiom",
      children: [
        {
          key: 1,
          label: (
            <Link to={`/event/dashboard/${data._id}`}>
              <span>
                <TbPencil className="mr-1" />
                Manage Event
              </span>
            </Link>
          ),
        },
        {
          key: 2,
          label: (
            <span
              onClick={() => handleDeleteEvent(data._id)}
              className="text-danger"
            >
              <MdDelete className="mr-1" />
              Delete Event
            </span>
          ),
        },
      ],
    },
  ];
  
  return (
    <>
      <div className="col-lg-4 col-sm-6 col-12 p-3">
        <div id="event-card" className="text-decoration-none event-card">
          <div className="mb-3">
            <img
              height="auto"
              width="auto"
              //src={data.eventImg ? data.eventImg : defaultimg}
              src={defaultimg}
              alt="avatar"
              loading="lazy"
              className="img-fluid mx-auto"
            />
            {data.hasRegistred && (
              <span className="reg-status-block">
                <i className="fal fa-check-circle" /> Registered
              </span>
            )}
            {
              <>
                {/* <UncontrolledDropdown
                  setActiveFromChild
                  className='event-host-action-toggle'
                >
                  <DropdownToggle tag='a' className=''>
                    <ToolTip title='Event host action' placement='top'>
                      <HiOutlineDotsVertical />
                    </ToolTip>
                  </DropdownToggle>
                  <DropdownMenu
                    className='dropdown-menu mt-3 card-schadow'
                    left='true'
                  >
                    <DropdownItem header>
                      <span>Host Actions</span>
                    </DropdownItem>
                    <DropdownItem>
                      <Link to={`/event/dashboard/${data._id}`}>
                        <span>
                          <i className='fal fa-pencil mr-2' />
                          Manage Event
                        </span>
                      </Link>
                    </DropdownItem>
                    <DropdownItem onClick={() => deleteEvent(data._id)}>
                      <span>
                        <i className='fal fa-trash mr-2' />
                        Delete Event
                      </span>
                    </DropdownItem>
                  </DropdownMenu>
                </UncontrolledDropdown> */}
                <div className="event-host-action-toggle">
                  <Dropdown
                    menu={{
                      items,
                    }}
                    trigger={["click"]}
                  >
                    <Space>
                      <PiDotsThreeOutlineVerticalFill size={25} />
                    </Space>
                  </Dropdown>
                </div>
              </>
            }
            <span className="date-block">
              <h1>{new Date(data.start_date).getDate()}</h1>
              <small>
                {new Date(data.start_date).toLocaleString("en-US", {month: "short"})}
              </small>
            </span>
          </div>
          <h1 className="event-card-title px-3 pt-4 text-truncate">
            {data.title}
          </h1>
          <div className="row m-0 event-card-footer">
            <div className="col-12 px-3">
              {/* <h6 className="event-card-sub-heading">{data.sub_title}</h6> */}
              <p>
                <i className="fas fa-laptop-code mr-2 icon" />
                {data.category}
              </p>
              <p className="">
                <i className="far fa-calendar-alt mr-2 icon" />
                {moment(data.start_date).format("LL")} |{" "}
                {`${timeConvert(data.start_time)} - ${timeConvert(
                  data.end_time
                )}`}
              </p>
              <div className="row m-0 my-4">
                <div className="col-12 px-0">
                  <span className="event-tag px-2">Python</span>
                  <span className="event-tag px-2">Begineers</span>
                  <span className="event-tag px-2">{data.event_mode === "virtual" ? "Online" : "Offline"}</span>
                </div>
              </div>
            </div>
          </div>
          <a
            href={`https://datacode.in/event/${data && data._id}`}
            target="_blank"
            className="fuck-line"
            rel="noreferrer"
          >
            <div className="hover-sutter">
              <div className="btn btn-primary">Join Now!</div>
            </div>
          </a>
        </div>
      </div>
      {/* <ToolTip message="Go To Event Page" id="event-card" /> */}
    </>
  );
};



EventCard.propTypes = {
  data: PropTypes.object,
};

export default EventCard;

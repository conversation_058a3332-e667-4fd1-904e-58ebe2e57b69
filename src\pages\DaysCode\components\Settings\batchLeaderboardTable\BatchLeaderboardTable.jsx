import React from 'react'

import BootstrapTable from 'react-bootstrap-table-next'
import { getColumns } from './helpers'

const BatchLeaderboardTable = ({ batchLeaderboard }) => {
    let columns = getColumns();
  
    return (
      <>
        {batchLeaderboard && batchLeaderboard.leaderboard?.length &&
          <div className="table table-responsive">
            <BootstrapTable
              keyField='id'
              bordered={true}
              data={batchLeaderboard.leaderboard}
              columns={columns}
              search
              hover={true}
            />
          </div>
        }
      </>
    )
  }
  
  export default BatchLeaderboardTable
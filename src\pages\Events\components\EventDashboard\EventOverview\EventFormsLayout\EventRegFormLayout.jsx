import { useState, useEffect } from "react";
import { connect, useDispatch } from "react-redux";
import { useParams } from "react-router";
import { Field, reduxForm, getFormValues } from "redux-form";

import { getOptions } from "../../../../../../components/utils";
import { addEventFormField, editEventFormField } from "../../../../actions";
import ToolTip from "../../../../../../components/sharedComponents/ToolTip";

import {
  renderInputField,
  renderSelectField,
} from "../../../../../../components/sharedComponents/ReduxFormFields";
import { required } from "../../../../../../components/utils/validators";
import {
  fieldTypesOptions,
  generateFieldName,
} from "./helper";

const EventRegFormLayout = ({
  reset,
  handleSubmit,
  submitting,
  initialize,
  eventDetails,
  formValues,
  formName,
  tempEditField,
  isEdit,
  setEdit,
  onCloseDrawer,
  showDrawer,
}) => {
  
  const dispatch = useDispatch();
  const { id } = useParams();

  const [optionsLabel, setOptionsLabel] = useState([]);
  const [showOption, setShowOption] = useState(true);

 useEffect(()=>{
  console.log(formValues,"form")
 },[formValues])

  useEffect(() => {
    if (tempEditField && isEdit) {
      initialize({
        label: tempEditField.label,
        type: tempEditField.type,
        id: tempEditField.id,
      });
      if (tempEditField?.options?.length) {
        let optionsLabel = tempEditField?.options?.map((item) => item.label);
        setOptionsLabel(optionsLabel);
      }
    }
  }, [isEdit]);

  const onSubmit = (values) => {
    console.log(values,"values");
    const formField = { ...values };
    const optionsArr = optionsLabel.map((item) => {
      let tempObj = {};
      tempObj["label"] = item;
      if (values.type === "radio") {
        tempObj["name"] = generateFieldName(values.label);
      } else if (values.type === "checkbox") {
        tempObj["name"] = generateFieldName(item);
      }
      return tempObj;
    });
    formField["formName"] = formName;
    optionsArr.length && (formField["options"] = optionsArr);
    formField["type"] = values.type;
    formField["name"] = generateFieldName(values.label);
    formField["eventId"] = id;

    if (isEdit) {
      dispatch(editEventFormField(formField)).then((res) => {
        if (res) {
          resetForm();
        }
      });
    } else {
      formField["id"] = Math.random().toString(36).substr(4, 9);
      dispatch(addEventFormField(formField)).then((res) => {
        if (res) {
          resetForm();
        }
      });
    }
  };

  const resetForm = () => {
    reset("create-form");
    initialize({ type: "" });
    setOptionsLabel([]);
    setEdit(false);
    onCloseDrawer();
  };
  const generateRadioOptionField = () => {
    if (formValues?.option) {
      setOptionsLabel([...optionsLabel, formValues?.option]);
      initialize({ ...formValues, option: "" });
    }
  };
  const deleteRadioOptionField = (e) => {
    const data = optionsLabel.filter((item) => item !== e);
    setOptionsLabel(data);
  };

  return (
    <>
      <div className="border p-4 rounded-lg">
        <h4>Event {formName} Form Layout</h4>
        <form className="" onSubmit={handleSubmit(onSubmit)}>
          <div className="row mx-0 my-0 px-0">
            <div className="col-8 m-0 px-0">
              <Field
                name="type"
                label="Field Type"
                placeholder=""
                options={getOptions(fieldTypesOptions, "type")} // type is form variable
                textField={"type"}
                component={renderSelectField}
              />
            </div>
          </div>
          <div className="form-group mt-0">
            <Field
              type="text"
              name="label"
              label="Enter Field Label"
              placeholder="Question"
              component={renderInputField}
              validate={[required]}
            />
          </div>
          {(formValues?.type === "radio" ||
            formValues?.type === "checkbox") && (
            <div className="border rounded-lg p-3 mb-3">
              {optionsLabel && optionsLabel.map((item) => (
                <ul className="">
                  <li className="d-flex align-items-center">
                    <span className="d-flex align-items-center">
                      {formValues?.type === "radio" ? (
                        <>
                          <input type="radio" disabled className="mr-3" />
                          <span>{item}</span>
                        </>
                      ) : (
                        <>
                          <input type="checkbox" disabled className="mr-3" />
                          <span>{item}</span>
                        </>
                      )}
                    </span>
                    <i
                      onClick={() => deleteRadioOptionField(item)}
                      className="ml-3 fal fa-times"
                    />
                  </li>
                </ul>
              ))}
              {showOption ? (
                <>
                  <div className="form-group">
                    <Field
                      type="text"
                      name={`option`}
                      label={`Option ${optionsLabel.length + 1}`}
                      placeholder=""
                      component={renderInputField}
                      validate={[required]}
                    />
                  </div>
                  <div className="d-flex justify-content-between">
                    <span
                      onClick={() => generateRadioOptionField()}
                      className=""
                    >
                      <i className="fal fa-plus-circle mr-2" />
                      Add Option
                    </span>
                    {optionsLabel.length !== 0 && (
                      <span onClick={() => setShowOption(false)}>
                        <i className="fal fa-times mr-2" />
                        Cancel
                      </span>
                    )}
                  </div>
                </>
              ) : (
                <span onClick={() => setShowOption(true)} className="">
                  <i className="fal fa-plus-circle mr-2" />
                  Add More Option
                </span>
              )}
            </div>
          )}
          {formValues?.type === "linearScale" && (
            <div className="border rounded-lg p-x-1 py-2  mb-3">
              <div className="row mx-0">
                <div className="col-6">
                  <Field
                    type="number"
                    name={"start"}
                    placeholder="Start"
                    component={renderInputField}
                    isDisabled={formValues.start <= 0}
                    validate={[required]}
                  />
                </div>
                <div className="col-6">
                  <Field
                    type="number"
                    name={"end"}
                    placeholder="End"
                    component={renderInputField}
                    isDisabled={formValues.end >= 10 || formValues.end <= 0}
                    validate={[required]}
                  />
                </div>
              </div>
              <div className="row my-2 mx-0">
                <div className="col-6 m-0">
                  <Field
                    type="text"
                    name="labelStart"
                    label={formValues.start}
                    placeholder="Label Start"
                    component={renderInputField}
                    validate={[required]}
                  />
                </div>
                <div className="col-6 m-0">
                  <Field
                    type="text"
                    name="labelEnd"
                    label={formValues.end}
                    placeholder="Label End"
                    component={renderInputField}
                    validate={[required]}
                  />
                </div>
              </div>
            </div>
          )}
          <div className="row mx-0">
            <div className="col-12 new-form">
              <ToolTip title="Cancel" placement="bottom">
                <button
                  type="submit"
                  className="cancel-form"
                  onClick={() => resetForm()}
                >
                  <span>Cancel</span>
                </button>
              </ToolTip>
              <ToolTip title="Add Form Field" placement="bottom">
                <button
                  type="submit"
                  className="add-form"
                  disabled={submitting}
                >
                  <span>{isEdit ? "Edit" : "Add Form"}</span>
                </button>
              </ToolTip>
            </div>
          </div>
        </form>
      </div>
    </>
  );
};

export default connect((state) => ({
  formValues: getFormValues("create-form")(state),
}))(
  reduxForm({
    form: "create-form",
  })(EventRegFormLayout)
);

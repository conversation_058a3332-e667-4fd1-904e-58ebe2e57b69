import { useState } from "react";

import QuestionViewDrawer from "./Drawers/QuestionViewDrawer.jsx";

const QuestionTypesSidebar = () => {

  const [questionType, setQuestionType] = useState("Multiple choice");
  const [open, setOpen] = useState(false);

  const handleClick = (e) => {
    const selectedType = e.target.innerText;
    setQuestionType(selectedType);
    setOpen(true); // Open the modal
  };

  return (
    <>
      <div className='row px-3 mx-0 q-type-sidebar'>
        <div className='col-12 pt-3 pb-2'>
          <h4>Select Question Type</h4>
        </div>

        {/* General Category */}
        <div className='col-12'>
          <div className='col-12 d-flex align-items-baseline pb-1'>
            <h5>General</h5>
            <i className='bi bi-exclamation-circle m-2'></i>
          </div>
          <div className='col-12 d-flex flex-column'>
            <span onClick={handleClick}>Multiple choice</span>
            <span onClick={handleClick}>Subjective</span>
            <span onClick={handleClick}>Diagram</span>
          </div>
        </div>

        {/* Coding Category */}
        <div className='col-12'>
          <div className='col-lg-12 d-flex align-items-baseline py-1'>
            <h5>Coding</h5>
            <i className='bi bi-exclamation-circle m-2'></i>
          </div>
          <div className='col-12 d-flex flex-column'>
            <span onClick={handleClick}>Programming</span>
            <span onClick={handleClick}>Data Science</span>
            <span onClick={handleClick}>Front End</span>
            <span onClick={handleClick}>SQL</span>
            <span onClick={handleClick}>Machine Learning</span>
          </div>
        </div>

        {/* Product Category */}
        <div className='col-12'>
          <div className='col-12 d-flex align-items-baseline py-1'>
            <h5>Product</h5>
            <i className='bi bi-exclamation-circle m-2'></i>
          </div>
          <div className='col-12 d-flex flex-column'>
            <span onClick={handleClick}>Full Stack</span>
            <span onClick={handleClick}>Java</span>
            <span onClick={handleClick}>C#</span>
            <span onClick={handleClick}>Python</span>
            <span onClick={handleClick}>File Upload</span>
            <span onClick={handleClick}>DevOps</span>
          </div>
        </div>

        {/* Modal Drawer */}
        {open && (
          <QuestionViewDrawer
            open={open}
            setOpen={setOpen}
            questiontype={questionType}
          />
        )}
      </div>
    </>
  );
};

export default QuestionTypesSidebar;

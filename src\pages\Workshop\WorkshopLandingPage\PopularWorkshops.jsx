import React from "react"
import { Popover } from 'antd';
import { GiSettingsKnobs } from "react-icons/gi";
import { RiArrowUpDownFill } from "react-icons/ri";

import WorkshopFilterPopover from "./WorkshopFilterPopover";
import WorkshopCard from "./WorkshopCard";
import WorkshopSortPopover from "./WorkshopSortPopover";

const PopularWorkshops = () => {
  const data = [
    {
      img: "http://res.cloudinary.com/datacode/image/upload/v1691170388/natghz9cxos2uyp4osxf.png",
      days: '3x Days',
      title: 'Full Stack Web Development Workshop',
      category: 'Programming',
      speaker: '',
      students: '100+ Students ',
      rating: '3',
    },
    {
      img: "http://res.cloudinary.com/datacode/image/upload/v1691170388/natghz9cxos2uyp4osxf.png",
      days: '10x Days',
      title: 'Full Stack Web Development Workshop',
      category: 'Web Development',
      speaker: '',
      students: '50+ Students',
      rating: '5',
    },
    {
      img: "http://res.cloudinary.com/datacode/image/upload/v1691170388/natghz9cxos2uyp4osxf.png",
      days: '3x Days',
      title: 'Full Stack Web Development Workshop',
      category: 'Programming',
      speaker: '',
      students: '50+ Students ',
      rating: '2',
    },
  ]

   /* const categories = ['Programming', 'Web Development', 'Design', 'Data Science', 'Placement Prep']; */

  return (
    <>
      <div className="row mx-0 d-flex justify-content-center popular-workshop-section">
        <div className="col-12 col-md-10">
          <div className="row mx-0 pb-3">
            <div className="col-12 px-0">
              <div className="row mx-0 d-flex">
                <div className="col-md-6 col-12">
                  <h1 className="heading">Popular<span className="workshop-name"> Workshops</span></h1>
                </div>
                <div className="col-md-6 col-12 justify-content-end workshop-filter-bar d-flex">
                  <Popover arrow={false} placement="bottomRight" content={WorkshopSortPopover} title="Sort Workshop" trigger="click">
                    <div className="d-flex align-items-center btn">
                      <i className="fas fa-filter search-icon" />
                      <div className="py-2 px-3 ml-4 sort-border">Sort <RiArrowUpDownFill className="ml-2" /></div>
                    </div>
                  </Popover>
                  <Popover arrow={false} placement="bottomRight" content={WorkshopFilterPopover} title="Filter Applications" trigger="click">
                    <div className="d-flex align-items-center btn ">
                      <i className="fas fa-filter search-icon" />
                      <div className="py-2 px-3 ml-4 filter-border">Filter<GiSettingsKnobs className="ml-2" /></div>
                    </div>
                  </Popover>
                </div>
              </div>
            </div>
          </div>
          <div className="row mx-0 px-4">
            {data && data.map((item, i) => (
              <div className="col-md-6 col-12 d-flex p-4" key={i}>
                <WorkshopCard item={item} />
              </div>
            ))}
          </div>
          <div className="row mx-0 my-5">
            <div className="col-12 text-center">
              <div>
                <button className="explore-button">Explore All Workshops....</button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}

export default PopularWorkshops
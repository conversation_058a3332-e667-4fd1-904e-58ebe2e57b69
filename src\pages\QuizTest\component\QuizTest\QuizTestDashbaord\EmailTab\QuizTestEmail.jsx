import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useParams } from "react-router";
import { Combobox } from "react-widgets";

import { editEvent } from "../../../../../Events/actions";
import TextEditor from "../../../../../../components/sharedComponents/TextEditor";

const QuizTestEmail = ({ setActiveTab, activeTab }) => {
  
  const dispatch = useDispatch();
  const { id } = useParams();

  const { eventDetails } = useSelector((state) => state.event);
  
  const [template, setTemplate] = useState("");
  const [editorContent, setEditorContent] = useState("<p></p>");
  const [subject, setSubject] = useState("");
  const [emailFrom, setEmailFrom] = useState("");
  const { emailTemplates = [] } = eventDetails || [];

  const Templates = [
    "Invite Email",
    "Remainder Email",
    "Invite Cancel Email",
    "Thankyou Email",
    "Shortlist Email",
    "Archive Email",
  ];

  const setInitialValues = () => {
    setSubject("Add Subject");
    setEmailFrom("Add Address");
    setEditorContent("<p>Add Email Content format</p>");
  };

  useEffect(() => {
    const format = emailTemplates.find((item) => item.template === template);
    console.log("format", format);
    if (format) {
      setSubject(format.subject);
      setEmailFrom(format.from);
      setEditorContent(format.body);
    } else {
      setInitialValues();
    }
  }, [template]);

  const handleTemplateChange = (value) => {
    setTemplate(value);
  };

  const handleSendEmail = (event) => {
    event.preventDefault();
    const existingTemplateIndex = emailTemplates.findIndex(
      (item) => item.template === template
    );
    if (existingTemplateIndex !== -1) {
      emailTemplates[existingTemplateIndex] = {
        template,
        from: emailFrom,
        subject,
        body: editorContent,
      };
    } else {
      emailTemplates.push({
        template,
        from: emailFrom,
        subject,
        body: editorContent,
      });
    }
    setInitialValues();
    setTemplate("");
    console.log("Sending data to API:", emailTemplates);
    dispatch(editEvent({ _id: id, emailTemplates: emailTemplates }));
  };

  return (
    <>
      <div className="row mx-0 p-3 email-tab-section">
        <div className="col-12 px-0 mb-3">
          <h4>Email Templates</h4>
        </div>
        <div className="col-lg-12 px-0 py-3 d-flex pb-3 flex-column">
          <span className="py-1 select-template">Select Template</span>
          <div className="text-left form-set">
            <Combobox
              style={{ width: "30%" }}
              data={Templates}
              placeholder="Select Template"
              value={template}
              onChange={handleTemplateChange}
            />
          </div>
        </div>
        <div className="col-lg-12 px-0 d-flex form-section">
          <div className="col-lg-9 p-4">
            <form onSubmit={handleSendEmail}>
              <div className="mb-3">
                <label htmlFor="fromInput" className="form-label">
                  From
                </label>
                <input
                  type="email"
                  className="form-control"
                  id="fromInput"
                  value={emailFrom}
                  onChange={(e) => setEmailFrom(e.target.value)}
                />
              </div>
              <div className="mb-3">
                <label htmlFor="subjectInput" className="form-label">
                  Subject
                </label>
                <input
                  type="text"
                  className="form-control"
                  id="subjectInput"
                  value={subject}
                  onChange={(e) => setSubject(e.target.value)}
                />
              </div>
              <div className="mb-3">
                <label className="form-label d-block">Email Template</label>
                <div className="border">
                  <TextEditor
                    handleTextEditor={setEditorContent}
                    text={editorContent}
                  />
                </div>
              </div>
              <button type="submit" className="btn custom-button">
                <span>Send Email</span>
              </button>
            </form>
          </div>
          <div className="col-lg-3 p-4 note-section">
            <p>
              <strong>Note</strong>: Company name can be changed from company
              information in Settings. <a href="#">Review here</a>
            </p>
            <p>
              <strong>Note</strong>: The Strings <code>{"{@TestName@}"}</code>,{" "}
              <code>{"{@FullName@}"}</code>,<code>{"{@StartTime@}"}</code>,{" "}
              <code>{"{@EndTime@}"}</code>,<code>{"{@TestDuration@}"}</code>,{" "}
              <code>{"{@TestLink@}"}</code>,{" "}
              <code>{"{@UserLoginDetails@}"}</code> will be replaced by actual
              values, so do not remove them.
            </p>
          </div>
        </div>
      </div>
    </>
  );
};

export default QuizTestEmail;

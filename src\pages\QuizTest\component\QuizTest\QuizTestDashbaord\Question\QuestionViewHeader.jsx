const QuestionViewHeader = ({ setIsEditDrawer }) => {
  return (
    <div className='row mx-0 p-3 d-flex align-items-center w-100 question-view-header'>
      <div className='col-lg-6 col-sm-12 px-0'>
        <h4 className='font-weight-bold mb-0'>Multiple Choice Question</h4>
      </div>
      <div className='col-lg-6 col-sm-12 px-0 d-flex justify-content-lg-end justify-content-start'>
        <button
          className='btn btn-primary mr-2 mb-2 mb-sm-0 edit-que-btn '
          onClick={() => setIsEditDrawer(true)}
        >
          Edit Question
        </button>
        <button className='btn btn-outline-secondary mb-2 mb-sm-0 try-que-btn'>
          Try Question
        </button>
      </div>
    </div>
  );
};

export default QuestionViewHeader;

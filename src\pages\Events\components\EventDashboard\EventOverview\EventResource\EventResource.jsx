import React, { useState ,useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Button, Input, Select, Space } from "antd";
import { useParams } from "react-router";

import { editEvent, getEventDetails } from "../../../../actions";
import { resourceOptions } from "../../../../../../components/utils/selectOptions";
import CustomLoader from "../../../../../../components/sharedComponents/CustomLoader";
import LinkPreviewCard from "./LinkPreviewCard";

const EventResource = () => {

  const dispatch = useDispatch();
  const { id } = useParams();

  const { eventDetailsLoading, editEventLoading } =
    useSelector((state) => state.event) || {};

  const [resourceType, setResourceType] = useState(null);
  const [resourceURL, setResourceURL] = useState(null);
  const [resource, setResource] = useState([]);
  const [isUpload, setIsUpload] = useState(false);

  useEffect(() => {
    dispatch(getEventDetails(id)).then((res) => {
      if (res) {
        setResource(res.data.resources);
      }
    });
  }, []);

  const handleResourceSubmitButton = () => {
    if (resourceType !== null && resourceURL !== null) {
      setResource((prev) => [
        ...prev,
        { type: resourceType, url: resourceURL },
      ]);
      setResourceType();
      setResourceURL("");
      setIsUpload(true);
    } else {
      alert("Enter The valid Type and URL");
    }
  };
  
  const handleResourceUploadBtn = () => {
    dispatch(editEvent({ _id: id, resources: resource })).then(() => {
      setIsUpload(false);
    });
  };

  const handleDeleteResource = (url) => {
    setResource((prev) => prev.filter((resource) => resource.url !== url));
    setIsUpload(true);
  };

  return (
    <div className='event-resource py-3'>
      <div className='event-resource-header px-4'>
        <h1>You can Add the Resource details </h1>
        <span>Event settings can't be changed after the event is created.</span>
      </div>
      <div className='bg-white border rounded-lg shadow mx-5 py-2 event-resource-content my-5'>
        <div className='d-flex flex-column align-items-center justify-content-center'>
          <h3 className='my-2'>Add Resource</h3>
          <div className='d-flex flex-wrap align-items-center'>
            <Space.Compact size=''>
              <Select
                placeholder='Resource Type'
                options={resourceOptions}
                value={resourceType}
                onChange={(e) => {
                  setResourceType(e);
                }}
              />
              <Input
                placeholder='URL'
                value={resourceURL}
                onChange={(e) => {
                  setResourceURL(e.target.value);
                }}
              />
            </Space.Compact>
            <Button
              className='mx-3'
              type='dashed'
              onClick={handleResourceSubmitButton}
            >
              Submit
            </Button>
            {isUpload && (
              <Button
                type='primary'
                onClick={handleResourceUploadBtn}
                className='upload-btn'
              >
                {editEventLoading ? <CustomLoader color='white' /> : "Upload"}
              </Button>
            )}
          </div>
        </div>
        <div className='event-resource-list mt-4 px-4'>
          <h3>Resource List</h3>
          <div className='row'>
            {eventDetailsLoading ? (
              <div className='d-flex justify-content-center w-100'>
                <CustomLoader />
              </div>
            ) : (
              resource?.map((item, i) => {
                return (
                  <div className='col-4 my-2' key={i}>
                    <LinkPreviewCard
                      resourceURL={item.url}
                      resourceType={item.type}
                      handleDeleteResource={handleDeleteResource}
                    />
                  </div>
                );
              })
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default EventResource;

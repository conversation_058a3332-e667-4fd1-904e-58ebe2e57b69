import React, { useEffect, useState } from "react";
import { connect, useDispatch, useSelector } from "react-redux";
import { Field, getFormValues, reduxForm } from "redux-form";
import { useNavigate, useParams } from "react-router-dom";

import { addSponsor, editSponsor, getSponsorDetails } from "../../actions";

import {
  renderInputField,
  renderSelectField,
} from "../../../../components/sharedComponents/ReduxFormFields";
import { required, email } from "../../../../components/utils/validators";
import ProfileImageUpload from "../../../../components/sharedComponents/ProfileImageUpload";
import { sponsorType } from "../../../../components/utils/selectOptions";

const AddSponsor = ({
  reset,
  handleSubmit,
  submitting,
  initialize,
  formValues,
}) => {
  
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { id } = useParams();

  const {
    eventDetails,
  } = useSelector((state) => state.event) || {};

  const [isEdit, setEdit] = useState(false);
  const [uplodedImg, setUploadedImg] = useState(
    "https://res.cloudinary.com/datacode/image/upload/v1664786288/xs4idyfyatoqbsuvfoni.png"
  );

  useEffect(() => {
    if (id === "new") {
      setEdit(false);
    } else {
      setEdit(true);
      dispatch(getSponsorDetails(id)).then((res) => {
        if (res) {
          console.log(res)
          setUploadedImg(res.Sponsor.imgUrl);
          initialize(res.Sponsor);
        }
      });
    }
  }, [id , dispatch , initialize]);

  const onSubmit = (values) => {
    const sponsor = { ...values };
    sponsor["imgUrl"] = uplodedImg;
    if (isEdit) {
      dispatch(editSponsor(sponsor)).then((res) => {
        if (res && res.success) {
          dispatch(getSponsorDetails(eventDetails._id));
          navigate("/sponsors");
        }
      });
    } else {
      dispatch(addSponsor(sponsor)).then((res) => {
        if (res && res.success) {
          reset("add-sponsor");
          navigate("/sponsors");
        }
      });
    }
  };

  return (
    <>
      <h3 className="p-3">Sponsor Application</h3>
      <div className="row mx-0 border rounded-lg form-shadow">
        <div className="col-12">
          <div className="row mx-0 p-2 main">
            <div className="col-12 left">
              <h3 className="text-center">Become Community Partner</h3>
              <form className="" onSubmit={handleSubmit(onSubmit)}>
                <div className="row mx-0">
                  <div className="col-12 col-md-6">
                    <h5 className="mb-0">Basic Details</h5>
                    <div className="row mx-0 mb-4 mt-2">
                      <div className="col-12 rounded p-0">
                        <div className="row mx-0">
                          <div className="col-12 col-md-6">
                            <Field
                              type="text"
                              name="name"
                              label="Name"
                              placeholder="Organization / Club / Community Name"
                              component={renderInputField}
                              validate={[required]}
                            />
                          </div>
                          <div className="col-12 col-md-6">
                            <Field
                              type="text"
                              name="orgEmail"
                              label="Org Email"
                              placeholder=""
                              component={renderInputField}
                              validate={[required, email]}
                            />
                          </div>
                        </div>
                        <div className="row mx-0">
                          <div className="col-12 col-md-6">
                            <Field
                              type="text"
                              name="orgPhone"
                              label="Org Contact No."
                              placeholder=""
                              component={renderInputField}
                              validate={[required]}
                            />
                          </div>
                          <div className="col-12 col-md-6">
                            <Field
                              type="text"
                              name="website"
                              label="Website"
                              placeholder=""
                              component={renderInputField}
                            />
                          </div>
                        </div>
                        <div className="row mx-0">
                          <div className="col-12 col-md-4">
                            <Field
                              type="text"
                              name="city"
                              label="City"
                              placeholder="Ex: Indore"
                              component={renderInputField}
                              validate={[required]}
                            />
                          </div>
                          <div className="col-12 col-md-4">
                            <Field
                              type="text"
                              name="state"
                              label="State"
                              placeholder="Ex: Madhya Pradesh"
                              component={renderInputField}
                              validate={[required]}
                            />
                          </div>
                          <div className="col-12 col-md-4">
                            <Field
                              type="text"
                              name="country"
                              label="Country"
                              placeholder="Ex: India"
                              component={renderInputField}
                              validate={[required]}
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="col-12 col-md-6 my-5 my-md-0 d-flex align-items-center justify-content-center ">
                    <ProfileImageUpload
                      uplodedImg={uplodedImg}
                      setUploadedImg={setUploadedImg}
                    />
                  </div>
                  <div className="col-12 col-md-6">
                    <h5 className="mb-0">Lead Details</h5>
                    <div className="row mx-0 mt-2">
                      <div className="col-12 border rounded p-0">
                        <div className="row mx-0">
                          <div className="col-12 col-md-6">
                            <Field
                              type="text"
                              name="pocName"
                              label="Lead Name"
                              placeholder="Organization / Club / Community Name"
                              component={renderInputField}
                              validate={[required]}
                            />
                          </div>
                          <div className="col-12 col-md-6">
                            <Field
                              type="text"
                              name="pocEmail"
                              label="Lead Email"
                              placeholder=""
                              component={renderInputField}
                              validate={[required, email]}
                            />
                          </div>
                        </div>
                        <div className="row mx-0">
                          <div className="col-12 col-md-6">
                            <Field
                              type="text"
                              name="pocPhone"
                              label="Lead Contact No."
                              placeholder=""
                              component={renderInputField}
                              validate={[required]}
                            />
                          </div>
                          <div className="col-12 col-md-6">
                            <Field
                              type="text"
                              name="pocLinkedin"
                              label="Lead Linkedin"
                              placeholder=""
                              component={renderInputField}
                              validate={[required]}
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="col-12 col-md-6">
                    <h5 className="mb-0 border-bottom">Social Profiles</h5>
                    <div className="row mx-0 mt-2 mb-4">
                      <div className="col-12 rounded p-3">
                        <Field
                          type="text"
                          name="linkedin"
                          label="Linedin Id"
                          placeholder=""
                          component={renderInputField}
                          validate={[required]}
                        />
                        <Field
                          type="text"
                          name="github"
                          label="Github Id"
                          placeholder=""
                          component={renderInputField}
                        />
                        <Field
                          type="text"
                          name="twitter"
                          label="Twitter Id"
                          placeholder=""
                          component={renderInputField}
                        />
                      </div>
                    </div>
                  </div>
                  <div className="col-12 col-md-6">
                    <h5 className="mb-0">Let us know</h5>
                    <div className="row mx-0 mt-2 mb-4">
                      <div className="col-12 border rounded p-3">
                        <Field
                          type="textarea"
                          name="description"
                          label="Commmunity Description"
                          placeholder=""
                          component={renderInputField}
                          validate={[required]}
                        />
                        <Field
                          name="technologies"
                          label="Select Sponsor Type"
                          placeholder=""
                          options={sponsorType} // type is form variable
                          textField={"type"}
                          component={renderSelectField}
                          validate={[required]}
                        />
                      </div>
                    </div>
                  </div>
                </div>
                <div className="row mt-4">
                  <div className="col-12 text-center">
                    <button
                      type="submit"
                      className="btn custom-button"
                      disabled={submitting}
                    >
                      <span>{isEdit ? "Edit Sponsor" : "Add Sponsor"}</span>
                    </button>
                  </div>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default connect((state) => ({
  formValues: getFormValues("add-sponsor")(state),
}))(
  reduxForm({
    form: "add-sponsor",
  })(AddSponsor)
);

import { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Select } from "antd";

import { Pagination } from "@akshayDatacode/datacode-ui";
import { getAppointmentEvents, deleteAppointmentEvent } from "../../actions";
import DeleteModal from "../../../../components/sharedComponents/DeleteModal";
import AppointmentEventCard from "./AppointmentEventCard";
import CustomLoader from "../../../../components/sharedComponents/CustomLoader";
import AppointmentEventsHeader from "./AppointmentEventsHeader";

const AppointmentEvents = () => {
  const dispatch = useDispatch();
  const {
    appointmentEvents,
    appointmentEventsLoading,
    appointmentEventsCount,
  } = useSelector((state) => state.appointment) || {};

  const [toggle, setToggle] = useState("upcoming");
  const [openModal, setOpenModal] = useState(false);
  const [tempDeleteId, setTempDeleteId] = useState();
  const [selected, setSelected] = useState(0);
  const [cardsPerPage, setCardsPerPage] = useState(10);

  const limitOptions = [
    {
      label: <span>10 rows</span>,
      value: "10",
    },
    {
      label: <span>20 rows</span>,
      value: "20",
    },
    {
      label: <span>30 rows</span>,
      value: "4",
    },
    {
      label: <span>50 rows</span>,
      value: "50",
    },
    {
      label: <span>All</span>,
      value: "all",
    },
  ];

  useEffect(() => {
    dispatch(
      getAppointmentEvents({
        page: selected,
        limit: cardsPerPage,
        schedule: toggle,
      })
    );
    window.scrollTo(0, 0);
  }, [toggle, cardsPerPage]);

  const toggleModal = () => {
    setOpenModal(!openModal);
  };

  const handleTab = (id) => {
    setToggle(id);
  };
  const handleDeleteEvent = (id) => {
    setTempDeleteId(id);
    setOpenModal(!openModal);
  };

  const pageCount = Math.ceil(appointmentEventsCount / cardsPerPage);
  const changePage = ({ selected }) => {
    setSelected(selected);
    dispatch(
      getAppointmentEvents({
        page: selected,
        limit: cardsPerPage,
        schedule: toggle,
      })
    );
  };

  const handleEventeDeleteModal = () => {
    dispatch(deleteAppointmentEvent(tempDeleteId)).then((res) => {
      if (res.success) {
        setOpenModal(!openModal);
        dispatch(
          getAppointmentEvents({
            page: selected,
            limit: cardsPerPage,
            schedule: toggle,
          })
        );
      }
    });
  };

  const changeCardPerPage = (item) => {
    if (item === "all") {
      setCardsPerPage(appointmentEventsCount);
    } else {
      setCardsPerPage(parseInt(item));
    }
  };

  

  return (
    <>
      <div className="">
        <AppointmentEventsHeader handleTab={handleTab} toggle={toggle} />
        <div className="d-flex justify-content-end">
          <div className="mt-4 mr-5">
            <Select
              onChange={(value) => changeCardPerPage(value)}
              defaultValue="10 rows"
              style={{ width: 120 }}
            >
              {limitOptions.map((item, i) => (
                <Select.Option key={i} value={item.value}>
                  {item.label}
                </Select.Option>
              ))}
            </Select>
          </div>
        </div>
        <div className="row mx-0 d-flex justify-content-center">
          <div className="col-12 col-md-10 py-4 my-4">
            {appointmentEventsLoading ? (
              <CustomLoader />
            ) : (
              <div className="row mx-0">
                <div className="col-12">
                  <div className="row mx-0">
                    {appointmentEvents && appointmentEventsCount ? (
                      appointmentEvents.map((appointment) => (
                        <div
                          className="col-md-4 col-12 my-md-2 my-4 py-1"
                          key={appointment._id}
                        >
                          <AppointmentEventCard
                            appointment={appointment}
                            handleDeleteEvent={handleDeleteEvent}
                          />
                        </div>
                      ))
                    ) : (
                      <p>No Appointment card available in list</p>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
      {appointmentEventsCount > 0 && !appointmentEventsLoading && (
        <div className="d-flex justify-content-center mb-4">
          <Pagination pageCount={pageCount} changePage={changePage} />
        </div>
      )}
      <DeleteModal
        open={openModal}
        toggle={toggleModal}
        onSubmit={handleEventeDeleteModal}
        submitButtonName={"Delete Problem"}
        message={" Are you sure want to delete this event"}
        title={"Delete Event"}
      />
    </>
  );
};

export default AppointmentEvents;

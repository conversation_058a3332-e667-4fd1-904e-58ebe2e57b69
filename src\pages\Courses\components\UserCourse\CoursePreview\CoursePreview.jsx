import React, { useState, useEffect, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import { GiHamburgerMenu } from "react-icons/gi";
import { RxCross2 } from "react-icons/rx";
import { Gr<PERSON>ogin } from "react-icons/gr";
import { Link,useParams } from "react-router-dom";

import CourseContent from "./CourseContent";
import PreviewFooter from "./PreviewFooter";
import {
  getLessonDetails,
  setLessonDetails,
  getCourseProgress,
  getCourseCurriculum,
  completeLesson,
} from "../../../actions";
import LessonPreview from "../../CourseStudio/PreviewPages/LessonPreview/LessonPreview";
import CustomLoader from "../../../../../components/sharedComponents/CustomLoader";
import { getIncompleteLessonsList } from "../helper";
import CourseCompleteDashboard from "./CourseCompleteDashboard";
import CommonModelPopup from "../../../../../components/sharedComponents/CommonModelPopup";

const CoursePreview = () => {
  
  const dispatch = useDispatch();
  const { id } = useParams();
  const blogContentRef = useRef(null);

  const {
    courseProgressLoading,
    courseDetails,
    courseProgress,
    courseCurriculum,
  } = useSelector((state) => state.course) || {};

  const [nexrPrevbuttonDisabled, setNexrPrevButtonDisabled] = useState(false);
  const [isAllLessonsComplete, setIsAllLessonsComplete] = useState(false);
  const [isOpen, setIsOpen] = useState(true);
  const [scrollPercentage, setScrollPercentage] = useState(0);
  const [lessonPreview, setLessonPreview] = useState(true);
  const [isModalVisible, setIsModalVisible] = useState(false);

  useEffect(() => {
    const blogContent = blogContentRef.current;

    const handleScroll = () => {
      const scrollPosition = blogContent.scrollTop;
      const contentHeight = blogContent.scrollHeight - blogContent.clientHeight;
      const newScrollPercentage = (scrollPosition / contentHeight) * 100;

      setScrollPercentage(newScrollPercentage);
    };

    blogContent.addEventListener("scroll", handleScroll);

    return () => {
      blogContent.removeEventListener("scroll", handleScroll);
    };
  }, [scrollPercentage]);

  useEffect(() => {
    if (id) {
      dispatch(getCourseProgress(id));
      dispatch(getCourseCurriculum(id)).then((res) => {
        if (
          res &&
          getIncompleteLessonsList(res.data?.data?.sections) !== true
        ) {
          dispatch(
            getLessonDetails(
              getIncompleteLessonsList(res.data?.data?.sections)[0]?._id,
            ),
          );
        } else {
          setIsAllLessonsComplete(true);
        }
      });
    }
    // Prevent scrolling of the body when component mounts
    document.body.style.overflow = "hidden";

    return () => {
      // Re-enable scrolling of the body when component unmounts
      document.body.style.overflow = "";
      dispatch(setLessonDetails({}));
    };
  }, [dispatch, id]);

  const handleChange = () => {
    setIsOpen(!isOpen);
  };

  const handleClose = () => {
    setIsOpen(true);
  };

  const handleLessonDetails = (lessonId) => {
    dispatch(getLessonDetails(lessonId));
    setLessonPreview(true);
  };

  const closeModal = () => {
    setIsModalVisible(false);
  };

  // Get All Course Lesson List------------
  const getAllCourseLessonsList = (courseCurriculum) => {
    const courseLessonsIds = [];
    courseCurriculum.sections &&
      courseCurriculum.sections.forEach((data) => {
        if (data) {
          data.lessons.forEach((lesson) => {
            courseLessonsIds.push(lesson?._id);
          });
        }
      });

    return courseLessonsIds;
  };
  
  const nextLesson = (currentLessonId) => {
    const allLessonsId = getAllCourseLessonsList(courseCurriculum);
    let nextLessonId = allLessonsId[allLessonsId.indexOf(currentLessonId) + 1];
    if (allLessonsId.indexOf(currentLessonId) < allLessonsId.length - 1) {
      dispatch(getLessonDetails(nextLessonId));
      setNexrPrevButtonDisabled(false);
    } else {
      setIsModalVisible(true);
    }
    setScrollPercentage(0);
  };

  const previousLesson = (currentLessonId) => {
    const allLessonsId = getAllCourseLessonsList(courseCurriculum);
    let previousLessonId =
      allLessonsId[allLessonsId.indexOf(currentLessonId) - 1];
    if (allLessonsId.indexOf(currentLessonId) !== 0) {
      dispatch(getLessonDetails(previousLessonId));
      setNexrPrevButtonDisabled(false);
    } else {
      setNexrPrevButtonDisabled(true);
    }
    setScrollPercentage(0);
  };
  // Get All Incomplete Lesson List --------------
  const getAllIncompleteCourseLessonsList = (courseCurriculum) => {
    const courseLessonsIds = [];
    courseCurriculum.sections &&
      courseCurriculum.sections.forEach((data) => {
        if (data) {
          data?.lessons.forEach((lesson) => {
            if (lesson?.isCompleted === false) {
              courseLessonsIds.push(lesson?._id);
            }
          });
        }
      });

    return courseLessonsIds;
  };
  const nextIncompleteLesson = (currentLessonId) => {
    const allLessonsId = getAllIncompleteCourseLessonsList(courseCurriculum);
    let nextLessonId = allLessonsId[allLessonsId.indexOf(currentLessonId) + 1];
    if (allLessonsId.indexOf(currentLessonId) < allLessonsId.length - 1) {
      dispatch(getLessonDetails(nextLessonId));
      setNexrPrevButtonDisabled(false);
    } else {
      setIsAllLessonsComplete(true);
    }
    setScrollPercentage(0);
  };
  // Mark as Complete Lesson By User
  const currentUser =
    sessionStorage.getItem("currentUser") ||
    localStorage.getItem("currentUser");
  const user = JSON.parse(currentUser);
  const handleLessionComplete = (lessonId) => {
    let userId = user?._id;
    if (userId) {
      dispatch(completeLesson({ lessonId, userId }));
    }
    dispatch(getCourseProgress(id));
    dispatch(getCourseCurriculum(id));

    nextIncompleteLesson(lessonId);
  };

  return (
    <>
      <div className='course-preview-container'>
        <div
          className='custom-progress-bar'
          style={{ width: `${scrollPercentage}%` }}
        />
        <div className='row mx-0 d-flex justify-content-start align-items-start course-preview'>
          {isOpen ? (
            <div className='col-12 col-md-4 col-lg-4 col-sm-12  course-content'>
              <button
                onClick={handleChange}
                className={` me-3 crossBtn ${
                  isOpen ? "float-right" : "float-left"
                }`}
              >
                <RxCross2 className='cross' />
              </button>
              <CourseContent
                courseDetails={courseDetails}
                handleLessonDetails={handleLessonDetails}
                courseCurriculum={courseCurriculum}
              />
            </div>
          ) : (
            <button
              onClick={handleClose}
              className='bg-white text-dark border-1 border-light w-5 h-5 me-3 '
            >
              <GiHamburgerMenu />
            </button>
          )}
          <div
            className={`col-12  col-sm-12 preview-content ${
              isOpen ? "col-md-8  col-lg-8" : "col-md-12  col-lg-12"
            }`}
          >
            <div className='row mx-0 main-preview' ref={blogContentRef}>
              <div className='col-12 col-md-10 col-lg-10 col-sm-12 pl-5 content-container'>
                {lessonPreview && isAllLessonsComplete === false && (
                  <LessonPreview
                    component={true}
                    nextLesson={nextLesson}
                    handleLessionComplete={handleLessionComplete}
                    nextIncompleteLesson={nextIncompleteLesson}
                  />
                )}
                {isAllLessonsComplete === true && <CourseCompleteDashboard />}
              </div>

              <div className='col-12 col-md-2 col-lg-2 col-sm-12 position-relative1 right-0'>
                <div className='d-flex align-items-center position-fixed p-2 me-10'>
                  <Link to='/courses/my'>
                    <button type='btn' className='btn exitBtn'>
                      <GrLogin /> Exit Course
                    </button>
                  </Link>
                </div>
              </div>
            </div>
            <div className='mx-0'>
              <div className={`preview-footer m-0 ${isOpen ? "mb-1" : "mb-5"}`}>
                {courseProgressLoading ? (
                  <CustomLoader />
                ) : (
                  <PreviewFooter
                    className='fixed bottom-3 right-3 z-10 hidden gap-1 lg:flex'
                    nextLesson={nextLesson}
                    previousLesson={previousLesson}
                    courseProgress={courseProgress}
                    nexrPrevbuttonDisabled={nexrPrevbuttonDisabled}
                  />
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
      {isModalVisible && (
        <CommonModelPopup
          model={isModalVisible}
          toggle={closeModal}
          message={"No more Lesson in this Course!"}
          submitButtonText={"Okey"}
          modeltitle={"Finish!"}
        />
      )}
    </>
  );
};

export default CoursePreview;

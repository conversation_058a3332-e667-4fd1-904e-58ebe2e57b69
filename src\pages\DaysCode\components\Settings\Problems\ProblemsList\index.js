import { connect } from "react-redux";
import { getProblems, removeDaysProblem } from "../../../../actions";

import ProblemsList from "./ProblemsList";

const mapStateToProps = ({ dayscode, auth }) => ({
  currentUser: auth.currentUser ? auth.currentUser : {},
  problemsListLoading: dayscode.problemsListLoading,
  problemsList: dayscode.problemsList,
});

const mapDispatchToProps = {
  getProblems,
  removeDaysProblem,
};

export default connect(mapStateToProps, mapDispatchToProps)(ProblemsList);

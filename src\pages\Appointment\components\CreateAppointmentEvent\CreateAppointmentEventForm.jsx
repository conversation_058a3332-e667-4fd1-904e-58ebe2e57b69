import React, { useEffect, useState } from "react";
import { connect, useDispatch, useSelector } from "react-redux";
import { useNavigate, useParams } from "react-router-dom";
import { Field, getFormValues, reduxForm } from "redux-form";

import {
  renderInputField,
  renderSelectField,
} from "../../../../components/sharedComponents/ReduxFormFields";
import { required } from "../../../../components/utils/validators";
import CustomLoader from "../../../../components/sharedComponents/CustomLoader";
import {
  createAppointmentEvent,
  editAppointmentEvent,
  getAppointmentEventDetails,
} from "../../actions";
import TextEditor from "../../../../components/sharedComponents/TinyTextEditor";
import { durationOptions } from "./helper";
import { virtualEventPlatform } from "../../../../components/utils/selectOptions";
import ToolTip from "../../../../components/sharedComponents/ToolTip";

const CreateAppointmentEvent = ({
  reset,
  handleSubmit,
  submitting,
  initialize,
  formValues,
  appointmentEventDetails,
  appointmentEventDetailsLoading,
}) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { id } = useParams();
  const { editAppointmentEventFormLoading } =
    useSelector((state) => state.appointment) || {};

  const [payload, setPayload] = useState("<p></p>");
  const [isEdit, setEdit] = useState(false);
  /*   const {
    eventDetails,
    eventDetailsLoading,
    eventRegistationLoading,
    imageUploading,
  } = useSelector((state) => state.event) || {}; */
   
  useEffect(() => {
    if (appointmentEventDetails && id) {
      setEdit(true);
      initialize(appointmentEventDetails);
    }
  }, [appointmentEventDetails, initialize]);

  const onSubmit = (values) => {
    const appointmentEvent = { ...values };
    // appointmentEvent['start_date_time'] = moment(appointmentEvent.startDate + ' ' + appointmentEvent.startTime).format()
    // appointmentEvent['end_date_time'] = moment(appointmentEvent.endDate + ' ' + appointmentEvent.endTime).format()

    if (isEdit) {
      dispatch(editAppointmentEvent(appointmentEvent)).then((res) => {
        if (res && res.success) {
          dispatch(getAppointmentEventDetails(appointmentEvent._id));
          navigate(`/appointment/${res.appointmentEvent._id}`);
        }
      });
    } else {
      dispatch(createAppointmentEvent(appointmentEvent)).then((res) => {
        if (res && res.success) {
          reset("add-appointment-event");
          navigate(`/appointment/${res.appointmentEvent._id}`);
        }
      });
    }
  };

  /*   const calculateDays = (startDate, endDate) => {
    const start = new Date(startDate);
    const end = new Date(endDate);

    const timeDifference = end - start;

    const numDays = Math.floor(timeDifference / (1000 * 60 * 60 * 24));

    return numDays;
  };

  const startDate = "2024-05-03";
  const endDate = "2024-08-10"; */

  /*   const totalDays = calculateDays(startDate, endDate); */

  return (
    <>
      <div className="row mx-0 d-flex justify-content-center">
        <div className="col-12 p-0 py-md-5">
          <div className="row mx-0 p-2 d-flex justify-content-center">
            <div className="col-12 col-md-10 p-5 border shadow form-body">
              <form className="" onSubmit={handleSubmit(onSubmit)}>
                <h4>MEETING DETAILS</h4>
                <div>
                  {appointmentEventDetailsLoading ? (
                    <CustomLoader />
                  ) : (
                    <>
                      <div className="row mx-0">
                        <div className="col-12">
                          <Field
                            name="title"
                            type="text"
                            label="Meeting name *"
                            placeholder="Meeting Name"
                            component={renderInputField}
                            validate={[required]}
                          />
                          <Field
                            name="platform"
                            label="Platform"
                            placeholder=""
                            options={virtualEventPlatform} // type is form variable
                            textField={"type"}
                            component={renderSelectField}
                            validate={[required]}
                          />
                          <Field
                            type="text"
                            name="meetLink"
                            label="Meet Link"
                            placeholder=""
                            component={renderInputField}
                            validate={[required]}
                          />
                          <div className="row mx-0 mb-5">
                            <div className="col-12 p-0">
                              <label className="mt-4">Details / Agenda</label>
                              <TextEditor
                                text={payload}
                                handleTextEditor={setPayload}
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                      <h4>SLOTS DETAILS</h4>
                      <div className="row mx-0 ">
                        <div className="col-3">
                          <Field
                            type="date"
                            name="startDate"
                            label="Event Start Date"
                            placeholder=""
                            component={renderInputField}
                            validate={[required]}
                          />
                        </div>
                        <div className="col-3">
                          <Field
                            type="date"
                            name="endDate"
                            label="Event End Date"
                            placeholder="<EMAIL>"
                            component={renderInputField}
                            validate={[required]}
                          />
                        </div>
                      </div>
                      <div className="row mx-0">
                        <div className="col-md-6 col-12">
                          <Field
                            name="duration"
                            label="Select the duration of each meeting*"
                            placeholder=""
                            options={durationOptions} // type is form variable
                            textField={"type"}
                            component={renderSelectField}
                            validate={[required]}
                          />
                        </div>
                        <div className="col-md-6 col-12">
                          <Field
                            name="breakDuration"
                            label="Select the duration of break after each meeting* "
                            placeholder=""
                            options={durationOptions} // type is form variable
                            textField={"type"}
                            component={renderSelectField}
                            validate={[required]}
                          />
                        </div>
                      </div>
                      <div className="row mt-4">
                        <div className="col-12 text-center">
                          <ToolTip title="Edit Speaker" placement="bottom">
                            {editAppointmentEventFormLoading ? (
                              <CustomLoader />
                            ) : (
                              <button
                                type="submit"
                                className="btn custom-button"
                                disabled={submitting}
                              >
                                <span>
                                  {isEdit ? "Edit Speaker" : "Publish Event"}
                                </span>
                              </button>
                            )}
                          </ToolTip>
                        </div>
                      </div>
                    </>
                  )}
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default connect((state) => ({
  formValues: getFormValues("add-appointment-event")(state),
}))(
  reduxForm({
    form: "add-appointment-event",
    initialValues: {
      title: "Default Title",
    },
  })(CreateAppointmentEvent)
);

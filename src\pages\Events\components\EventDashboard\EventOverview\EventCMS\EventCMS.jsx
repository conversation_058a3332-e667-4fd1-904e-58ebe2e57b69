import React, { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { useParams } from "react-router";
import Carousel from "react-multi-carousel";

import { getEventCMS } from "../../../../actions";
import noImage from "../../../../../../assets/images/bg/noImage.png";
import ToolTip from "../../../../../../components/sharedComponents/ToolTip";

const EventCMS = () => {
  
  const dispatch = useDispatch();
  const { id } = useParams();

  const [content, setContent] = useState([]);
  const responsive = {
    superLargeDesktop: {
      breakpoint: { max: 4000, min: 3000 },
      items: 1,
    },
    desktop: {
      breakpoint: { max: 3000, min: 1024 },
      items: 1,
    },
    tablet: {
      breakpoint: { max: 1024, min: 464 },
      items: 1,
    },
    mobile: {
      breakpoint: { max: 464, min: 0 },
      items: 1,
    },
  };

  const demoData =
    "Lorem ipsum dolor sit amet consectetur, adipisicing elit. Reprehenderit exercitationem natus neque labore tempore quaerat ea unde pariatur at expedita dolorum non ipsa, repudiandae quasi dolore necessitatibus eaque quis praesentium autem blanditiis. Voluptatibus quis illum animi ea blanditiis placeat dolore corrupti reiciendis suscipit earum nulla, dicta expedita corporis quia vero?";

  useEffect(() => {
    
    dispatch(getEventCMS(id)).then((res) => {
        console.log(" getEventCMS:", res);
      setContent(res?.data?.contents);
    });
  }, [id]);

  return (
    <div className="event-cms py-3 px-4">
      <div className="event-cms-header">
        <h1>You can Manage your content here </h1>
        <span>Event settings can't be changed after the event is created.</span>
      </div>
      {Array.isArray(content) && content.map(({ payload,index }) => {
        debugger;
        return (
          <div className="event-cms-outer-card rounded-lg border bg-white my-3 py-3 px-4 " key={index}>
            <div className="row">
              <div className="col-md-6 col-sm-12">
                <span className="event-tag px-3"> {payload.block}</span>
                <div className="outer-card-heading py-2">
                  <h2>{payload.heading}</h2>
                  <h5>{payload.subHeading} </h5>
                  <p>
                    {payload.content === " "
                      ? "Lorem ipsum dolor sit amet consectetur, adipisicing elit.Placeat consectetur nisi tenetur voluptatibus quibusdam quis,vel accusamus atque voluptatem iusto deleniti. Eaque fugabeatae necessitatibus ipsum sequi. Non impedit, voluptati buslaborum incidunt tempora inventore laudantium sequi dolore modi magnam quae illum pariatur ullam ipsam provident at placeat! Facere, alias est?"
                      : payload.content}
                  </p>
                </div>
                <div className="outer-card-body">
                  <div className="d-flex flex-column ">
                    <span>
                      <span>CTA: </span>
                      <b> {payload.cta}</b>
                    </span>
                    <span>
                      <span>CTA URL: </span>
                      <ToolTip title={payload.ctaURL} color="#7a7a84">
                        <a
                          className="w-100"
                          href={payload.ctaURL}
                          target="_blank"
                          rel="noopener noreferrer"
                        >
                          <b>
                            {payload.ctaURL.slice(0, 50) +
                              `${payload.ctaURL === "" ? " " : "..."}`}
                          </b>
                        </a>
                      </ToolTip>
                    </span>
                    <span>
                      Category: <b> {payload.block}</b>
                    </span>
                  </div>
                </div>
              </div>
              <div className="col-md-5 col-sm-10 border rounded-lg event-cms-images">
                <Carousel
                  swipeable={true}
                  draggable={true}
                  showDots={true}
                  // ssr={true}
                  responsive={responsive}
                  infinite={true}
                  autoPlay={true}
                  autoPlaySpeed={3000}
                  customTransition="all .5"
                  transitionDuration={500}
                  containerClass="carousel-container"
                  removeArrowOnDeviceType={["tablet", "mobile"]}
                >
                  {payload?.images?.length > 0 ? (
                    payload?.images?.map((image) => {
                      console.log(image);
                      return (
                        <div key={image.id}>
                          <img
                            src={image.url}
                            alt="EventImage"
                            width={"100%"}
                            height={300}
                          />
                        </div>
                      );
                    })
                  ) : (
                    <div>
                      <img
                        src={noImage}
                        alt="EventImage"
                        width={"100%"}
                        height={300}
                      />
                    </div>
                  )}
                </Carousel>
              </div>
            </div>
            <h1 className="heading">Card Section</h1>
            <div className="row">
              {payload?.cardSections?.map((cardData ,index) => {
                return (
                  <div className="col-lg-4 col-md-6 col-sm-12 mb-4" key={index}>
                    <div className="event-cms-inner-card border mx-1 my-2 ">
                      {Array.isArray(cardData.images) && cardData.images.length  > 1 ? (
                        <Carousel
                          swipeable={true}
                          draggable={true}
                          showDots={true}
                          responsive={responsive}
                          infinite={true}
                          autoPlay={true}
                          autoPlaySpeed={3000}
                          customTransition="all .5"
                          transitionDuration={500}
                          containerClass="carousel-container"
                          removeArrowOnDeviceType={["tablet", "mobile"]}
                        >
                          {cardData?.images?.map((image) => (
                            <div key={image.id}>
                              <img
                                src={image.url}
                                alt="EventImage"
                                width={"100%"}
                                height={150}
                              />
                            </div>
                          ))}
                        </Carousel>
                      ) : Array.isArray(cardData.images) && cardData.images.length  === 1 ? (
                        <img
                          src={cardData.images[0].url}
                          alt="EventImage"
                          width={"100%"}
                          height={150}
                        />
                      ) : (
                        <div
                         className="noimg-card"
                        >
                          <img
                            src={noImage}
                            alt="EventImage"
                            width={"100%"}
                            height={150}
                          />
                          <span
                            className="badge badge-primary"
                      
                          >
                            {cardData.block}
                          </span>
                        </div>
                      )}

                      <div className="p-2">
                        <h4>{cardData.heading}</h4>
                        <h6>{cardData.subHeading} </h6>
                        <p>{demoData.slice(0, 50) + "..."}</p>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default EventCMS;

import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "reactstrap";
import { Combobox } from "react-widgets";

import { sponsorType } from "../../../../../../components/utils/selectOptions";

const AddEventSponsorModal = ({
  open,
  toggle,
  onSubmit,
  data,
  submitButtonName,
  submitButtonColor,
}) => {
  const [type, setType] = useState();

  return (
    <Modal isOpen={open} toggle={toggle} className="delete-tutorial-modal">
      <ModalHeader toggle={toggle} className="modal-header">
        Add Sponsor to Event
      </ModalHeader>
      <ModalBody>
        <div className="row mx-0">
          <div className="col-12">
            <div className="">
              <h6>{data?.name}</h6>
            </div>
            <div className="text-left form-set">
              <label className="form-label">Sponsor Type</label>
              <Combobox
                data={sponsorType}
                dataKey={"value"}
                textField={"type"}
                placeholder={"Select Type"}
                value={type}
                onChange={(e) => setType(e.value)}
              />
            </div>
          </div>
        </div>
      </ModalBody>
      <ModalFooter>
        <div className="final-add-btn">
          <Button
            color={submitButtonColor}
            onClick={() => onSubmit({ ...data, type })}
          >
            {submitButtonName}
          </Button>
          <Button color="secondary" onClick={toggle}>
            Cancel
          </Button>
        </div>
      </ModalFooter>
    </Modal>
  );
};

export default AddEventSponsorModal;

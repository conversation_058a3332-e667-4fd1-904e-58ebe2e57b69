import { connect } from "react-redux";
import { getQuizChallenges, removeQuizChallenge } from "../../../../actions";
import QuizChallengesList from "./QuizChallengesList";

const mapStateToProps = (state) => ({
  currentUser: state.auth.currentUser ? state.auth.currentUser : {},
  problemsListLoading: state.dayscode.problemsListLoading,
  quizChallengesList: state.dayscode.quizChallengesList,
});

const mapDispatchToProps = {
  getQuizChallenges,
  removeQuizChallenge,
};

export default connect(mapStateToProps, mapDispatchToProps)(QuizChallengesList);

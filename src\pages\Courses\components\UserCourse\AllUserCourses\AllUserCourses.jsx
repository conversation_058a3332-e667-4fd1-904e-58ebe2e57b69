import React, { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Link} from "react-router-dom";

import { getCourses } from "../../../actions/operations";
import CustomLoader from "../../../../../components/sharedComponents/CustomLoader";
import AllUserCourseList from "./AllUserCourseList";
import ToolTip from "../../../../../components/sharedComponents/ToolTip";

const AllUserCourses = () => {
  const dispatch = useDispatch();
  const {coursesList, coursesCount, coursesListLoading } = useSelector((state) => state.course);

  useEffect(() => {
    dispatch(getCourses());
  }, [dispatch]);

  return (
    <>
    <div className="container-fluid">
      <div className="row mx-0">
        <div className="col-12 px-5 py-4">
          
          <div className="user-course-list">
            <div>
              <div className="row">
                <div className="col-12 col-md-6 col-lg-6 col-sm-12">
                <h3 className="f-wfw-bold ">All Courses {coursesCount}</h3>
                </div>
                <div className="col-12 col-md-6 col-lg-6 col-sm-12">
                  <Link  to="/courses/my">
                <ToolTip title="Join For Courses" placement="top">
                <button className="btn-courses  col-md-6">
                 <span>My Courses</span>
                </button>
                </ToolTip>
                </Link>
                </div>       
              </div>      
              <div className="line">
                <div className="line-double"></div>
              </div>
            </div>
            {coursesListLoading ?(<CustomLoader />): ( <AllUserCourseList
              coursesList={coursesList}
              coursesCount={coursesCount}
            />)}       
          </div>
        </div>
      </div>
    </div>
  </>
  )
}

export default AllUserCourses;
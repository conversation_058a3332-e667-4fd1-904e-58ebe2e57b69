import { useEffect } from "react";
import CandidatesInvitedTable from "../Tables/CandidatesInvitedTable";
import { getQuizTestInvitesList } from "../../../../actions/operations";
import { useDispatch, useSelector } from "react-redux";

const QuizTestInvited = ({ setActiveTab, activeTab }) => {
  const dispatch = useDispatch()
  const { quizTestInvitesList } = useSelector((state) => state.quizTest) || { quizTestInvitesList: [] }

  useEffect(() => {
    dispatch(getQuizTestInvitesList())
  }, [])

  return (
    <>
      <div className="row mx-0 p-3 invited-section">
        <div className="col-12 px-0 pb-3 invited-section-details">
          <h5 className="py-2">Quiz Test Invited</h5>
          <div className="d-flex align-items-baseline justify-content-between ">
            <p>1005 Candidates invited to take test | 902 students have not taken test yet</p>
            <p><a href="">Download Invites(.xls)</a></p>
          </div>
        </div>
        <div className="col-12 px-0">
          <CandidatesInvitedTable quizTestInvitesList={quizTestInvitesList} />
        </div>
      </div>
    </>
  )
};

export default QuizTestInvited;

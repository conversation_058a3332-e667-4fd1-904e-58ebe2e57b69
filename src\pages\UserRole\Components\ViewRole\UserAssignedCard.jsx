import UserListTable from "./UserListTable";

const UserAssignedCard = ({id,role,items, userIdRef}) => {
   
    return (
        <>
            <div className="row mx-0 mt-4 user-card">
                <div className="col-12">
                    <div className="row mx-0 d-flex justify-content-space-between">
                        <div className="col-8 card1-header d-flex justify-content-space-between align-items-stretch flex-wrap ">Users Assigned</div>
                        <div className="col-4 d-flex justify-content-end align-items-stretch flex-wrap mt-3">
                            <input type="text" className=" form-control form-control-solid ps-15 " placeholder="Search Users" />
                        </div>
                    </div>
                </div>
                <div className="col-12  card1-body overflow-scroll">
                    <div className="row mx-0 d-flex flex-column ">
                        <UserListTable id={id} role={role}  items={items} userIdRef={userIdRef}/>
                    </div>
                </div>
            </div>

        </>
    );
};

export default UserAssignedCard;

import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Field, reduxForm } from "redux-form";
import { useNavigate, useParams } from "react-router-dom";
import moment from "moment";

import CodeClassManage from "./CodeClassManage";
import {
  renderInputField,
  renderSwitchField,
} from "../../../../../../components/sharedComponents/ReduxFormFields";
import { required } from "../../../../../../components/utils/validators";
import LayoutContainer from "../../LayoutContainer";
import TextEditor from "../../../../../../components/sharedComponents/TextEditor";
import {
  createCodeClass,
  editCodeClass,
  getCodeClassDetails,
} from "../../../../actions";

const CreateCodeClass = ({
  handleSubmit,
  reset,
  initialize,
  submitting,
  formValues,
}) => {

  const dispatch = useDispatch();
  const { id } = useParams();
  const navigate = useNavigate();

  const { codeClassDetails } = useSelector((state) => state.dayscode) || {};
  
  const [isEdit, setEdit] = useState(false);
  const [descriptionText, setDescriptionText] = useState("<p> </p>");

  useEffect(() => {
    if (id !== "new") {
      dispatch(getCodeClassDetails(id)).then((res) => {
        if (res.success === true) {
          let classDate = moment(res.data.dateTime).format("YYYY-MM-DD");
          let classTime = moment(res.data.dateTime).format("HH:mm");
          initialize({ ...res.data, codeClassDate: classDate, codeClassTime: classTime });
          setDescriptionText(res.data?.description);
          setEdit(true);
        }
      });
    }
  }, [dispatch ,id ]);

  const onSubmit = (values) => {
    const codeClass = { ...values };
    codeClass["description"] = descriptionText;
    codeClass["status"] = codeClass.status ? "public" : "draft";
    codeClass["dateTime"] = moment(
      codeClass.codeClassDate + "T" + codeClass.codeClassTime
    ).format();
    if (isEdit) {
      dispatch(editCodeClass(codeClass)).then((res) => {
        if (res && res.success) {
          // dispatch(getCodeClassDetails(id));
          navigate(`/admin/days_code/codeclass`);
        }
      });
    } else {
      codeClass["id"] = Math.random().toString(36).substr(4, 9);
      dispatch(createCodeClass(codeClass)).then((res) => {
        if (res && res.success) {
          navigate(`/admin/days_code/codeclass`);
          reset("create-code-class");
        }
      });
    }
  };

  const handleTextEditor = (text) => {
    setDescriptionText(text);
  };

  return (
    <>
      <LayoutContainer>
        <div className="row mx-0">
          <div className="col-md-8 col-6 mx-0 d-flex align-items-center">
            {isEdit ? (
              <>
                <h4 className="mb-0 py-md-3 py-2 mr-3">
                  <i className="fas fa-edit mr-2" />
                  {"General settings"}
                </h4>
              </>
            ) : (
              <h4 className="mb-0 py-md-3 py-2">Add Code Class</h4>
            )}
          </div>
          <div className="col-md-4 col-6 d-flex justify-content-end align-items-center">
            <button
              onClick={() => navigate("/admin/days_code/codeclass")}
              type="button"
              className="btn enroll-small-btn"
            >
              <small>
                <i className="fas fa-file-alt mr-2" />
                All Code Classes
              </small>
            </button>
          </div>
        </div>
        <form className="py-3 px-md-5 px-1" onSubmit={handleSubmit(onSubmit)}>
          <div className="row m-0 border rounded-lg my-2 p-md-3 p-0">
          <div className="col-md-12 col-12 pl-md-0 pr-md-2 ">
                  <Field
                    type="text"
                    name="title"
                    label="Title"
                    placeholder="Class Title"
                    component={renderInputField}
                    validate={[required]}
                  />
                </div>
            <div className="col-md-6 col-lg-6 col-12 col-sm-12">
              <div className="">
              <label className="mr-4 form-label">Status: Public</label>
              <label className="switch mt-4">
                <Field
                  type="checkbox"
                  name="status"
                  component={renderSwitchField}
                />
              </label>
              </div>
              <div className="row mx-0">
                
                <div className="col-md-12 col-12 pl-md-0 pr-md-2 ">
                  <Field
                    type="text"
                    name="level"
                    label="Level"
                    placeholder="Class Level"
                    component={renderInputField}
                    validate={[required]}
                  />
                </div>
              </div>
            </div>
            <div className="col-md-6 col-lg-6 col-12 col-sm-12">
              <div className="row mx-0">
                <div className="col-md-6 col-12 pl-md-2 pr-md-0">
                  <Field
                    type="date"
                    name="codeClassDate"
                    label=" Date"
                    placeholder=""
                    component={renderInputField}
                    validate={[required]}
                  />
                </div>
                <div className="col-md-6 col-12 pl-md-2 pr-md-0">
                  <Field
                    type="time"
                    name="codeClassTime"
                    label=" Time"
                    placeholder=""
                    component={renderInputField}
                    validate={[required]}
                  />
                </div>
              </div>
            </div>
            <div className="my-3">
              <label className="form-label text-left pt-5">Description</label>
              <div className="mb-3 border ">
                {isEdit && codeClassDetails && codeClassDetails.description && (
                  <TextEditor
                    handleTextEditor={handleTextEditor}
                    text={codeClassDetails?.description ? descriptionText : ""}
                  />
                )}
                {!isEdit && (
                  <TextEditor handleTextEditor={handleTextEditor} text={" "} />
                )}
              </div>
            </div>
            <div className="row my-4">
              <div className="col-12 text-right">
                <button
                  type="submit"
                  className="btn custom-button"
                  disabled={submitting}
                >
                  <span>{isEdit ? "Save" : "Add Class"}</span>
                </button>
              </div>
            </div>
          </div>
        </form>
        {isEdit ? (
              <>
              <div className="ml-5"> 
                <h4 className="">
                  {"Manage settings"}
                </h4>
                <CodeClassManage codeClass = {id} codeClassDetails = {codeClassDetails}/>
                </div>
              </>
            ) : ""}
      </LayoutContainer>
     
    </>
  );
};

export default reduxForm({
  form: "create-code-class",
})(CreateCodeClass);

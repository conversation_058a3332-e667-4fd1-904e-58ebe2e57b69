import React, { useEffect, useState } from "react";
import { connect, useDispatch, useSelector } from "react-redux";
import { Field, getFormValues, reduxForm } from "redux-form";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";

import {
  renderInputField,
  renderRadioField,
  renderMultiSelectField,
} from "../../../../components/sharedComponents/ReduxFormFields";
import { required, email } from "../../../../components/utils/validators";
import { addSpeaker, editSpeaker, getSpeakerDetails } from "../../actions";
import ProfileImageUpload from "../../../../components/sharedComponents/ProfileImageUpload";
import { technologyOptions } from "../../../../components/utils/selectOptions";

const AddSpeaker = ({
  reset,
  handleSubmit,
  submitting,
  initialize,
  formValues,
}) => {

  const dispatch = useDispatch();
  const { id } = useParams();
  const navigate = useNavigate();
  
  const [searchParams, setSearchParams] = useSearchParams();
  const speakerType = searchParams.get("type");

  const {
    eventDetails,
  } = useSelector((state) => state.event) || {};

  const [isEdit, setEdit] = useState(false);
  const [uplodedImg, setUploadedImg] = useState(
    "https://res.cloudinary.com/datacode/image/upload/v1664786288/xs4idyfyatoqbsuvfoni.png"
  );

  useEffect(() => {
    if (id === "new") {
      setEdit(false);
    } else {
      setEdit(true);
      dispatch(getSpeakerDetails(id)).then((res) => {
        if (res) {
          setUploadedImg(res.Speaker.imgUrl);
          initialize(res.Speaker);
        }
      });
    }
  }, [id , dispatch , initialize]);

  const onSubmit = (values) => {
    const speaker = { ...values };
    speaker["imgUrl"] = uplodedImg;
    speaker["speakerType"] = speakerType;
    if (isEdit) {
      dispatch(editSpeaker(speaker)).then((res) => {
        if (res && res.success) {
          dispatch(getSpeakerDetails(eventDetails._id));
          navigate("/speakers");
        }
      });
    } else {
      dispatch(addSpeaker(speaker)).then((res) => {
        if (res && res.success) {
          reset("add-speaker");
          navigate("/speakers");
        }
      });
    }
  };

  return (
    <>
      <h3 className=" p-3">Add Speaker</h3>
      <div className="row mx-0 border rounded-lg form-shadow">
        <div className="col-12">
          <div className="row mx-0 p-2 main">
            <div className="col-12 left my-4">
              <h3 className="text-center">
                {speakerType === "guest"
                  ? "Add Guest Speaker"
                  : "Become Community Speaker"}
              </h3>
              <form className="" onSubmit={handleSubmit(onSubmit)}>
                <div className="row mx-0">
                  <div className="col-12 col-md-6">
                    <h5 className="mb-0">Basic Details</h5>
                    <div className="row mx-0 mb-4 mt-2">
                      <div className="col-12 border rounded p-0">
                        <div className="row mx-0">
                          <div className="col-12 col-md-6">
                            <Field
                              type="text"
                              name="name"
                              label="Full Name"
                              placeholder=""
                              component={renderInputField}
                              validate={[required]}
                            />
                          </div>
                          <div className="col-12 col-md-6">
                            <Field
                              type="text"
                              name="email"
                              label="Email"
                              placeholder=""
                              component={renderInputField}
                              validate={[required, email]}
                            />
                          </div>
                        </div>
                        <div className="row mx-0">
                          <div className="col-12 col-md-6">
                            <Field
                              type="text"
                              name="phone"
                              label="Contact No."
                              placeholder=""
                              component={renderInputField}
                              validate={[required]}
                            />
                          </div>
                          <div className="col-12 col-md-6">
                            <Field
                              type="text"
                              name="bio"
                              label="Designation"
                              placeholder=""
                              component={renderInputField}
                              validate={[required]}
                            />
                          </div>
                        </div>
                        <div className="row mx-0">
                          <div className="col-12 col-md-4">
                            <Field
                              type="text"
                              name="city"
                              label="City"
                              placeholder="Ex: Indore"
                              component={renderInputField}
                              validate={[required]}
                            />
                          </div>
                          <div className="col-12 col-md-4">
                            <Field
                              type="text"
                              name="state"
                              label="State"
                              placeholder="Ex: Madhya Pradesh"
                              component={renderInputField}
                              validate={[required]}
                            />
                          </div>
                          <div className="col-12 col-md-4">
                            <Field
                              type="text"
                              name="country"
                              label="Country"
                              placeholder="Ex: India"
                              component={renderInputField}
                              validate={[required]}
                            />
                          </div>
                        </div>
                        <div className="row mx-0 mt-4 pb-3">
                          <div className="col-12">
                            <label className="form-label">Gender</label>
                            <Field
                              name="gender"
                              component={renderRadioField}
                              value="male"
                              label="Male"
                              type="radio"
                              validate={[required]}
                            />
                            <Field
                              name="gender"
                              component={renderRadioField}
                              value="female"
                              label="Female"
                              type="radio"
                              validate={[required]}
                            />
                            <Field
                              name="gender"
                              component={renderRadioField}
                              value="other"
                              label="Prefer not to say"
                              type="radio"
                              validate={[required]}
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="d-flex align-items-center justify-content-center col-12 col-md-6 my-5 my-md-0">
                    <ProfileImageUpload
                      uplodedImg={uplodedImg}
                      setUploadedImg={setUploadedImg}
                    />
                  </div>
                  {speakerType !== "guest" && (
                    <div className="col-12 col-md-6">
                      <h5 className="mb-0">Let us know</h5>
                      <div className="row mx-0 mt-2 mb-4">
                        <div className="col-12 border rounded p-3">
                          <Field
                            type="textarea"
                            name="description"
                            label="Why do you want to become Community Speaker"
                            placeholder=""
                            component={renderInputField}
                            validate={[required]}
                          />
                          {formValues?.experience ? (
                            <Field
                              type="textarea"
                              name="experience"
                              label="Please share about your previous experience, (Start writing from Yes, ....)?"
                              placeholder=""
                              component={renderInputField}
                              validate={[required]}
                            />
                          ) : (
                            <div className="row mx-0 mt-4">
                              <div className="col-12 p-0">
                                <label className="form-label">
                                  Do you have any previous experience?
                                </label>
                                <Field
                                  name="experience"
                                  component={renderRadioField}
                                  value="Yes"
                                  label="Yes"
                                  type="radio"
                                  validate={[required]}
                                />
                                <Field
                                  name="experience"
                                  component={renderRadioField}
                                  value="No"
                                  label="No"
                                  type="radio"
                                  validate={[required]}
                                />
                              </div>
                            </div>
                          )}
                          <Field
                            name="technologies"
                            label="Select Technologies"
                            placeholder=""
                            options={technologyOptions} // tech is form variable
                            textField={"tech"}
                            component={renderMultiSelectField}
                            validate={[required]}
                          />
                        </div>
                      </div>
                    </div>
                  )}
                  <div className="col-12 col-md-6">
                    <h5 className="mb-0">Social Profiles</h5>
                    <div className="row mx-0 mt-2 mb-4">
                      <div className="col-12 border rounded p-3">
                        <Field
                          type="text"
                          name="linkedin"
                          label="Linedin Id"
                          placeholder=""
                          component={renderInputField}
                          validate={[required]}
                        />
                        <Field
                          type="text"
                          name="github"
                          label="Github Id"
                          placeholder=""
                          component={renderInputField}
                        />
                        <Field
                          type="text"
                          name="twitter"
                          label="Twitter Id"
                          placeholder=""
                          component={renderInputField}
                        />
                        <Field
                          type="text"
                          name="website"
                          label="Website"
                          placeholder=""
                          component={renderInputField}
                        />
                      </div>
                    </div>
                  </div>
                </div>
                <div className="row mt-4">
                  <div className="col-12 text-center">
                    <button
                      type="submit"
                      className="btn custom-button"
                      disabled={submitting}
                      id="speaker-btn"
                    >
                      <span>{isEdit ? "Edit Speaker" : "Add Speaker"}</span>
                    </button>
                  </div>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default connect((state) => ({
  formValues: getFormValues("add-speaker")(state),
}))(
  reduxForm({
    form: "add-speaker",
  })(AddSpeaker)
);

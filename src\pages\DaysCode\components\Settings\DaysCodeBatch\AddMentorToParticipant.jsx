import React, { useState } from "react";

import { useDispatch } from "react-redux";
import Combobox from "react-widgets/Combobox";

import { deleteParticipantTomentor } from "../../../actions";
import usePermissions from "../../../../../hooks/userPermission";
import CustomFormModal from "../../../../../components/sharedComponents/CustomFormModal";

const AddMentorToParticipant = ({
  model,
  toggle,
  handleAssignMentorToParticipant,
  batch,
  batchMentorList,
  participantId,
  batchParticipantsMentorList,
}) => {
  
  const dispatch = useDispatch();
  const [mentor, setMentor] = useState("");
  const { hasPermission } = usePermissions();

  const getBatchesOptions = (mentorList) => {
    const options = [];
    mentorList.forEach((mentor) => {
      const mentors = mentor.participants.includes(participantId);
      if (mentors === false) {
        options.push({ mentor: mentor?.mentor?.name, value: mentor._id });
      }
    });
    return options;
  };

  const handleDeleteParticipantToMentor = (mentor) => {
    dispatch(
      deleteParticipantTomentor({ mentor, batchParticipant: participantId }),
    ).then((res) => {
      if (res && res.success) {
        toggle();
        window.location.reload();
      }
    });
  };

  return (
    <CustomFormModal
      isOpen={model}
      toggleModal={toggle}
      title='Assign Mentor to the Participants'
      titleIcon=''
      onSubmitTitle='Add'
      onSubmit={() => handleAssignMentorToParticipant(mentor)}
    >
      <div>
        <h5 className='mr-4 '>Assign Mentor to the Participants :</h5>
        <div className='border mt-3 rounded-lg card-schadow p-3'>
          {batchParticipantsMentorList?.mentors &&
            batchParticipantsMentorList.mentors.map((mentor) => {
              return (
                <>
                  <div className='p-2 d-flex align-items-center justify-content-between'>
                    <h6 className='mb-0' key={mentor?._id}>
                      {mentor?.mentor?.name}
                    </h6>
                    {hasPermission("batch", "delete") && (
                      <i
                        className='fas fa-times'
                        onClick={() =>
                          handleDeleteParticipantToMentor(mentor?._id)
                        }
                      />
                    )}
                  </div>
                </>
              );
            })}
          <div className='mt-1'>
            <Combobox
              data={getBatchesOptions(batchMentorList)}
              dataKey={"value"}
              textField='mentor'
              placeholder={"Select Mentors"}
              value={mentor}
              onChange={(value) => setMentor(value)}
            />
          </div>
        </div>
      </div>
    </CustomFormModal>
  );
};

export default AddMentorToParticipant;

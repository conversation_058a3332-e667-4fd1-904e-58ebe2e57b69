import ToolTip from "../../../../../../components/sharedComponents/ToolTip";
const QuestionViewSidebar = ({ quizQuestionDetails }) => {
  const getDifficultyColor = (level) => {
    switch (level) {
      case "easy":
        return "success";
      case "medium":
        return "warning";
      case "hard":
        return "danger";
      default:
        return "secondary";
    }
  };

  return (
    <>
      {quizQuestionDetails && (
        <div className='col-lg-12 pt-sm-0 pt-lg-4 px-0 side-bar'>
          <div className='col-lg-12 difficulty-level'>
            <h4 className='font-weight-bold'>Difficulty Level</h4>
            <div
              className={`difficulty-indicator bg-${getDifficultyColor(
                quizQuestionDetails?.level,
              )}`}
            >
              {quizQuestionDetails?.level}
            </div>
          </div>

          <div className='col-lg-12 text-left d-lg-flex px-0 pt-3 align-items-center score-section '>
            <div className='col-lg-6 score-item'>
              <h5 className='font-weight-bolder text-center'>Maximum Score</h5>
              <p className='text-center mb-3'>{quizQuestionDetails?.maxScore}</p>
            </div>
            <div className='col-lg-6 score-item px-0'>
              <h5 className='font-weight-bolder text-center'>
                Negative Score
                <ToolTip
                  title='This score will be deducted from the total score if the answer is
              incorrect.'
                  color='rgba(56, 56, 56, 0.53)'
                >
                  <a href='#'>
                    <i className='bi bi-exclamation-circle m-2'></i>
                  </a>
                </ToolTip>
              </h5>
              <p className='text-center mb-3'>
                {quizQuestionDetails?.negativeScore}
              </p>
            </div>
          </div>

          <div className='col-lg-12 text-left  tags-section'>
            <h5>Tags</h5>
            <span>
              {quizQuestionDetails?.tags
                ? quizQuestionDetails?.tags
                : "You have not added any tag to this question"}
            </span>
          </div>
        </div>
      )}
    </>
  );
};

export default QuestionViewSidebar;

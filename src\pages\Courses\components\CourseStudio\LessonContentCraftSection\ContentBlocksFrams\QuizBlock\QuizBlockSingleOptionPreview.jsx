import React, { useState } from "react";

import CommonModelPopup from "../../../../../../../components/sharedComponents/CommonModelPopup";

const QuizBlockSingleOptionPreview = ({ item }) => {
  
  const [selected, setSelected] = useState("");
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isWrongAnswerModalVisible, setIsWrongAnswerModalVisible] = useState(false);

  const handleCheckboxChange = (value) => {
    setSelected(value);
  };

  const options = () => {
    var row = [];
    for (let i = 1; i <= 4; i++) {
      row.push(
        <div className="d-flex align-items-center mb-1 font-f" key={i}>
          <input
            type="checkbox"
            className="mr-3"
            name="options"
            value={item.payload && item?.payload[`option${i}`]}
            checked={selected === (item.payload && item?.payload[`option${i}`])}
            onChange={(e) => handleCheckboxChange(e.target.value)}
          />
          <label className="d-flex align-items-center mb-0">{item.payload && item?.payload[`option${i}`]}</label>
        </div>
      );
    }
    return row;
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (selected === item.payload[`${item?.payload?.correct_answer}`]) {
      setIsModalVisible(true);
    } else {
      setIsWrongAnswerModalVisible(true);
    }
    setSelected("");
  };

  const closeModal = () => {
    setIsModalVisible(false);
  };

  const closeWrongAnswerModal = () => {
    setIsWrongAnswerModalVisible(false);
  };

  return (
    <>
      <div className="border-bottom rounded-lg p-3">
        <div>
          <p className="my-3 font-f">Choose the correct answer:</p>
        </div>
        <form onSubmit={handleSubmit}>
          <div>
            <h5 className="mb-3 font-f">{item?.payload?.question}</h5>
            {options()}
          </div>
          {selected && (
            <div className="d-flex justify-content-center">
              <button type="submit" className="btn btn-primary">
                Submit
              </button>
            </div>
          )}
        </form>
      </div>
      {isModalVisible && (
        <CommonModelPopup
          model={isModalVisible}
          toggle={closeModal}
          message={"Congratulations !! your Answer is correct"}
          submitButtonText={"Okay"}
          modeltitle={" Correct Answer!"}
          type="success"
          submitButtonColor="success"
        />
      )}
      {isWrongAnswerModalVisible && (
        <CommonModelPopup
          model={isWrongAnswerModalVisible}
          toggle={closeWrongAnswerModal}
          message={"Opps !! Your Answer Is Incorrect"}
          submitButtonText={"Try Again"}
          modeltitle={"Wrong Answer!"}
          type="danger"
          submitButtonColor="danger"
        />
      )}
    </>
  );
};

export default QuizBlockSingleOptionPreview;

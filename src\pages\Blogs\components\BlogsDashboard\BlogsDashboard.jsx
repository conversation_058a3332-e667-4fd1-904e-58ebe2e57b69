import React, { useEffect, useRef, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { useNavigate } from 'react-router'

import { getSort } from '../../../../components/utils'
import { getLessons } from '../../../DaysCode/actions'
import BlogListCard from './BlogListCard'
import { deleteLesson,  getLessonDetails } from '../../../Courses/actions'
import EditBlogStatusModal from '../../../../components/Modals/EditBlogStatusModal'
import CustomFilterPopup from '../../../DaysCode/components/Settings/DaysCodeBatch/CustomFilterPopup'
import BlogListTable from './BlogListTable'
import ToolTip from '../../../../components/sharedComponents/ToolTip'
import CustomLoader from '../../../../components/sharedComponents/CustomLoader'
import DeleteModal from '../../../../components/sharedComponents/DeleteModal'
import { copyToClipboard } from '../../../../components/utils/copyToClipboard';

const BlogsDashboard = () => {

  const dispatch = useDispatch();
  const navigate = useNavigate();
  
   const { lessonsList, problemsListLoading } = useSelector((state) => state.dayscode);
   
  //const [openFilter, setOpenFilter] = useState(false);
  const [editModal, setEditModal] = useState(false);
  const [showDelModal, setShowDelModal] = useState(false);
  const [listView, setListView] = useState('grid')
  const [editType, setEditType] = useState();
 
  const selectedBLog =useRef()
  
   const items = [
    {
      key: '1',
      label: (
        <div onClick={(e) => handleManage(e, selectedBLog.current)}> Manage</div>
      ),
    },
    {
      key: '2',
      label: (
        <div onClick={(e) => handleEdit(e, selectedBLog.current)}>Change Status</div>
      ),
    },
    {
      key: '3',
      label: (
        <div onClick={(e) => toggleDel()}> Delete</div>
      ),
    },
    {
      key: '4',
      label: (
        <div onClick={(e)=>{
          e.preventDefault();
          copyToClipboard(`http://localhost:3000/blog/preview/${selectedBLog.current}`)}}> Copy Link</div>
      ),
    },
    {
      key: '5',
      label: (
        <div className="text-danger"> Report!</div>
      ),
    },
  ];


  useEffect(() => {
    dispatch(getLessons())
  }, [dispatch])

  const handleCreate = () => {
    navigate('/blog/create')
  }

  const handleEdit = (e, id) => {
    e.preventDefault()
    setEditType("changeStatus")
    dispatch(getLessonDetails(id));
    setEditModal(true);
  }

  const handleNavigation = (event, id) => {
    selectedBLog.current = id;
    // Check if the click target is not the dropdown
    if (!event.target.closest('.dropdown-trigger ,.ant-dropdown-menu ')) {
      navigate(`/blog/preview/${id}`);
    }
  };

  const handleManage = (e, id) => {
    e.preventDefault()
    navigate(`/blog/${id}/manage`)
  }


  const toggleDel = () => {
    setShowDelModal(!showDelModal);
  }

  const handleDel = () => {
    dispatch(deleteLesson(selectedBLog.current));
    setShowDelModal(false);
  }

  const handleApprove =(id)=>{
    selectedBLog.current=id;
    setEditType("approve")
    dispatch(getLessonDetails(id));
    setEditModal(true)
  }
 

  return (
    <>

      <div className='row mx-0 blogs'>
        <div className={listView === "grid" ?  "blog-container col-sm-9 col-12 ":"blog-container col-lg-9 col-12"}>
          <div className='header-blog row'>
            <div className=" d-flex flex-column justify-content-start col-6">
              <div className=" pt-3">Blogs </div>
             {/*  <div className="">Home-Blogs</div> */}
            </div>
            <div className="col-6 d-flex justify-content-end align-items-center gap-3 gap-lg-4 buttons">            
              <ToolTip message={'Table View'} id="table" placement="bottom" > 
              <i id="table" onClick={() => setListView('table')} className='p-2 mx-3 border rounded fas fa-list' />
              </ToolTip>             
              <ToolTip message={'Grid View'} id="grid" placement="bottom" >
              <i id="grid" onClick={() => setListView('grid')} className="p-2 border rounded fas fa-th-large mr-3 vis" />
              </ToolTip>
              <div className='filterr'>
                <CustomFilterPopup type={"blog"} />
              </div>
              <button className="ml-2 pt-1 btn btn-primary" onClick={handleCreate}>
                <div>Create</div>
              </button>
            </div>
          </div>
          {problemsListLoading ? <CustomLoader /> : (<>

            <div className='blog-body mt-4 row mx-0 px-0'>
              {listView === "grid" ? (<>
                {
                  lessonsList && getSort(lessonsList, 'day').map((item, i) => (
                    <div key={item._id} className="blog-card col-md-4 col-sm-6 col-12 " onClick={(event) => handleNavigation(event, item._id)} >
                      <BlogListCard lesson={item}  items={items} selectedBLog={selectedBLog}/></div>
                  ))
                }</>
              ) : (
                <div className='card-body col-12 '>
                  <div className="row mx-0 d-flex justify-content-space-between mb-4">
                    <div className="col-4 d-flex justify-content-end align-items-stretch flex-wrap mt-3">
                      <input type="text" className=" form-control form-control-solid ps-15 " placeholder="Search" />
                    </div>
                  </div>
                  <BlogListTable lessons={lessonsList}  items={items} handleApprove={handleApprove} selectedBLog={selectedBLog}/> </div>)}
            </div>
          </>)}
        </div>
      </div>
      {editModal && <EditBlogStatusModal setshowModal={setEditModal} showModal={editModal} type={editType}/>}
      {<DeleteModal onSubmit={handleDel} toggle={toggleDel} title="Delete Blog" open={showDelModal} />}
    </>
  )
}

export default BlogsDashboard
import React from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "reactstrap";

const MentorDetails = ({open,
  toggle,
  title,
  message,
  mentorlist
}) => (
 
  <Modal isOpen={open} toggle={toggle} className="delete-tutorial-modal">
  <ModalHeader toggle={toggle}>
    <div className="custom-modal-header">
      
      <h3>{}</h3>
    </div>
  </ModalHeader>

  <ModalBody className="modal-body">
    <p> {mentorlist.role}</p>
  </ModalBody>
  
</Modal>
);



// DeleteModal.propTypes = {
//   submitButtonName: PropTypes.string,
//   submitButtonColor: PropTypes.string,
//   toggle: PropTypes.func.isRequired,
//   open: PropTypes.bool.isRequired,
// };

 

  export default  MentorDetails;

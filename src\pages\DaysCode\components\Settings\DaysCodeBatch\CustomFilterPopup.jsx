import { <PERSON><PERSON>, Space } from "antd";
import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Combobox } from "react-widgets";
import { FaFilter } from "react-icons/fa6";

import {
  getProblemTypeOptions,
  problemTypeOptions,
} from "../Problems/AddProblem/helper";
import {
  LimitOptions,
  StatusOptions,
  batchContentRoleOptions,
} from "../helpers";
import { getOptions, getSort } from "../../../../../components/utils";
import {
  getAllSubmissions,
  getBatchParticipants,
  getBatchProblems,
  getCodeBatches,
  // getDaysUsersListOptions,
  // getSubmissionsByBatchProblem,
} from "../../../actions";

const CustomFilterPopup = ({
  onChangeProblemType,
  setBatchContentStatus,
  type,
  setRole,
  setFilterQuery,
  setFilterLimit,
  setFilterStatus,
  filterLimit,
  filterQuery,
  filterStatus,
  batchId,
  onChangeProblemOption,
  onChangeUserOption,
}) => {

  const dispatch = useDispatch();
  
  const { batchParticipantsList, batchProblemList } = useSelector(
    (state) => state.dayscode,
  );

  const [isOpen, setIsOpen] = useState(false);
  const [selectedValue, setSelectedValue] = useState();
  const [status, setStatus] = useState();
  const [selectedRole, setSelectedRole] = useState();
  const [user, setUser] = useState();
  const [problem, setProblem] = useState();
  const [batchDetails, setBatchDetails] = useState([]);
  const [batch, setBatch] = useState();
  const [selectUser, setSelectUser] = useState(false);


  useEffect(() => {
    if (type === "Submissions") {
      dispatch(
        getBatchProblems({
          id: batchId,
        }),
      );
      dispatch(getBatchParticipants({ batch: batchId }));
    }
    if (type === "Submission") {
      dispatch(getCodeBatches({ schedule: "all" })).then((res) => {
        setBatchDetails(res?.batches);
      });
    }
    if (isOpen) {
      document.addEventListener("click", handleOutsideClick);
    }
    return () => {
      document.removeEventListener("click", handleOutsideClick);
    };
  }, [isOpen, user, problem ]);

  const handleOk = () => {
    if (type === "Submissions" || type === "Submission") {
      onChangeProblemOption(problem);
      onChangeUserOption(user);
    } else {
      if (type === "Problem") {
        selectedValue
          ? onChangeProblemType(selectedValue)
          : onChangeProblemType("Assignment");
      }
      if (type === "Participant") {
      } else {
        type === "Mentor"
          ? setRole(selectedRole.value)
          : status
          ? setBatchContentStatus(status)
          : setBatchContentStatus("public");
      }
      setIsOpen(false);
    }
  };

  const handleCancel = () => {
    setStatus();
    setUser();
    setProblem();
    setSelectedValue();
    setBatch();
    setSelectUser();
    setSelectedRole();

    if (type === "Participant") {
      setFilterStatus();
      setFilterLimit();
      setFilterQuery("");
    }
    if (type === "Submission") {
      dispatch(getAllSubmissions());
    }
    // setIsOpen(false);
  };
  const handleRadioChange = (event) => {
    setStatus(event.target.value);
  };

  const handleComboboxChange = (value) => {
    setSelectedValue(value);
    setSelectedRole(value);
  };
  const handleOutsideClick = (event) => {
    if (!event.target.closest(".filter")) {
      setIsOpen(false);
    }
  };

  const getBatchOptions = (batches) => {
    const options = [];
    if (batches) {
      getSort(batches, "_id").map((item, i) => {
        return options.push({ batch: item.title, value: item._id });
      });
    }
    return options;
  };

  const onChangeBatchOption = (e) => {
    setBatch(e);
    setSelectUser(true);
    dispatch(getBatchParticipants({ batch: e.value }));
    dispatch(getBatchProblems({ id: e.value }));
  };

  const getUsersOptions = (users) => {
    const options = [];
    if (users) {
      getSort(users, "firstName").map((item) => {
        return options.push({
          user: item.codeUser.user?.firstName,
          value: item._id,
        });
      });
    }
    return options;
  };

  const getProlemOptions = (problems) => {
    const options = [];
    if (problems) {
      getSort(problems, "day").map((item, i) => {
        return options.push({
          problem: `Day: ${i} | ${item.problemDetails?.title}`,
          value: item.problemDetails?._id,
        });
      });
    }
    return options;
  };

  return (
    <>
      <div className='filter-Modal-content'>
        <Space>
          <Button
            className='filter-modal-button'
            onClick={() => setIsOpen(!isOpen)}
          >
            <FaFilter className='mr-2' />
            Filter
          </Button>
        </Space>
        {isOpen ? (
          <>
            <div className='filter'>
              <div>
                <span>Filter Options</span>
              </div>
              <div className='empty-line'></div>

              {type === "problem" ? (
                <div className='mb-2'>
                  <label className=' form-label'>Problem Type:</label>
                  <Combobox
                    data={getProblemTypeOptions(problemTypeOptions)}
                    dataKey={"value"}
                    textField='problem_type'
                    placeholder={"Select Problem Type"}
                    value={selectedValue}
                    onChange={handleComboboxChange}
                  />
                </div>
              ) : (
                ""
              )}
              {type === "Mentor" ? (
                <div className='mb-2 '>
                  <label className='form-label'>Role:</label>
                  <Combobox
                    data={getOptions(batchContentRoleOptions, "role")}
                    dataKey={"value"}
                    textField='role'
                    placeholder={"role"}
                    value={selectedRole}
                    onChange={handleComboboxChange}
                  />
                </div>
              ) : (
                ""
              )}

              {type === "Participant" ? (
                <>
                  <div className='mb-2 '>
                    <label className='form-label'>Status:</label>
                    <Combobox
                      data={getOptions(StatusOptions, "status")}
                      dataKey={"value"}
                      textField='status'
                      placeholder={"status"}
                      value={filterStatus}
                      onChange={(value) => {
                        if (value === "all") {
                          setFilterStatus(null);
                        } else {
                          setFilterStatus(value.value);
                        }
                      }}
                    />
                  </div>
                  <div className='mb-2 '>
                    <label className='mr-4 form-label'>Limit:</label>
                    <Combobox
                      data={getOptions(LimitOptions, "limit")}
                      dataKey={"value"}
                      textField='limit'
                      placeholder={"limit"}
                      value={filterLimit}
                      onChange={(value) => {
                        if (value === "all") {
                          setFilterLimit(null);
                        } else {
                          setFilterLimit(value.value);
                        }
                      }}
                    />
                  </div>
                  <div className=''>
                    <label className='form-label'>Search Name</label>
                    <input
                      className='form-control'
                      type='text'
                      name='search'
                      value={filterQuery}
                      onChange={(e) => setFilterQuery(e.target.value)}
                    />
                  </div>
                </>
              ) : (
                ""
              )}
              {type === "Submission" ? (
                <div className=''>
                  <label className='form-label'>Select Batch</label>
                  <Combobox
                    data={getBatchOptions(batchDetails)}
                    dataKey={"value"}
                    textField='batch'
                    placeholder={"Select Batch"}
                    value={batch}
                    onChange={(value) => onChangeBatchOption(value)}
                  />
                </div>
              ) : (
                ""
              )}
              {type === "Submissions" ||
              (type === "Submission" && selectUser) ? (
                <>
                  <div className='mt-3'>
                    <label className='form-label'>Select User</label>
                    <Combobox
                      data={getUsersOptions(batchParticipantsList)}
                      dataKey={"value"}
                      textField='user'
                      placeholder={"Select User Name"}
                      value={user}
                      onChange={(value) => setUser(value.value)}
                      disabled={problem && true}
                    />
                  </div>
                  <div className='mt-3'>
                    <label className='form-label'>Select Problem</label>
                    <Combobox
                      data={getProlemOptions(batchProblemList)}
                      dataKey={"value"}
                      textField='problem'
                      placeholder={"Select Problem Name"}
                      value={problem}
                      onChange={(value) => setProblem(value.value)}
                      disabled={user && true}
                    />
                  </div>
                </>
              ) : (
                ""
              )}

              {type === "Mentor" ||
              type === "Participant" ||
              type === "Submissions" ||
              type === "Submission" ? (
                ""
              ) : (
                <div className='mt-3 '>
                  <label className='form-label'>Status:</label>
                  <div className='d-flex'>
                    <div className='form-check'>
                      <input
                        className='form-check-input'
                        type='radio'
                        id='public'
                        value='public'
                        checked={status === "public"}
                        onChange={handleRadioChange}
                      />
                      <label className='form-check-label ' htmlFor='public'>
                        Public
                      </label>
                    </div>
                    <div className='form-check'>
                      <input
                        className='form-check-input'
                        type='radio'
                        id='draft'
                        value='draft'
                        checked={status === "draft"}
                        onChange={handleRadioChange}
                      />
                      <label className='form-check-label' htmlFor='draft'>
                        Draft
                      </label>
                    </div>
                  </div>
                </div>
              )}
              <div className='mt-4 filter-buttons'>
                <button
                  className='btn btn-secondary mx-2'
                  onClick={handleCancel}
                >
                  Reset
                </button>
                <button className='btn btn-primary mx-2' onClick={handleOk}>
                  Apply
                </button>
              </div>
            </div>
          </>
        ) : (
          ""
        )}
      </div>
    </>
  );
};

export default CustomFilterPopup;

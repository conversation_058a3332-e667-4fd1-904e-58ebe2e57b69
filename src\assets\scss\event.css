.event-side-nav ul {
  display: flex;
  flex-direction: column;
}
.event-side-nav ul li {
  list-style: none;
}
.event-side-nav ul li a {
  padding: 10px 24px;
  display: flex;
  align-items: center;
  gap: 12px;
  color: #626c70;
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
}
.event-side-nav ul li a:hover {
  background-color: rgba(195, 195, 195, 0.1411764706);
}

.event-page-header {
  min-height: 400px;
  background-color: #603f83;
}
.event-page-header .event-heading {
  color: #c7d3d4;
  font-size: 55px;
  font-weight: 300;
  font-family: "Quicksand", sans-serif;
}
.event-page-header .event-sub-heading {
  color: #c7d3d4;
  font-size: 20px;
  font-weight: 200;
}
.event-page-header .event-date-time {
  color: yellow;
  font-size: 18px;
  font-weight: 400 !important;
}
.event-page-header .event-header-tag {
  font-size: 14px;
  color: #000;
  padding: 2px 5px;
  border: 1px solid #726f77;
  border-radius: 50px;
  margin-right: 7px;
  text-decoration: none;
  text-align: center;
  display: inline-block;
}
.event-page-header .event-platform {
  background-color: #f9fbfc;
  border-radius: 5px;
  color: #30006d;
  font-weight: 500;
  font-size: 15px;
}
.event-page-header .event-catagory {
  background-color: #f9fbfc;
  border-radius: 5px;
  font-size: 15px;
  font-weight: 500;
  color: #30006d;
}

.event-social-icons {
  display: flex;
}
.event-social-icons span {
  display: block;
  border-radius: 50%;
  width: 34px;
  height: 34px;
  padding: 8px;
  background: #fff;
  border: 1px solid #f9fbfc;
  color: #000;
  text-align: center;
  margin-right: 20px;
  line-height: 20px;
}

.event-reg-section {
  background-color: #f9fbfc;
}

.event-description p {
  font-size: 20px;
}

.register-text {
  font-size: 30px;
  font-family: 300 !important;
}

.custom-button {
  background-color: #ffffff;
  border-radius: 30px;
  color: #30006d;
  font-weight: 600;
  font-family: "Fira Code", monospace;
  padding: 8px !important;
  border: solid 1px rgba(110, 83, 189, 0.9058823529) !important;
  margin-top: 35px;
  width: 170px;
  height: 46px;
  position: relative;
  cursor: pointer;
  transition: all 0.2s ease;
  overflow: hidden;
  align-items: center;
}
.custom-button > span {
  text-align: center;
  font-weight: 600;
  font-family: "Fira Code", monospace;
  align-items: center;
  color: #30006d;
  font-weight: bold;
  font-size: 16px;
  position: relative;
  z-index: 1;
  transition: all 0.5s ease;
  white-space: nowrap;
}
.custom-button:before {
  content: "";
  width: 100%;
  height: 100%;
  background-color: rgba(110, 83, 189, 0.9058823529);
  position: absolute;
  transition: all 0.5s ease;
}
.custom-button:hover span {
  color: #ffffff !important;
}
.custom-button:hover span i {
  color: #ffffff;
}

.reg-button {
  background-color: #30006d;
  color: #ffffff;
  font-weight: 400;
  border-radius: 7px;
  align-items: center;
  min-height: 50px;
  min-width: 100%;
}

.custom-button:before {
  top: 0;
  left: -210px;
  color: #ffffff;
}
.custom-button:hover:before {
  left: 0;
  color: #ffffff;
}

.create-event-promo {
  min-height: 100px;
  background-color: #30006d;
}
.create-event-promo h1 {
  color: white;
  font-size: 23px;
}

.event-carousel {
  background: linear-gradient(180deg, rgb(255, 255, 255) 11%, rgb(255, 255, 255) 35%) !important;
}
.event-carousel h1 {
  color: rgb(23, 3, 54);
}
.event-carousel h2 {
  color: rgb(20, 130, 0);
}
.event-carousel .qoute {
  color: #000;
}
.event-carousel .qoute h6 {
  color: rgb(43, 54, 45);
}

.event-status-head {
  border-bottom: 1px solid #ddd;
}
.event-status-head .event-state {
  padding: 15px;
}
.event-status-head .event-state h1 {
  font-size: 35px;
  color: rgba(23, 23, 26, 0.8156862745);
}

.event-list-section {
  background-color: #f8f9fa;
}

.event-list-nav {
  justify-content: flex-end;
  display: flex;
  align-items: center;
}
.event-list-nav .filter-icon {
  font-size: 30px;
}
.event-list-nav .my-events {
  background-color: rgba(0, 0, 0, 0.2);
  padding: 5px 10px;
  border-radius: 5px;
  cursor: pointer;
}

.event-filter {
  position: absolute;
  top: 100%;
  right: 10%;
  z-index: 9;
  transition: all 0.5s ease-in-out;
  min-width: 50%;
  min-height: 300px;
  border-radius: 5px;
}

.event-promot-section {
  background-color: #30006d;
  color: #ffffff;
  min-height: 400px;
}
.event-promot-section h3 {
  font-size: 25px;
  color: white;
}
.event-promot-section p {
  color: #ffffff;
  font-size: 16px;
  padding: 20px 50px;
}

.event-card {
  border: solid 1px rgba(204, 204, 204, 0.842);
  border-radius: 10px;
  height: 100%;
  text-decoration: none;
  position: relative;
  cursor: pointer;
}
.event-card img {
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
}
.event-card:hover {
  box-shadow: 0 3px 6px 0 rgba(0, 0, 0, 0.2);
  text-decoration-style: none !important;
}
.event-card:hover .hover-sutter {
  height: 40%;
  padding-top: 10px;
  background-color: rgba(47, 0, 109, 0.4470588235);
}
.event-card .hover-sutter {
  text-align: center;
  align-items: center;
  justify-content: center;
  position: absolute;
  width: 100%;
  height: 0;
  overflow: hidden;
  border-bottom-left-radius: 7px;
  border-bottom-right-radius: 7px;
  background-color: #30006d;
  transition: all 0.5s ease-in-out;
  bottom: 0;
  line-height: 18px;
  color: #fff;
}
.event-card .hover-sutter .btn {
  margin-top: 40px;
}
.event-card .event-card-title {
  font-size: 18px;
  color: rgba(23, 23, 26, 0.8156862745);
  text-decoration: none;
}
.event-card .event-card-sub-heading {
  font-size: 16px;
  color: rgb(59, 59, 59);
}
.event-card .reg-status-block {
  position: absolute;
  top: 5%;
  right: 0;
  color: white;
  padding: 8px 10px;
  border-top-left-radius: 15px;
  border-bottom-left-radius: 15px;
  background-color: #e6f4ea;
  color: #1e8e3e;
  font-size: 10px;
  line-height: 11px;
}
.event-card .event-host-action-toggle {
  position: absolute;
  top: 5%;
  right: 0;
  color: white;
  padding: 8px 10px;
  border-top-left-radius: 15px;
  border-bottom-left-radius: 15px;
  background-color: #e6f4ea;
  color: #1e8e3e;
  font-size: 10px;
  line-height: 11px;
}
.event-card .date-block {
  position: absolute;
  bottom: 50%;
  right: 5%;
  padding: 5px 10px;
  border-radius: 7px;
  background-color: #1671a6;
  text-align: center;
  color: #ffffff;
}
.event-card .date-block h1 {
  font-size: 20px;
  margin-bottom: 0;
  color: #ffffff;
}
.event-card .date-block p {
  margin-bottom: 0;
  font-size: 13px;
}
.event-card p {
  font-size: 15px;
  color: rgb(104, 103, 103);
  margin-bottom: 7px;
}
.event-card .event-card-footer {
  display: grid;
  align-items: stretch !important;
  bottom: 5%;
  width: 100%;
}

.featured-event-card {
  border: solid 1px rgba(204, 204, 204, 0.842);
  border-radius: 10px;
  height: 100%;
  text-decoration: none;
  position: relative;
  cursor: pointer;
}
.featured-event-card img {
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
}
.featured-event-card:hover {
  box-shadow: 0 3px 6px 0 rgba(0, 0, 0, 0.2);
  text-decoration-style: none !important;
}
.featured-event-card:hover .featured-card-hover-sutter {
  width: 100%;
  height: 100%;
  padding-top: 35px;
  background-color: rgba(47, 0, 109, 0.4470588235);
}
.featured-event-card .featured-card-hover-sutter {
  text-align: center;
  align-items: center;
  justify-content: center;
  position: absolute;
  width: 0%;
  height: 0%;
  overflow-y: hidden;
  overflow-x: hidden;
  border-top-right-radius: 7px;
  border-bottom-right-radius: 7px;
  background-color: #30006d;
  transition: all 0.5s ease-in-out;
  left: 0;
  top: 0;
  bottom: 0;
  z-index: 200;
  line-height: 18px;
  color: #fff;
}
.featured-event-card .featured-card-hover-sutter .btn {
  margin-top: 40px;
}
.featured-event-card .featured-event-img {
  height: 100%;
  width: 100%;
  min-height: 200px;
  background-size: cover;
  background-position: 50%;
  background-color: #f1f3f4;
  background-position: center center;
  background-repeat: no-repeat;
  border-top-left-radius: 10px !important;
  border-bottom-left-radius: 10px !important;
  border-top-right-radius: 0 !important;
}
.featured-event-card .featured-event-title {
  font-size: 30px;
  color: rgba(23, 23, 26, 0.8156862745);
  text-decoration: none;
}
.featured-event-card .event-card-sub-heading {
  font-size: 16px;
  color: rgb(59, 59, 59);
}
.featured-event-card .reg-status-block {
  position: absolute;
  top: 5%;
  right: 0;
  color: white;
  padding: 8px 10px;
  border-top-left-radius: 15px;
  border-bottom-left-radius: 15px;
  background-color: #e6f4ea;
  color: #1e8e3e;
  font-size: 10px;
  line-height: 11px;
}
.featured-event-card .date-block {
  position: absolute;
  bottom: 5%;
  right: 10%;
  padding: 5px 10px;
  border-radius: 7px;
  background-color: #1671a6;
  text-align: center;
  color: #ffffff;
}
.featured-event-card .date-block h1 {
  font-size: 20px;
  margin-bottom: 0;
  color: #ffffff;
}
.featured-event-card .date-block p {
  margin-bottom: 0;
  font-size: 13px;
}
.featured-event-card p {
  font-size: 15px;
  color: rgb(104, 103, 103);
  margin-bottom: 7px;
}
.featured-event-card .event-card-footer {
  display: grid;
  align-items: stretch !important;
  bottom: 5%;
  width: 100%;
}

.fuck-line {
  text-decoration: none !important;
}

.event-tag {
  font-size: 12px;
  color: #000;
  padding: 2px;
  border-radius: 50px;
  margin-right: 7px;
  margin-top: 7px;
  text-decoration: none;
  text-align: center;
  display: inline-block;
}
.event-tag:nth-child(1) {
  color: #694cea;
  border: solid 0.5px #694cea;
}
.event-tag:nth-child(2) {
  color: orangered;
  border: solid 0.5px orangered;
}
.event-tag:nth-child(3) {
  color: olivedrab;
  border: solid 0.5px olivedrab;
}

.event-dashboard .back-btn {
  width: 50px;
  height: 50px;
  color: #000;
  border-radius: 100%;
}
.event-dashboard .back-btn:hover {
  background-color: #673de6;
  color: #fff;
}

.event-dashboard-header ul {
  margin-top: 20px;
  margin-bottom: 0;
  display: flex;
  padding: 0;
  border-bottom: 1px solid #ddd;
}
.event-dashboard-header ul li {
  list-style: none;
}
.event-dashboard-header ul li span {
  cursor: pointer;
  font-weight: 500;
  display: block;
  padding: 6px 25px;
  margin: 5px;
  color: #000;
  font-size: 14px;
  border: 1px solid #000;
  border-radius: 20px;
}
.event-dashboard-header ul li span.active {
  border: 1px solid #673de6;
  color: #673de6;
  font-weight: 700;
  font-size: 16px;
}
.event-dashboard-header ul li span.active::before {
  width: 3px;
  background-color: #30006d;
}
.event-dashboard-header ul li span:hover {
  background-color: #673de6;
  border: 1px solid #673de6;
  color: #fff;
}
.event-dashboard-header ul li span:focus {
  text-decoration: none;
}
.event-dashboard-header ul li span .overview-nav-img {
  margin-left: 8px;
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.timeline-header .add-timeline-btn {
  font-size: 16px;
  font-weight: 600;
  background-color: #673de6;
}
.timeline-header .add-timeline-btn:hover {
  color: #673de6;
  background-color: transparent;
  border: 1px solid #673de6;
}

.add-new-timeline .btn-list {
  display: flex;
  justify-content: center;
  align-items: center;
}
.add-new-timeline .btn-list .btn-secondary {
  background-color: #4e5557;
}
.add-new-timeline .btn-list .btn-secondary:hover {
  color: #4e5557;
  background-color: transparent;
  border: 1px solid #4e5557;
}
.add-new-timeline .btn-list .btn-primary {
  background-color: #673de6;
}
.add-new-timeline .btn-list .btn-primary:hover {
  color: #673de6;
  background-color: transparent;
  border: 1px solid #673de6;
}

.timeline-card .main-card {
  display: flex;
  justify-content: space-around;
  align-items: center;
}
.timeline-card .main-card .title {
  width: 450px;
  height: 180px;
  margin: 20px;
  padding: 30px;
  box-shadow: 0 0 3px #626c70;
  border-radius: 20px;
  overflow: auto;
  cursor: pointer;
}
.timeline-card .main-card .title .timeline-delete {
  display: flex;
  justify-content: space-between;
}
.timeline-card .main-card .title .timeline-delete .delete-btn {
  width: 26px;
  height: 26px;
  color: #673de6;
}
.timeline-card .main-card .title .timeline-delete .delete-btn:hover {
  cursor: pointer;
  color: #000;
}
.timeline-card .main-card .title h6 {
  color: #4e5557;
  text-align: justify;
}
.timeline-card .main-card .mid-style {
  height: 100%;
  display: flex;
  align-items: center;
  gap: 30px;
  color: #673de6;
}
.timeline-card .main-card .mid-style .vertical-line {
  width: 1px;
  height: 100%;
  border: 1px solid #673de6;
}
.timeline-card .main-card .date-time {
  margin: 0;
  padding: 0;
}
.timeline-card .main-card .date-time .date {
  display: flex;
  gap: 20px;
}
.timeline-card .main-card .date-time .time {
  display: flex;
  gap: 20px;
}

.speaker-header {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  padding: 30px 10px 30px 10px;
  background-color: #f4f2f7;
  border-radius: 12px;
}
.speaker-header .add-speaker-btn {
  background-color: #673de6;
  font-weight: 600;
  color: #fff;
}
.speaker-header .add-speaker-btn:hover {
  color: #673de6;
  background-color: transparent;
  border: 1px solid #673de6;
}

.no-speaker {
  width: 100vw;
  height: 60vh;
  display: flex;
  justify-content: center;
  align-items: center;
}

.speaker-card {
  min-height: 260px;
  margin: 10px;
  padding: 20px;
  border: 1px solid #673de6;
  border-radius: 15px;
  text-align: center;
}
.speaker-card .dlt-card-btn {
  width: 20px;
  height: 20px;
  color: #673de6;
  cursor: pointer;
}
.speaker-card .dlt-card-btn:active {
  background-color: #673de6;
}
.speaker-card .remove-btn:active {
  background-color: #673de6;
}

.search-section {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 10px;
}
.search-section .add-guest {
  padding: 5px 10px;
  color: #fff;
  background-color: #673de6;
  font-weight: 600;
  border: none;
  border-radius: 5px;
}
.search-section .add-guest:hover {
  background-color: transparent;
  color: #673de6;
  border: 1px solid #673de6;
}

.event-speaker-search-table .speaker-search-card {
  padding: 10px;
  margin: 10px;
  border: 1px solid #673de6;
  border-radius: 10px;
  display: flex;
  justify-content: space-around;
  align-items: center;
}
.event-speaker-search-table .speaker-search-card h6 {
  font-size: 14px;
}
.event-speaker-search-table .speaker-search-card p {
  font-size: 10px;
}
.event-speaker-search-table .add {
  color: #673de6;
  cursor: pointer;
}

.sponsor-header {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
}
.sponsor-header .add-sponsor-btn {
  background-color: #673de6;
  font-weight: 600;
  color: #fff;
}
.sponsor-header .add-sponsor-btn:hover {
  color: #673de6;
  background-color: transparent;
  border: 1px solid #673de6;
}

.event-sponsor-search-table .sponsor-search-card {
  padding: 10px;
  margin: 10px;
  border: 1px solid #673de6;
  border-radius: 10px;
  display: flex;
  justify-content: space-around;
  align-items: center;
}
.event-sponsor-search-table .sponsor-search-card h6 {
  font-size: 14px;
}
.event-sponsor-search-table .sponsor-search-card p {
  font-size: 10px;
}
.event-sponsor-search-table .add {
  color: #673de6;
  cursor: pointer;
}

.sponsor-card {
  width: 330px;
  height: 260px;
  margin: 10px;
  padding: 20px;
  box-shadow: 0 0 3px #626c70;
  border-radius: 15px;
  text-align: center;
}
.sponsor-card .dlt-card-btn {
  width: 20px;
  height: 20px;
  color: #673de6;
  cursor: pointer;
}
.sponsor-card .remove-btn:active {
  background-color: #673de6;
}

.final-add-btn {
  display: flex;
  gap: 10px;
}
.final-add-btn button {
  background-color: #694cea;
  border: 1px solid #673de6;
}
.final-add-btn button:hover {
  background-color: transparent;
  color: #673de6;
  border: 1px solid #673de6;
}

.form-group {
  width: 100%;
  margin-top: 10px;
  display: flex;
  gap: 30px;
  justify-content: center;
  align-items: center;
}
.form-group .add-new-btn {
  background-color: #673de6;
}
.form-group .add-new-btn:hover {
  background-color: transparent;
  color: #673de6;
  border: 1px solid #673de6;
}
.form-group .add-new-btn:active {
  background-color: #673de6;
  color: #fff;
}

.form-page {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.form-page h3 {
  text-align: center;
  margin: 10px;
}
.form-page form {
  margin: 10px;
}
.form-page .input-field {
  margin: 0 5px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.form-page .input-field .label {
  font-weight: 600;
  margin-top: 20px;
}
.form-page .save-btn {
  font-size: 20px;
  padding: 5px;
  margin: 15px 20px;
  text-align: center;
  background-color: #673de6;
  color: #fff;
}
.form-page .save-btn:hover {
  cursor: pointer;
  background-color: transparent;
  border: 1px solid #673de6;
  color: #673de6;
}

.dot-icon {
  cursor: pointer;
  color: #673de6;
}

.icon {
  margin-right: 5px;
}

.move-btn {
  background-color: #673de6;
}
.move-btn:active {
  background-color: #673de6;
  color: #fff;
}

.dlt-btn {
  background-color: #673de6;
}
.dlt-btn:active {
  background-color: #673de6;
  color: #fff;
}

.edit-btn {
  background-color: #673de6;
}
.edit-btn:active {
  background-color: #673de6;
  color: #fff;
}

.form-check {
  margin: 5px 10px;
}
.form-check .form-check-label {
  margin-left: 5px;
}

.new-form {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 15px;
}
.new-form .cancel-form {
  width: 100px;
  padding: 10px;
  background-color: #4e5557;
  color: #fff;
  border-radius: 5px;
}
.new-form .cancel-form:hover {
  color: #4e5557;
  background-color: transparent;
  border: 1px solid #4e5557;
}
.new-form .add-form {
  width: 150px;
  padding: 10px;
  background-color: #673de6;
  color: #fff;
  border-radius: 5px;
}
.new-form .add-form:hover {
  cursor: pointer;
  background-color: transparent;
  border: 1px solid #673de6;
  color: #673de6;
}

.event-dashboard-header::-webkit-scrollbar-track {
  border-radius: 15px;
  background-color: #f1efef;
}
.event-dashboard-header::-webkit-scrollbar {
  width: 3px;
  height: 1px;
  background-color: #fff;
}
.event-dashboard-header::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background-color: #c3c3c3;
}

.link-gift-icon {
  color: #726f77;
  font-size: 65px;
}

.form-shadow {
  inset: 0px;
  box-shadow: 0px 2px 10px rgba(0, 0, 0, 0.12);
  border-radius: 2px;
  background: rgb(255, 255, 255);
}

.cancel-img-btn:hover {
  font-size: 20px;
  background-color: #694cea;
  padding: 5px;
  color: #ddd;
  border-radius: 50%;
}

.event-speaker-search-table .speaker-card:hover {
  background-color: #f9fbfc;
}

.event-dashboard-side-nav ul {
  list-style: none;
  padding: 0;
}
.event-dashboard-side-nav ul li {
  padding: 10px;
  border: 2px solid "Quicksand", sans-serif !important;
}
.event-dashboard-side-nav ul li .side-img {
  margin-right: 12px;
}
.event-dashboard-side-nav ul li:hover {
  background-color: rgba(195, 195, 195, 0.1411764706);
  cursor: pointer;
}
.event-dashboard-side-nav ul li span.active {
  color: #673de6;
}

@media (max-width: 767px) {
  .event-page-header .event-heading {
    font-size: 40px;
  }
  .event-page-header .event-sub-heading,
  .event-page-header .event-organizer {
    font-size: 18px;
    font-family: 300 !important;
  }
  .event-page-header .event-date-icon {
    font-size: 20px;
  }
  .event-description p {
    font-size: 16px;
  }
  .event-status-head .event-state {
    padding: 10px;
  }
  .event-status-head .event-state h1 {
    font-size: 25px;
  }
  .reg-button {
    min-width: 80%;
  }
  .event-carousel h1 {
    font-size: 60px;
    text-align: left;
  }
  main {
    justify-content: space-between;
  }
  .left {
    order: 2;
  }
  .right {
    order: 1;
  }
  .event-filter {
    top: 130%;
    right: 10%;
    min-width: 70%;
    min-height: 300px;
  }
  .event-card {
    position: relative;
  }
  .event-card .reg-status-block,
  .event-card .event-host-action-toggle {
    right: 0;
  }
  .event-promot-section {
    background-color: #30006d;
    color: #ffffff;
    min-height: 400px;
  }
  .event-promot-section h2 {
    font-size: 22px;
    text-align: center;
  }
  .event-promot-section h3 {
    text-align: center;
    font-size: 20px;
    color: white;
  }
  .event-promot-section p {
    color: #ffffff;
    font-size: 13px;
    padding: 10px 20px;
  }
}/*# sourceMappingURL=event.css.map */
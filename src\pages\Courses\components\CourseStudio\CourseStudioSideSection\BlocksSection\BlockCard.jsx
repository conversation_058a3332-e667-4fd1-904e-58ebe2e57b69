import React from "react";

const BlockCard = ({
  icon,
  title,
  description,
  type,
  handleCreateContentBlock,
}) => {
  return (
    <>
      <div
        className="row mx-2 my-2 block"
        onClick={() => handleCreateContentBlock(type)}
      >
        <div className="col-12 d-flex px-1 py-2">
          <div className="d-flex align-items-center">
            <i className={icon} />
          </div>
          <div className="d-flex flex-column">
            <p className="mb-0 title">{title}</p>
            <p className="mb-0 description">{description}</p>
          </div>
        </div>
      </div>
    </>
  );
};
export default BlockCard;

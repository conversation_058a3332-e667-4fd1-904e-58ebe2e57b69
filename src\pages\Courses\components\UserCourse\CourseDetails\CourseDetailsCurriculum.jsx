import React, { useState } from "react";
import { useDispatch } from "react-redux";
import { CiLock } from "react-icons/ci";
import { CgNotes } from "react-icons/cg";
import { Progress } from "reactstrap";
import { RiCheckboxCircleLine } from "react-icons/ri";
import { Link } from "react-router-dom";

import { enrollCoursebyuser } from "../../../actions";
import CourseUserEnrollModel from "./CourseUserEnrollModel";


const CourseDetailsCurriculum = ({
  courseDetails,
  totalCourseLessons,
  courseProgress,
}) => {
  const dispatch = useDispatch();

  const [showModel, setShowModel] = useState(false);
  const toggle = () => {
    setShowModel(!showModel);
  };

  // Enroll Course By User----------

  const handleEnrollCourseByUser = (courseId) => {
    dispatch(enrollCoursebyuser({ courseId })).then((res) => {
      if (res && res.success) {
        window.location.href = "/courses/my";
      } else {
        toggle();
      }
    });
  };

  return (
    <>
      <div className="p-2 course-details-curriculum">
        <div className="p-2 course-details-progress">
          <div className="row mx-0 align-items-center">
            <div className="col-12 col-md-8 col-lg-8 col-sm-12">
              <p className=" fw-normal fs-6">
                {courseProgress?.progress ? "Started" : "Not Started"}
              </p>
              <Progress
                style={{
                  height: "8px",
                  backgroundColor: "#c3c3c3",
                  width: "304px",
                }}
                value={courseProgress?.progress}
              />
            </div>
            <div className="col-12 col-md-4 col-lg-4 col-sm-12 mt-3">
              {courseProgress?.progress > 0 ? (
                <Link to="/courses/my">
                  <button
                    type="button"
                    className="btn  fs-6 fw-bold text-dark text-centet p-3"
                  >
                    {" "}
                    Resume Course
                  </button>{" "}
                </Link>
              ) : courseProgress?.progress === 100 ? (
                <Link to="/courses/my">
                  <button
                    type="button"
                    className="btn  fs-6 fw-bold text-dark text-centet p-3"
                  >
                    Course Completed{" "}
                  </button>
                </Link>
              ) : (
                <button
                  type="button"
                  className="btn  fs-6 fw-bold text-dark text-centet p-3"
                  onClick={() => toggle()}
                >
                  Enroll Course
                </button>
              )}
            </div>
          </div>
        </div>
        <div className="curriculum">
          <h4 className="fw-bold ">Curriculum</h4>
          <ol className="p-2">
            {totalCourseLessons &&
              totalCourseLessons.map((lesson, index) => {
                return (
                  <div key={index}>
                    <span>
                      {lesson.isCompleted === true ? (
                        <RiCheckboxCircleLine className="tickMark" />
                      ) : lesson.isCompleted === false ? (
                        <CiLock className="clock" />
                      ) : (
                        <CiLock className="clock" />
                      )}
                      <li>{lesson?.name}</li>
                    </span>
                    <p>
                      <CgNotes />
                      {lesson.duration ? lesson.duration : "0"}{" "}mins.
                    </p>
                  </div>
                );
              })}
          </ol>
        </div>
      </div>
      <CourseUserEnrollModel
        model={showModel}
        toggle={toggle}
        courseDetails={courseDetails}
        handleEnrollCourseByUser={handleEnrollCourseByUser}
      />
    </>
  );
};

export default CourseDetailsCurriculum;

import React, { useEffect, useState } from "react";
import { Field, reduxForm } from "redux-form";

import {
  renderInputField,
  renderSelectField,
} from "../../../../components/sharedComponents/ReduxFormFields";
import BannerImageUpload from "../../../../components/sharedComponents/BannerImageUpload";
import { getOptions } from "../../../../components/utils";
import { categoryOptions } from "../../../../components/utils/selectOptions";
import { useDispatch, useSelector } from "react-redux";
import { editLesson } from "../../../Courses/actions";

const BlogSettingsSection = ({ handleSubmit, submitting, initialize }) => {
 
  const dispatch = useDispatch();
  const { lessonDetails } = useSelector((state) => state.course);

  const [uplodedImg, setUploadedImg] = useState("");
   // const [ price, setPrice ] = useState(0)
  // const [ discountedPrice, setDiscountedPrice ] = useState(0)
  // const prices = [ 99, 199, 299, 399, 499, 599, 699, 799, 899, 999 ]
  // const discountedPrices = prices.filter(p => p < price)

  useEffect(() => {
    initialize(lessonDetails);
  }, [lessonDetails]);

  const onSubmit = (value) => {
    let formData = {
      ...value,
      // other data
      thumbnail: uplodedImg,
    };
    dispatch(editLesson(formData));
  };

  return (
    <>
      <div className="row mx-0 setting-section border border-bottom-0">
        <div className="col-12 px-0">
          <div className="setting-header">
            <h4>Settings</h4>
            <p>
              Choose a catchy title for your course and tell your students what
              to expect from it. And don't forget to set a price for your
              course;
            </p>
          </div>
          <div className="setting-list px-2 py-3">
            <form onSubmit={handleSubmit(onSubmit)}>
              <Field
                type="text"
                name="name"
                label="Title"
                placeholder="My New Course"
                component={renderInputField}
                // validate={ [ required ] }
              />
              <Field
                type="text"
                name="summary"
                label="Summary"
                placeholder="From Zero to Hero....."
                component={renderInputField}
                // validate={ [ required ] }
              />
              <Field
                type="textarea"
                name="description"
                label="Description"
                placeholder="## What will you learn"
                component={renderInputField}
                // validate={ [ required ] }
              />
              <Field
                name="category"
                label="Category"
                placeholder="300₹"
                options={getOptions(categoryOptions, "price")} // price is form variable
                textField={"category"}
                component={renderSelectField}
                // validate={ [ required ] }
              />
              <label>Cover Image</label>
              <small>(max. 10MB)</small>
              <div>
                <span className="form-label">Upload Banner Image</span>
                {uplodedImg && (
                  <div className="uploaded-img-preview border p-2">
                    <img
                      src={uplodedImg}
                      alt="course-banner"
                      className="mx-auto img-fluid"
                    />
                  </div>
                )}
                <BannerImageUpload setUploadedImg={setUploadedImg} />
              </div>
              <Field
                type="text"
                name="video"
                label="Promotional video (YouTube URL only)"
                placeholder="https://www.youtube.com/embed/..."
                component={renderInputField}
                // validate={ [ required ] }
              />
              <button
                type="submit"
                className="btn btn-primary"
                disabled={submitting}
              >
                Save
              </button>
            </form>
          </div>
        </div>
      </div>
    </>
  );
};

export default reduxForm({
  form: "blog-setting-section",
})(BlogSettingsSection);

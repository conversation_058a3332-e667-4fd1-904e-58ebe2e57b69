"use client";

import { withErrorBoundary } from 'react-error-boundary';
import QuizTestQuestions from "./QuizTestQuestions";
import ErrorFallback from '../../../../../../components/sharedComponents/ErrorFallback';

function logErrorToService(error, info) {
  // Log the error to an error logging service
  console.error("Logging error:", error, info);
}

export default withErrorBoundary(QuizTestQuestions, {
  FallbackComponent: ErrorFallback,
  onError: logErrorToService,
});
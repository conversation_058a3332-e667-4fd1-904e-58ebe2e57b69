import { useSelector } from "react-redux";

import CandidatesInfoTable from "./CandidatesInfoTable.jsx";

const CandidatesTables = () => {
  const { quizTestTakens } = useSelector((state) => state.quizTest);

  return (
    <div className='custom-table bg-white shadow-sm rounded '>
      <div className='d-flex flex-column flex-md-row justify-content-between align-items-center'>
        <h5 className='fw-bold mb-2 mb-md-0'>
          Candidates ({quizTestTakens?.length || 0})
        </h5>

        <div className='d-flex align-items-center'>
          <i className='bi bi-search cursor-pointer text-muted fs-5 mx-1 hover-effect'></i>
          <i className='bi bi-funnel cursor-pointer text-muted fs-5 mx-1 hover-effect'></i>
          <i className='bi bi-arrow-repeat cursor-pointer text-muted fs-5 mx-1 hover-effect'></i>

          <select className='form-select form-select-sm border-0 shadow-sm mx-1 px-2'>
            <option selected>20 rows</option>
            <option value='10'>10 rows</option>
            <option value='50'>50 rows</option>
          </select>

          <select className='form-select form-select-sm border-0 shadow-sm mx-1 px-2'>
            <option selected>8 of 11 cols</option>
            <option value='1'>All columns</option>
            <option value='2'>Custom</option>
          </select>
        </div>
      </div>

      <div className='mt-3'>
        <CandidatesInfoTable quizTestTakens={quizTestTakens} />
      </div>
    </div>
  );
};

export default CandidatesTables;

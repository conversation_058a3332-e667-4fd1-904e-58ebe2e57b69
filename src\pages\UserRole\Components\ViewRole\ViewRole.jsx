import { useEffect, useRef, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useParams } from "react-router-dom";

import RoleListCard from "../Role/RoleList/RoleListCard";
import AddRoleModal from "../Role/CreateRole/AddRoleModal";
import UserAssignedCard from "./UserAssignedCard";
import { getRole, unassignUserToRole } from "../../actions/operations";
import CustomLoader from "../../../../components/sharedComponents/CustomLoader";
import FilterPopup from "../Role/RoleList/FilterPopup";
import AssignUserToRoleModal from "./AssignUserToRoleModal";


const ViewRole = () => {

  const dispatch = useDispatch();
  const { id } = useParams()

  const { roles, role, viewRoleLoading } = useSelector((state) => state.role) || {}

  const [showModal, setShowModal] = useState(false);
  const [isView, setIsView] = useState(true);
  const [isEdit, setIsEdit] = useState(false);
  const [openFilter, setOpenFilter] = useState(false);
  const [showUserModal, setShowUserModal] = useState(false);
  const userIdRef = useRef(null);

  useEffect(() => {
    dispatch(getRole(id));
  }, [dispatch, id]);

  const toggleModal = () => {
    setShowModal(!showModal);
  };
  const handleIsEdit = () => {
    toggleModal();
    setIsEdit(true);
  }
  const handleIsView = () => {
    setIsView(!isView);
  }
  const handleUnassignUser = (role, userId) => {
    dispatch(unassignUserToRole({ role, userId }));

  }
  const items = [
    {
      key: '1',
      label: (
        <div >View</div>
      ),
    },
    {
      key: '2',
      label: (
        <div onClick={(e) => {
          e.preventDefault()
          console.log(userIdRef.current)
          handleUnassignUser(id, userIdRef.current)
        }}> Unassign User</div>
      ),
    },
  ];
  return (
    <>
      <div className="row mx-0 px-5 role-list">

        <div className="col-12 padding-x">
          <div className="row mx-0 ">
            <div className="col-10 d-flex flex-column justify-content-start">
              <div className="header-role pt-3">View Role Details</div>
              <div className="role-path">Home-Role</div>
            </div>

            <div className="col-2 d-flex justify-content-end align-items-center gap-3 gap-lg-4 buttons">
              <div className="filterr">
                <button className="filter-btn  pt-1 btn btn-light" onClick={() => setOpenFilter(!openFilter)}>
                  <div >Filter</div> </button>
                <div className={openFilter ? "filter-popup" : ""}><FilterPopup openFilter={openFilter} setOpenFilter={setOpenFilter} /></div>
              </div>
            </div>
          </div>
        </div>
        <div className="col-12  padding-x">
          <div className="row mx-0 d-flex justify-content-evenly gap-4 ">
            {viewRoleLoading ? (<div className="d-flex justify-content-center w-100"><CustomLoader /></div>)
              : (<>
                <div className="col-3 mt-3 ">
                  <RoleListCard role={role} isView={isView} handleIsEdit={handleIsEdit} handleIsView={handleIsView} showModal={showModal} setShowModal={setShowModal} toggleModal={toggleModal} isEdit={isEdit} setIsEdit={setIsEdit} setShowUserModal={setShowUserModal} key={"role"} />
                </div>
                <div className="col-9 mt-3">
                  <UserAssignedCard id={id} role={role} items={items} userIdRef={userIdRef} key={"users"} />
                </div>
              </>)}
          </div>
        </div>
      </div>
      {showModal && <AddRoleModal showModal={showModal} setShowModal={setShowModal} toggleModal={toggleModal} isEdit={isEdit} setIsEdit={setIsEdit} id={id} />}
      {showUserModal && <AssignUserToRoleModal showModal={showUserModal} setShowModal={setShowUserModal} role={role} id={id} />}
    </>
  );
};

export default ViewRole;


import React from 'react'

import './helper.css'

function CardDesign() {

    return (
        <div style={{ background: "#333333" }} className='rounded a m-3'>
            <div className='fs-1 c caredName'> Batch Name </div>
            <ul style={{ fontSize: "22px" }} >
                <li className='ml-4 caredName'>Registartion Start Date : </li>
                <li className='ml-4 caredName'>Registartion End Date : </li>
                <li className='ml-4 caredName'>Batch Start Date : </li>
                <li className='ml-4 caredName'>Batch End Date : </li>
                <li className='ml-4 caredName'>Status : </li>
            </ul>
            <div style={{ fontSize: "25px" }} className='mainCardContainer'>

                <div style={{ background: "#c345fd" }} className='cardContainer'>
                    <div>
                        <svg className="w-[40px] h-[40px] text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="60" height="60" fill="currentColor" viewBox="0 0 24 24">
                            <path fillRule="evenodd" d="M8 4a4 4 0 1 0 0 8 4 4 0 0 0 0-8Zm-2 9a4 4 0 0 0-4 4v1a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2v-1a4 4 0 0 0-4-4H6Zm7.25-2.095c.478-.86.75-1.85.75-2.905a5.973 5.973 0 0 0-.75-2.906 4 4 0 1 1 0 5.811ZM15.466 20c.34-.588.535-1.271.535-2v-1a5.978 5.978 0 0 0-1.528-4H18a4 4 0 0 1 4 4v1a2 2 0 0 1-2 2h-4.535Z" clipRule="evenodd" />
                        </svg>
                        <div className='caredName'>Total<br />Users</div>
                        <div className='cardValue'>2</div>
                        <div className='circleOne'></div>
                        <div className='circleTwo'></div>
                    </div>
                </div>
                <div style={{ background: "#2fc5ff" }} className='cardContainer'>
                    <div>
                        <svg className="w-[40px] h-[40px] text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="60" height="60" fill="none" viewBox="0 0 24 24">
                            <path stroke="currentColor" strokeLinecap="round" strokeWidth="2" d="M8.737 8.737a21.49 21.49 0 0 1 3.308-2.724m0 0c3.063-2.026 5.99-2.641 7.331-1.3 1.827 1.828.026 6.591-4.023 10.64-4.049 4.049-8.812 5.85-10.64 4.023-1.33-1.33-.736-4.218 1.249-7.253m6.083-6.11c-3.063-2.026-5.99-2.641-7.331-1.3-1.827 1.828-.026 6.591 4.023 10.64m3.308-9.34a21.497 21.497 0 0 1 3.308 2.724m2.775 3.386c1.985 3.035 2.579 5.923 1.248 7.253-1.336 1.337-4.245.732-7.295-1.275M14 12a2 2 0 1 1-4 0 2 2 0 0 1 4 0Z" />
                        </svg>
                        <div className='caredName'>Total<br />Problems</div>
                        <div className='cardValue'>2</div>
                        <div className='circleOne'></div>
                        <div className='circleTwo'></div>
                    </div>
                </div>
                <div style={{ background: "#04f6cd" }} className='cardContainer'>
                    <div>
                        <svg xmlns="http://www.w3.org/2000/svg" height="60px" viewBox="0 0 64 64" fill="#000000">
                            <path d="M32,32c5.523,0,10-4.477,10-10s-4.477-10-10-10s-10,4.477-10,10S26.477,32,32,32z M32,36c-5.522,0-16,2.688-16,8.037V52c0,1.104,0.896,2,2,2h28c1.104,0,2-0.896,2-2v-7.963C48,38.688,37.522,36,32,36z M49.5,29c2.485,0,4.5-2.015,4.5-4.5S51.985,20,49.5,20S45,22.015,45,24.5S47.015,29,49.5,29z M49.5,33c-3.728,0-11,1.82-11,5.46V52c0,0.552,0.448,1,1,1h20c0.552,0,1-0.448,1-1v-6.54C60.5,34.82,53.228,33,49.5,33z M14.5,29c2.485,0,4.5-2.015,4.5-4.5S16.985,20,14.5,20S10,22.015,10,24.5S12.015,29,14.5,29z M14.5,33c-3.728,0-11,1.82-11,5.46V52c0,0.552,0.448,1,1,1h20c0.552,0,1-0.448,1-1v-6.54C25.5,34.82,18.228,33,14.5,33z" />
                        </svg>
                        <div className='caredName'>Total<br />Mentors</div>
                        <div className='cardValue'>2</div>
                        <div className='circleOne'></div>
                        <div className='circleTwo'></div>
                    </div>
                </div>
                <div style={{ background: "#FFFF33" }} className='cardContainer'>
                    <div>
                        <svg className="w-[40px] h-[40px] text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="60" height="60" fill="currentColor" viewBox="0 0 24 24">
                            <path fillRule="evenodd" d="M2 12C2 6.477 6.477 2 12 2s10 4.477 10 10-4.477 10-10 10S2 17.523 2 12Zm9.008-3.018a1.502 1.502 0 0 1 2.522 1.159v.024a1.44 1.44 0 0 1-1.493 1.418 1 1 0 0 0-1.037.999V14a1 1 0 1 0 2 0v-.539a3.44 3.44 0 0 0 2.529-3.256 3.502 3.502 0 0 0-7-.255 1 1 0 0 0 2 .076c.014-.398.187-.774.48-1.044Zm.982 7.026a1 1 0 1 0 0 2H12a1 1 0 1 0 0-2h-.01Z" clipRule="evenodd" />
                        </svg>
                        <div className='caredName'>Total<br />Quizes</div>
                        <div className='cardValue'>2</div>
                        <div className='circleOne'></div>
                        <div className='circleTwo'></div>
                    </div>
                </div>
                <div style={{ background: "#EF2626" }} className='cardContainer'>
                    <div>
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-[40px] w-[40px] text-gray-800 dark:text-white" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                            <path d="M20 10L12 4v8l8 6M4 14l8 6V6l-8 4z" />
                        </svg>

                        <div className='caredName'>Total<br />Lessons</div>
                        <div className='cardValue'>2</div>
                        <div className='circleOne'></div>
                        <div className='circleTwo'></div>
                    </div>
                </div>
            </div>
        </div>
    )
}

export default CardDesign

import { GoPlus } from "react-icons/go";

const NoContentCard = ({ type ,setSelectedComponent , setOpenContent}) => {
    const handleButtonClick = (key) => {
      setSelectedComponent(key);
      setOpenContent(true);
    };
    return (
      <>
        <div className='card p-4'>
          <div className='mb-3 sub-title-heading'>
          No {type} found<span className='rounded-icon ml-2' onClick={()=>handleButtonClick(type)} ><GoPlus /></span>
          </div>
        </div>
      </>
    )
  }
  
  export default NoContentCard;
import { useSelector } from "react-redux";
import { RxDashboard } from "react-icons/rx";
import { FaPeopleGroup } from "react-icons/fa6";
import { FaChalkboardTeacher } from "react-icons/fa";
import { IoCodeSlash } from "react-icons/io5";
import { HiDocumentText } from "react-icons/hi2";
import { MdQuiz } from "react-icons/md";
import { TbBookUpload } from "react-icons/tb";

const DaysCodeBatchPageNav = ({ activeTab, setActiveTab }) => {
  
  const { codeBatchDetails, codeBatchDetailsSubmissions } = useSelector(
    (state) => state.dayscode,
  );
  return (
    <>
      <div className='row '>
        <div className='w-100 justify-content-between d-fallSubmissionslex'>
          <div className='w-100 px-0 table-responsive table batch-dashboard-header-nav'>
            <ul>
              <li>
                <span
                  onClick={() => setActiveTab("overview")}
                  className={`${
                    activeTab === "overview" ? "active" : ""
                  } batch-nav-elemnets `}
                >
                  <RxDashboard className='mr-2' />
                  Overview
                </span>
              </li>
              <li>
                <span
                  onClick={() => setActiveTab("problem")}
                  className={`${
                    activeTab === "problem" ? "active" : ""
                  } batch-nav-elemnets `}
                >
                  <IoCodeSlash className='mr-2' />
                  Problem
                  <span className='badge'>
                    {codeBatchDetails.problems?.length}
                  </span>
                </span>
              </li>
              <li>
                <span
                  onClick={() => setActiveTab("submission")}
                  className={`${
                    activeTab === "submission" ? "active" : ""
                  } batch-nav-elemnets `}
                >
                  <TbBookUpload className='mr-2' />
                  Submission
                  <span className='badge'>
                    {codeBatchDetailsSubmissions.submissions}
                  </span>
                </span>
              </li>
              <li>
                <span
                  onClick={() => setActiveTab("lesson")}
                  className={`${
                    activeTab === "lesson" ? "active" : ""
                  } batch-nav-elemnets `}
                >
                  <HiDocumentText className='mr-2' />
                  Lesson
                  <span className='badge'>
                    {codeBatchDetails.lessons?.length}
                  </span>
                </span>
              </li>
              <li>
                <span
                  onClick={() => setActiveTab("quiz")}
                  className={`${
                    activeTab === "quiz" ? "active" : ""
                  } batch-nav-elemnets `}
                >
                  <MdQuiz className='mr-2' />
                  Quiz
                  <span className='badge'>
                    {codeBatchDetails.quizes?.length}
                  </span>
                </span>
              </li>
              <li>
                <span
                  onClick={() => setActiveTab("mentor")}
                  className={`${
                    activeTab === "mentor" ? "active" : ""
                  } batch-nav-elemnets `}
                >
                  <FaChalkboardTeacher className='mr-2' />
                  Mentor
                  <span className='badge'>
                    {codeBatchDetails.mentorAssign?.length}
                  </span>
                </span>
              </li>
              <li>
                <span
                  onClick={() => setActiveTab("participant")}
                  className={`${
                    activeTab === "participant" ? "active" : ""
                  } batch-nav-elemnets `}
                >
                  <FaPeopleGroup className='mr-2' />
                  Participant
                  <span className='badge'>
                    {codeBatchDetails.participants?.length}
                  </span>
                </span>
              </li>
              <li>
                <span
                  onClick={() => setActiveTab("roadmap")}
                  className={`${
                    activeTab === "roadmap" ? "active" : ""
                  } batch-nav-elemnets `}
                >
                  RoadMap
                  <span className='badge'>
                    {codeBatchDetails.roadmap?.length}
                  </span>
                </span>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </>
  );
};

export default DaysCodeBatchPageNav;

import {
  DropdownItem,
  DropdownMenu,
  DropdownToggle,
  UncontrolledDropdown,
} from "reactstrap";
import { FaEllipsisVertical } from "react-icons/fa6";
import { PiPencilSimpleLineLight } from "react-icons/pi";
import { PiTrashSimple } from "react-icons/pi";

import ImageFrame from "../../../../components/sharedComponents/ImageFrame";

const SponsorsGridView = ({
  sponsorsList,
  handleDeleteSponsor,
  handleEditSponsor,
}) => {
  return (
    <>
      <div className='row mx-0'>
        {sponsorsList?.length ? (
          sponsorsList.map((sponsor) => (
            <div className='col-12 col-md-3 p-3 custom-card ' key={sponsor._id}>
              <div className='nav-sponsor-card'>
                <div className=''>
                  <UncontrolledDropdown
                    setActiveFromChild
                    className='event-host-action-toggle'
                  >
                    <DropdownToggle tag='span' className='card-action'>
                      <FaEllipsisVertical className='icon' />
                    </DropdownToggle>
                    <DropdownMenu
                      className='dropdown-menu mt-3 card-schadow'
                      left='true'
                    >
                      <DropdownItem header>
                        <span>Actions</span>
                      </DropdownItem>
                      <DropdownItem
                        onClick={() => handleEditSponsor(sponsor._id)}
                      >
                        <span>
                          <PiPencilSimpleLineLight className='icon' />
                          Edit Sponsor
                        </span>
                      </DropdownItem>
                      <DropdownItem
                        onClick={() => handleDeleteSponsor(sponsor._id)}
                      >
                        <span>
                          <PiTrashSimple className='icon' />
                          Delete Sponsor
                        </span>
                      </DropdownItem>
                    </DropdownMenu>
                  </UncontrolledDropdown>
                </div>
                <div className='d-flex justify-content-center'>
                  <ImageFrame imgUrl={sponsor?.imgUrl} />
                </div>
                <div className=''>Name: {sponsor.name}</div>
                <div className=''>Email: {sponsor.email}</div>
                <div className=''>Phone: {sponsor.phone}</div>
                <div className=''>Universal: {sponsor.university}</div>
                <div className=''>Bio: {sponsor.bio}</div>
              </div>
            </div>
          ))
        ) : (
          <span> No Sponsor Availabel </span>
        )}
      </div>
    </>
  );
};
export default SponsorsGridView;

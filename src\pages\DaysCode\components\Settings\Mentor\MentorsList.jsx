import React, { useState,useEffect  } from "react";
import { useDispatch, useSelector } from "react-redux";
import { BsThreeDotsVertical } from "react-icons/bs";
import {
  DropdownItem,
  DropdownMenu,
  DropdownToggle,
  UncontrolledDropdown,
} from "reactstrap";

import LayoutContainer from "../LayoutContainer";
import DeleteModal from "../../../../../components/sharedComponents/DeleteModal";
import {
  getDaysCodeMentors,
  deleteDaysCodeMentor,
} from "../../../actions/operations";
import MentorDetails from "../../../../../assets/Modals/MentorDetailsDelete";

const MentorsList = () => {
  
  const dispatch = useDispatch();

  const [openModal, setOpenModal] = useState(false);
  const [tempDeleteId, setTempDeleteId] = useState();
  const { mentorList } = useSelector((state) => state.dayscode) || {};

  useEffect(() => {
    dispatch(getDaysCodeMentors());
  }, [dispatch]);

  const toggleModal = () => {
    setOpenModal(!openModal);
  };

  const handleDeleteModal = (id) => {
    setTempDeleteId(id);
    setOpenModal(!openModal);
  };

  const handleMentorDeleteModal = () => {
    dispatch(deleteDaysCodeMentor(tempDeleteId)).then((res) => {
      if (res.success) {
        dispatch(getDaysCodeMentors());
      }
    });
  };

  return (
    <>
      <LayoutContainer>
        <h1>MentorsList</h1>
        <div className='row mx-0'>
          {mentorList &&
            mentorList.map((mentor) => (
              <div className='col-12 col-md-6' key={mentor._id}>
                <div className='mentor-card'>
                  <div className='upper-card-portion'>
                    <UncontrolledDropdown
                      setActiveFromChild
                      className='event-host-action-toggle'
                    >
                      <DropdownToggle tag='span' className='card-action'>
                        {/* < title="Remove Speaker" placement="bottom"> */}
                        <BsThreeDotsVertical className='dlt-card-btn my-2 mx-2' />{" "}
                      </DropdownToggle>
                      <DropdownMenu
                        className='dropdown-menu mt-3 card-shadow'
                        end
                      >
                        <DropdownItem
                          className='detail-mentor-btn'
                          // onClick={() => handleDeleteModal(mentor._id)}
                        >
                          Mentor Details
                        </DropdownItem>

                        <DropdownItem
                          className='remove-btn'
                          onClick={() => handleDeleteModal(mentor._id)}
                        >
                          Delete
                        </DropdownItem>
                      </DropdownMenu>
                    </UncontrolledDropdown>
                  </div>
                  <>
                    <img
                      className='mentor-image'
                      src={
                        mentor.imgUrl || (mentor.mentor && mentor.mentor.imgUrl)
                      }
                      alt=''
                    />
                    <div className='mentor-details'>
                      <p className='mb-0 text-truncate'>
                        {mentor.name || (mentor.mentor && mentor.mentor.name)}
                      </p>
                      <h5 className='mt-2'>{mentor.role}</h5>
                    </div>
                  </>
                </div>
              </div>
            ))}
        </div>
      </LayoutContainer>
      <DeleteModal
        open={openModal}
        toggle={toggleModal}
        onSubmit={handleMentorDeleteModal}
        submitButtonName={"Delete Problem"}
        message={" Are you sure want to delete this event"}
        title={"Delete Event"}
      />
      <MentorDetails mentorlist={MentorsList} />
    </>
  );
};
export default MentorsList;

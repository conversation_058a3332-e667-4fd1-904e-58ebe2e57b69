import axios from "axios";
import * as actions from "./actionCreators";
import {
  SET_PROBLEM_LOADING,
  GET_PROBLEMS_LOADING,
  GET_PROBLEM_DETAILS_LOADING,
  SET_SOLUTION_SUBMISSION_LOADING,
  SET_DAYS_USER_REG_LOADING,
  API_LOADING,
  GET_QUIZ_CHALLENGE_REPORT_LOADING,
  GET_USER_SUBMISSIONS_LOADING,
  GET_CONTENT_DETAILS_LOADING,
  SET_LESSON_COMPLETED_STATUS,
  SET_CHALLENGES_COMPLETED_STATUS,
  SET_SUBMISSION_COMPLETED_STATUS,
  SET_USER_ATTENDANCE_PRESENT_LOADING,
  SET_UPDATED_QUIZ_CHALLENGES,
  SET_USER_FEEDBACK_LOADING,
  GET_ALL_USERS_FEEDBACKS_LOADING,
  GET_ALL_USERS_PROGRESS_LOADING,
  GET_ALL_USERS_PROGRESS,
  GET_PRACTICE_PROBLEMS_LOADING,
  GET_ALL_CODE_BATCHES_LOADING,
  GET_ALL_CODE_BATCHES,
  GET_CODE_BATCH,
  GET_SPEAKERS_LIST_LOADING,
  SET_MENTOR_LIST,
  GET_BATCH_LEADERBOARD_LOADING,
  GET_CREATE_CODE_CLASS_LOADING,
  GET_CODE_CLASS_DETAILS_LOADING,
  GET_CODE_CLASSES_LOADING,
  GET_ADD_BATCH_IN_TO_CLASS_LOADING,
  GET_MORE_CODE_BATCHES_LOADING,
  GET_MORE_CODE_BATCHES,
  SET_BATCH_CONTENT_LOADING,
} from "../constants";
import { generateQueryParams } from "../../../components/utils";
import { toast } from "react-hot-toast";
import { triggerNotifier } from "../../../components/utils/notifier";

const baseURL = process.env.REACT_APP_BASE_URL;
let abortController = null;

export const checkOutAPI = async (problem) => {
  return axios
    .post(`${baseURL}/payment/create`, problem)
    .then((res) => {
      if (res.status === 200) {
        return { success: true, data: res.data };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      console.log("payment Error", error);
      triggerNotifier({
        message: error?.response?.data?.message,
        type: "error",
        duration: 2000,
        icon: "⚠️",
      });
    });
};

export const addProblem = (problem) => (dispatch) => {
  dispatch({ type: SET_PROBLEM_LOADING });
  return axios
    .post(`${baseURL}/dayscode/add_problem`, problem)
    .then((res) => {
      if (res.status === 200) {
        dispatch({ type: SET_PROBLEM_LOADING });
        return { success: true, problem:res?.data?.problem };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: SET_PROBLEM_LOADING });
      console.log("add problem Error", error);
      triggerNotifier({
        message: error?.response?.data?.message,
        type: "error",
        duration: 2000,
        icon: "⚠️",
      });
    });
};

export const addAdminSolution = (id, solution) => (dispatch) => {
  dispatch({ type: SET_PROBLEM_LOADING });
  return axios
    .put(`${baseURL}/dayscode/admin_submission/${id}`, solution)
    .then((res) => {
      if (res.status === 200) {
        console.log("created solution", res.data);
        dispatch({ type: SET_PROBLEM_LOADING });
        dispatch(getProblemDetails(id));
        return { success: true, solution: res?.data?.solution };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: SET_PROBLEM_LOADING });
      console.log("add solution Error", error);
      triggerNotifier({
        message: error?.response?.data?.message,
        type: "error",
        duration: 2000,
        icon: "⚠️",
      });
    });
};

export const addProblemTestCase = (id, testcase) => (dispatch) => {
  dispatch({ type: SET_PROBLEM_LOADING });
  return axios
    .put(`${baseURL}/dayscode/test_case/${id}`, testcase)
    .then((res) => {
      if (res.status === 200) {
        dispatch({ type: SET_PROBLEM_LOADING });
        dispatch(getProblemDetails(id));
        return { success: true, testCase: res?.data?.testCase };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: SET_PROBLEM_LOADING });
      console.log("add testcase Error", error);
      triggerNotifier({
        message: error?.response?.data?.message,
        type: "error",
        duration: 2000,
        icon: "⚠️",
      });
    });
};

export const editProblemTestCase = (id, testcase) => (dispatch) => {
  dispatch({ type: SET_PROBLEM_LOADING });
  return axios
    .put(`${baseURL}/dayscode/edit_test_case/${id}`, testcase)
    .then((res) => {
      if (res.status === 200) {
        dispatch({ type: SET_PROBLEM_LOADING });
        dispatch(getProblemDetails(id));
        return { success: true, testCase: res?.data?.testCase };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: SET_PROBLEM_LOADING });
      console.log("edit testcase Error", error);
      triggerNotifier({
        message: error?.response?.data?.message,
        type: "error",
        duration: 2000,
        icon: "⚠️",
      });
    });
};

export const solutionSubmission = (solution) => (dispatch) => {
  return axios
    .put(`${baseURL}dayscode/solution_submission`, solution)
    .then((res) => {
      if (res.status === 200) {
        dispatch({ type: SET_SOLUTION_SUBMISSION_LOADING });
        dispatch(getProblemDetails(solution.id));
        dispatch(getUserSubmissions(solution.userName));
        dispatch({ type: SET_SUBMISSION_COMPLETED_STATUS, payload: true });
        return { success: true, data: res.data };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: SET_SOLUTION_SUBMISSION_LOADING });
      console.log("solutionSubmission Error", error);
      triggerNotifier({
        message: error?.response?.data?.message,
        type: "error",
        duration: 2000,
        icon: "⚠️",
      });
    });
};

// export const eventQuestion = (question) => (dispatch) => {
//   dispatch({ type: SET_EVENT_REGISTRATION_LOADING });
//   return axios
//     .put(`${baseURL}/event/add_question`, question)
//     .then((res) => {
//       if (res.status === 200) {
//         dispatch({ type: SET_EVENT_REGISTRATION_LOADING });
//         return { success: true, data: res?.data?.event };
//       } else {
//         return { success: false };
//       }
//     })
//     .catch((error) => {
//       dispatch({ type: SET_EVENT_REGISTRATION_LOADING });
//       console.log("eventRegister Error", error);
//     });
// };

// export const getProblems = () => (dispatch) => {
//   dispatch({ type: SET_EVENT_REGISTRATION_LOADING });
//   return axios
//     .get(`${baseURL}/user/get_speakers`)
//     .then(({ data }) => {
//       if (data.success) {
//         return { success: true, data: data.Speakers };
//       }
//     })
//     .catch((error) => {
//       dispatch({ type: SET_EVENT_REGISTRATION_LOADING });
//       console.log("get speakers error", error);
//     });
// };

export const getProblemDetails = (id) => (dispatch) => {
  dispatch({ type: GET_PROBLEM_DETAILS_LOADING });
  return axios
    .post(`${baseURL}/dayscode/get_problem`, { id })
    .then(({ data }) => {
      if (data.success) {
        dispatch(actions.setProblemDetails(data?.problem));
        return { success: true, data: data?.problem };
      }
    })
    .catch((error) => {
      dispatch({ type: GET_PROBLEM_DETAILS_LOADING });
      console.log("get problem error", error);
      triggerNotifier({
        message: error?.response?.data?.message,
        type: "error",
        duration: 2000,
        icon: "⚠️",
      });
    });
};

// export const eventFeedback = (feedback) => (dispatch) => {
//   dispatch({ type: SET_EVENT_REGISTRATION_LOADING });
//   return axios
//     .put(`${baseURL}/event/feedback_event`, feedback)
//     .then((res) => {
//       if (res.status === 200) {
//         dispatch({ type: SET_EVENT_REGISTRATION_LOADING });
//         return { success: true, data: res?.data?.event };
//       } else {
//         return { success: false };
//       }
//     })
//     .catch((error) => {
//       dispatch({ type: SET_EVENT_REGISTRATION_LOADING });
//       console.log("event Feedback Error", error);
//     });
// };

// export const addEventSpeaker = (speaker) => (dispatch) => {
//   dispatch({ type: SET_EVENT_REGISTRATION_LOADING });
//   return axios
//     .put(`${baseURL}/event/event_speaker`, speaker)
//     .then((res) => {
//       if (res.status === 200) {
//         dispatch({ type: SET_EVENT_REGISTRATION_LOADING });
//         return { success: true, data: res?.data?.event };
//       } else {
//         return { success: false };
//       }
//     })
//     .catch((error) => {
//       dispatch({ type: SET_EVENT_REGISTRATION_LOADING });
//       console.log("event Speaker Error", error);
//     });
// };

export const getProblems =
  ({ status, problem_type, day, topic }) =>
  (dispatch) => {
    dispatch({ type: GET_PROBLEMS_LOADING });
    return axios
      .get(
        `${baseURL}/dayscode/fetch_problems${generateQueryParams({
          day: day ? day : null,
          problem_type: problem_type ? problem_type : "assignment",
          status: status ? status : "public",
          topic: topic,
        })}`,
      )
      .then((res) => {
        if (res.status === 200) {
          dispatch(actions.setProblemsList(res?.data?.problems));
          return { success: true, data:res?.data?.problems };
        } else {
          return { success: false };
        }
      })
      .catch((error) => {
        dispatch({ type: GET_PROBLEMS_LOADING });
        console.log("problem List Error", error);
        triggerNotifier({
          message: error?.response?.data?.message,
          type: "error",
          duration: 2000,
          icon: "⚠️",
        });
      });
  };

export const getPracticeProblems = (day) => (dispatch) => {
  dispatch({ type: GET_PRACTICE_PROBLEMS_LOADING });
  return axios
    .get(
      `${baseURL}/dayscode/fetch_problems${generateQueryParams({
        day: day ? day : null,
        problem_type: "practice",
        status: "public",
      })}`,
    )
    .then((res) => {
      if (res.status === 200) {
        dispatch(
          actions.setPracticeProblemsList(res?.data?.problems),
        );
        return { success: true, data: res?.data?.problems };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: GET_PRACTICE_PROBLEMS_LOADING });
      console.log("problem List Error", error);
      triggerNotifier({
        message: error?.response?.data?.message,
        type: "error",
        duration: 2000,
        icon: "⚠️",
      });
    });
};

export const getUserSubmissions = (userName, status) => (dispatch) => {
  dispatch({ type: GET_USER_SUBMISSIONS_LOADING });
  return (
    axios
      .get(`${baseURL}/dayscode/submissions_list/${"3"}/${userName}`)
      // .get(`${baseURL}/dayscode/submissions_list/${generateQueryParams({
      //   batchId: 2,
      //   userName,
      //   status,
      // })}`)
      .then((res) => {
        if (res.status === 200) {
          dispatch({ type: GET_USER_SUBMISSIONS_LOADING });
          dispatch(actions.setUserSubmissionsList(res?.data));
          return { success: true, data: res?.data?.submissions };
        } else {
          return { success: false };
        }
      })
      .catch((error) => {
        dispatch({ type: GET_USER_SUBMISSIONS_LOADING });
        console.log("soluitions List Error", error);
        triggerNotifier({
          message: error?.response?.data?.message,
          type: "error",
          duration: 2000,
          icon: "⚠️",
        });
      })
  );
};

export const getUserSubmissionsByStatus = (userName, status) => (dispatch) => {
  dispatch({ type: GET_USER_SUBMISSIONS_LOADING });
  return axios
    .post(
      `${baseURL}dayscode/user_submissions${generateQueryParams({
        batchId: 3,
        userName,
        status,
      })}`,
    )
    .then((res) => {
      if (res.status === 200) {
        dispatch({ type: GET_USER_SUBMISSIONS_LOADING });
        return {
          success: true,
          userSubmissions: res?.data?.submissions,
        };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: GET_USER_SUBMISSIONS_LOADING });
      console.log("soluitions List Error", error);
      triggerNotifier({
        message: error?.response?.data?.message,
        type: "error",
        duration: 2000,
        icon: "⚠️",
      });
    });
};

export const getSubmissionsLeaderboard = () => (dispatch) => {
  return axios
    .get(`${baseURL}/dayscode/submissions_leaderboard/${"3"}`)
    .then((res) => {
      if (res.status === 200) {
        dispatch(
          actions.setSubmissionsLeaderboard(res?.data?.leaderBoard),
        );
        return { success: true, data: res?.data?.problems };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      console.log("soluitions List Error", error);
      triggerNotifier({
        message: error?.response?.data?.message,
        type: "error",
        duration: 2000,
        icon: "⚠️",
      });
    });
};

// export const getAllSubmissions = (id) => (dispatch) => {
//   dispatch({ type: GET_PROBLEMS_LOADING });
//   dispatch({ type: GET_USER_SUBMISSIONS_LOADING });
//   return axios
//     .get(`${baseURL}/dayscode/all_submissions/${id}`)
//     .then((res) => {
//       if (res.status === 200) {
//         dispatch({ type: GET_PROBLEMS_LOADING });
//         dispatch({ type: GET_USER_SUBMISSIONS_LOADING });
//         dispatch(
//           actions.setAllSubmissions(
//             res?.data?.submissions.submissions,
//           ),
//         );
//         return {
//           success: true,
//           data: res?.data?.submissions.submissions,
//         };
//       } else {
//         return { success: false };
//       }
//     })
//     .catch((error) => {
//       dispatch({ type: GET_PROBLEMS_LOADING });
//       dispatch({ type: GET_USER_SUBMISSIONS_LOADING });
//       console.log("problem all soluitions List Error", error);
//       triggerNotifier({
//         message: error?.response?.data?.message,
//         type: "error",
//         duration: 2000,
//         icon: "⚠️",
//       });
//     });
// };

export const getAllSubmissions = () => (dispatch) => {
  dispatch({ type: GET_PROBLEMS_LOADING });
  dispatch({ type: GET_USER_SUBMISSIONS_LOADING });
  return axios
    .get(`${baseURL}/dayscode/submission`)
    .then((res) => {
      if (res.status === 200) {
        dispatch({ type: GET_PROBLEMS_LOADING });
        dispatch({ type: GET_USER_SUBMISSIONS_LOADING });
        dispatch(actions.setAllSubmissions(res?.data?.submissions));
        return {
          success: true,
          data: res?.data?.submissions,
        };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: GET_PROBLEMS_LOADING });
      dispatch({ type: GET_USER_SUBMISSIONS_LOADING });
      console.log("problem all soluitions List Error", error);
      triggerNotifier({
        message: error?.response?.data?.message,
        type: "error",
        duration: 2000,
        icon: "⚠️",
      });
    });
};

export const getAllSubmissionsByBatch = (batch) => (dispatch) => {
  dispatch({ type: GET_PROBLEMS_LOADING });
  dispatch({ type: GET_USER_SUBMISSIONS_LOADING });
  return axios
    .get(
      `${baseURL}/dayscode/submission/${generateQueryParams({
        batch,
      })}`,
    )
    .then((res) => {
      if (res.status === 200) {
        dispatch({ type: GET_PROBLEMS_LOADING });
        dispatch({ type: GET_USER_SUBMISSIONS_LOADING });
        dispatch(actions.setAllSubmissions(res?.data?.submissions));
        return {
          success: true,
          data: res?.data?.submissions,
        };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: GET_PROBLEMS_LOADING });
      dispatch({ type: GET_USER_SUBMISSIONS_LOADING });
      console.log("problem all soluitions List Error", error);
      triggerNotifier({
        message: error?.response?.data?.message,
        type: "error",
        duration: 2000,
        icon: "⚠️",
      });
    });
};

export const getAllSubmissionsByUser = (user) => (dispatch) => {
  dispatch({ type: GET_PROBLEMS_LOADING });
  dispatch({ type: GET_USER_SUBMISSIONS_LOADING });
  return axios
    .get(
      `${baseURL}/dayscode/submission/${generateQueryParams({
        participant: user,
      })}`,
    )
    .then((res) => {
      if (res.status === 200) {
        dispatch({ type: GET_PROBLEMS_LOADING });
        dispatch({ type: GET_USER_SUBMISSIONS_LOADING });
        dispatch(actions.setAllSubmissions(res?.data?.submissions));
        return {
          success: true,
          data: res?.data?.submissions,
        };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: GET_PROBLEMS_LOADING });
      dispatch({ type: GET_USER_SUBMISSIONS_LOADING });
      console.log("problem all soluitions List Error", error);
      triggerNotifier({
        message: error?.response?.data?.message,
        type: "error",
        duration: 2000,
        icon: "⚠️",
      });
    });
};

export const getAllSubmissionsByProblem = (problem) => (dispatch) => {
  dispatch({ type: GET_PROBLEMS_LOADING });
  dispatch({ type: GET_USER_SUBMISSIONS_LOADING });
  return axios
    .get(
      `${baseURL}/dayscode/submission/${generateQueryParams({
        problem: problem,
      })}`,
    )
    .then((res) => {
      if (res.status === 200) {
        dispatch({ type: GET_PROBLEMS_LOADING });
        dispatch({ type: GET_USER_SUBMISSIONS_LOADING });
        dispatch(actions.setAllSubmissions(res?.data?.submissions));
        return {
          success: true,
          data: res?.data?.submissions,
        };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: GET_PROBLEMS_LOADING });
      dispatch({ type: GET_USER_SUBMISSIONS_LOADING });
      console.log("problem all soluitions List Error", error);
      triggerNotifier({
        message: error?.response?.data?.message,
        type: "error",
        duration: 2000,
        icon: "⚠️",
      });
    });
};

export const editProblem = (problem) => (dispatch) => {
  dispatch({ type: SET_PROBLEM_LOADING });
  return axios
    .put(`${baseURL}/dayscode/edit_problem`, problem)
    .then((res) => {
      if (res.status === 200) {
        dispatch({ type: SET_PROBLEM_LOADING });
        dispatch(getProblemDetails(problem.id));
        return { success: true, problem: res?.data?.problem };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: SET_PROBLEM_LOADING });
      console.log("edit problem Error", error);
      triggerNotifier({
        message: error?.response?.data?.message,
        type: "error",
        duration: 2000,
        icon: "⚠️",
      });
    });
};

export const removeDaysProblem = (id) => (dispatch) => {
  return axios
    .delete(`${baseURL}/dayscode/delete_problem/${id}`)
    .then(({ data }) => {
      if (data.success) {
        dispatch(getProblems());
        return { success: true };
      }
    })
    .catch((error) => {
      console.log("get days user error", error);
      triggerNotifier({
        message: error?.response?.data?.message,
        type: "error",
        duration: 2000,
        icon: "⚠️",
      });
    });
};

export const deleteAdminSolution = (id, solutionId) => (dispatch) => {
  dispatch({ type: SET_PROBLEM_LOADING });
  return axios
    .delete(`${baseURL}/dayscode/delete_admin_solution/${id}/${solutionId}`)
    .then((res) => {
      if (res.status === 200) {
        dispatch({ type: SET_PROBLEM_LOADING });
        dispatch(getProblemDetails(id));
        return { success: true };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: SET_PROBLEM_LOADING });
      console.log("delete solution Error", error);
      triggerNotifier({
        message: error?.response?.data?.message,
        type: "error",
        duration: 2000,
        icon: "⚠️",
      });
    });
};

export const deleteProblemTestCase = (id, testCaseId) => (dispatch) => {
  dispatch({ type: SET_PROBLEM_LOADING });
  return axios
    .delete(`${baseURL}dayscode/delete_testcase/${id}/${testCaseId}`)
    .then((res) => {
      if (res.status === 200) {
        dispatch({ type: SET_PROBLEM_LOADING });
        dispatch(getProblemDetails(id));
        return { success: true };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: SET_PROBLEM_LOADING });
      console.log("delete testcase Error", error);
      triggerNotifier({
        message: error?.response?.data?.message,
        type: "error",
        duration: 2000,
        icon: "⚠️",
      });
    });
};

export const deleteUserSubmission = (id, userName) => (dispatch) => {
  dispatch({ type: SET_PROBLEM_LOADING });
  return axios
    .delete(`${baseURL}dayscode/delete_user_solution/${id}/${userName}`)
    .then((res) => {
      if (res.status === 200) {
        dispatch({ type: SET_PROBLEM_LOADING });
        dispatch(getAllSubmissions(id));
        return { success: true };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: SET_PROBLEM_LOADING });
      console.log("delete user solution Error", error);
      triggerNotifier({
        message: error?.response?.data?.message,
        type: "error",
        duration: 2000,
        icon: "⚠️",
      });
    });
};

export const setDaysUserReg = (data) => (dispatch) => {
  dispatch({ type: SET_DAYS_USER_REG_LOADING });
  return axios
    .post(`${baseURL}/dayscode/add_user`, data)
    .then(({ data }) => {
      if (data.success) {
        dispatch({ type: SET_DAYS_USER_REG_LOADING });
        return { success: true, data: data.user };
      }
    })
    .catch((error) => {
      dispatch({ type: SET_DAYS_USER_REG_LOADING });
      console.log("reg user error", error);
      triggerNotifier({
        message: error?.response?.data?.message,
        type: "error",
        duration: 2000,
        icon: "⚠️",
      });
    });
};

export const getDaysUserReg = (userName, batchId) => (dispatch) => {
  dispatch({ type: API_LOADING });
  return axios
    .get(
      `${baseURL}/dayscode/get_user/${generateQueryParams({
        batchId: batchId ? batchId : "3",
        userName,
      })}`,
    )
    .then(({ data }) => {
      if (data.success) {
        dispatch({ type: API_LOADING });
        dispatch(actions.setDaysUserDetails(data && data.user));
        return { success: true, data: data.user, waiting: data.waiting };
      }
    })
    .catch((error) => {
      dispatch({ type: API_LOADING });
      console.log("get days user error", error);
      triggerNotifier({
        message: error?.response?.data?.message,
        type: "error",
        duration: 2000,
        icon: "⚠️",
      });
    });
};

export const getDaysUsers =
  ({ search, limit }) =>
  (dispatch) => {
    dispatch({ type: GET_PROBLEMS_LOADING });
    return axios
      .get(
        `${baseURL}/dayscode/participants${generateQueryParams({
          limit: limit,
          search: search,
        })}`,
      )
      .then(({ data }) => {
        if (data.success) {
          dispatch(actions.setDaysUsersList(data.participants));
          dispatch({ type: GET_PROBLEMS_LOADING });
          return { success: true, data: data.participants };
        }
      })
      .catch((error) => {
        dispatch({ type: GET_PROBLEMS_LOADING });
        console.log("get days users error", error);
        triggerNotifier({
          message: error?.response?.data?.message,
          type: "error",
          duration: 2000,
          icon: "⚠️",
        });
      });
  };

export const getDaysUsersListOptions = (mentorId, admin) => (dispatch) => {
  dispatch({ type: GET_PROBLEMS_LOADING });
  return axios
    .get(`${baseURL}/dayscode/users_list/${mentorId}/${admin}`)
    .then(({ data }) => {
      if (data.success) {
        dispatch(actions.setDaysUsersList(data.users));
        dispatch({ type: GET_PROBLEMS_LOADING });
        return { success: true, data: data.users };
      }
    })
    .catch((error) => {
      dispatch({ type: GET_PROBLEMS_LOADING });
      console.log("get days users list error", error);
      triggerNotifier({
        message: error?.response?.data?.message,
        type: "error",
        duration: 2000,
        icon: "⚠️",
      });
    });
};

export const userSelectionAction = (id, data) => (dispatch) => {
  return axios
    .put(`${baseURL}/dayscode/edit_user/${id}`, data)
    .then(({ data }) => {
      if (data.success) {
        dispatch(getDaysUsers());
        return { success: true, data: data.user };
      }
    })
    .catch((error) => {
      console.log("get days users error", error);
      triggerNotifier({
        message: error?.response?.data?.message,
        type: "error",
        duration: 2000,
        icon: "⚠️",
      });
    });
};

export const deleteDaysUser = (id) => (dispatch) => {
  return axios
    .delete(`${baseURL}/dayscode/delete_user/${id}`)
    .then(({ data }) => {
      if (data.success) {
        dispatch(getDaysUsers());
        return { success: true, data: data.user };
      }
    })
    .catch((error) => {
      console.log("delete days users error", error);
      triggerNotifier({
        message: error?.response?.data?.message,
        type: "error",
        duration: 2000,
        icon: "⚠️",
      });
    });
};

export const approveUserCodeSubmission =
  ({ id, data }) =>
  (dispatch) => {
    dispatch({ type: GET_USER_SUBMISSIONS_LOADING });
    return axios
      .patch(`${baseURL}/dayscode/submission/${id}`, { data })
      .then((res) => {
        if (res.status === 200) {
          dispatch({ type: GET_USER_SUBMISSIONS_LOADING });
          dispatch(actions.setAllSubmissions(res?.data?.submissions));
          return { success: true, data: res?.data?.submissions };
        } else {
          return { success: false };
        }
      })
      .catch((error) => {
        dispatch({ type: SET_PROBLEM_LOADING });
        console.log("approve solution Error", error);
        triggerNotifier({
          message: error?.response?.data?.message,
          type: "error",
          duration: 2000,
          icon: "⚠️",
        });
      });
  };

export const adminNotificationDaysCodeUser = (data) => (dispatch) => {
  dispatch({ type: SET_PROBLEM_LOADING });
  return axios
    .put(`${baseURL}/dayscode/add_notification`, data)
    .then((res) => {
      if (res.status === 200) {
        dispatch({ type: SET_PROBLEM_LOADING });
        return { success: true };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: SET_PROBLEM_LOADING });
      console.log("add notification Error", error);
      triggerNotifier({
        message: error?.response?.data?.message,
        type: "error",
        duration: 2000,
        icon: "⚠️",
      });
    });
};

export const deleteAdminNotification = (id) => (dispatch) => {
  dispatch({ type: SET_PROBLEM_LOADING });
  return axios
    .put(`${baseURL}/dayscode/delete_notification/${id}`)
    .then((res) => {
      if (res.status === 200) {
        dispatch({ type: SET_PROBLEM_LOADING });
        return { success: true };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: SET_PROBLEM_LOADING });
      console.log("delete notification Error", error);
      triggerNotifier({
        message: error?.response?.data?.message,
        type: "error",
        duration: 2000,
        icon: "⚠️",
      });
    });
};

// -------------Lessons--------------------------------------------

export const createLesson = (lesson) => (dispatch) => {
  dispatch({ type: SET_PROBLEM_LOADING });
  return axios
    .post(`${baseURL}/dayscode/add_lesson`, lesson)
    .then((res) => {
      if (res.status === 200) {
        dispatch({ type: SET_PROBLEM_LOADING });
        return { success: true, lesson: res?.data?.lesson };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: SET_PROBLEM_LOADING });
      console.log("add lesson Error", error);
      triggerNotifier({
        message: error?.response?.data?.message,
        type: "error",
        duration: 2000,
        icon: "⚠️",
      });
    });
};

export const editLesson = (lesson) => (dispatch) => {
  dispatch({ type: SET_PROBLEM_LOADING });
  return axios
    .put(`${baseURL}/dayscode/edit_lesson`, lesson)
    .then((res) => {
      if (res.status === 200) {
        dispatch({ type: SET_PROBLEM_LOADING });
        dispatch(getLessonDetails(lesson.id));
        return { success: true, lesson: res?.data?.lesson };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: SET_PROBLEM_LOADING });
      console.log("edit lesson Error", error);
      triggerNotifier({
        message: error?.response?.data?.message,
        type: "error",
        duration: 2000,
        icon: "⚠️",
      });
    });
};

export const getLessons = () => (dispatch) => {
  dispatch({ type: GET_PROBLEMS_LOADING });
  return axios
    .get(`${baseURL}/lesson`)
    .then((res) => {
      if (res.status === 200) {
        dispatch(actions.setLessonsList(res?.data?.lessons));
        return { success: true, data: res?.data?.lessons };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: GET_PROBLEMS_LOADING });
      console.log("Lessons List Error", error);
      triggerNotifier({
        message: error?.response?.data?.message,
        type: "error",
        duration: 2000,
        icon: "⚠️",
      });
    });
};

export const getLessonDetails = (id) => (dispatch) => {
  dispatch({ type: GET_PROBLEM_DETAILS_LOADING });
  return axios
    .get(`${baseURL}/dayscode/get_lesson/${id}`)
    .then(({ data }) => {
      if (data.success) {
        dispatch(actions.setLessonDetails(data.lesson));
        return { success: true, data: data.lesson };
      }
    })
    .catch((error) => {
      dispatch({ type: GET_PROBLEM_DETAILS_LOADING });
      console.log("get problem error", error);
      triggerNotifier({
        message: error?.response?.data?.message,
        type: "error",
        duration: 2000,
        icon: "⚠️",
      });
    });
};

export const removeLesson = (id) => (dispatch) => {
  return axios
    .delete(`${baseURL}/dayscode/delete_lesson/${id}`)
    .then(({ data }) => {
      if (data?.success) {
        dispatch(getLessons());
        return { success: true };
      }
    })
    .catch((error) => {
      console.log("delete lesson error", error);
      triggerNotifier({
        message: error?.response?.data?.message,
        type: "error",
        duration: 2000,
        icon: "⚠️",
      });
    });
};

export const setCompleteLesson = (complete) => (dispatch) => {
  dispatch({ type: SET_SOLUTION_SUBMISSION_LOADING });
  return axios
    .put(`${baseURL}/dayscode/complete_lesson`, complete)
    .then((res) => {
      if (res.status === 200) {
        dispatch({ type: SET_SOLUTION_SUBMISSION_LOADING });
        dispatch({ type: SET_LESSON_COMPLETED_STATUS });
        return {
          success: true,
          result: res?.data?.result,
          completedBy: res?.data?.completedBy,
        };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: SET_SOLUTION_SUBMISSION_LOADING });
      console.log("complete lesson Error", error);
      triggerNotifier({
        message: error?.response?.data?.message,
        type: "error",
        duration: 2000,
        icon: "⚠️",
      });
    });
};

// -----------------Quiz-Challenge------------

export const createQuizChallenge = (quizChallenge) => (dispatch) => {
  dispatch({ type: SET_PROBLEM_LOADING });
  return axios
    .post(`${baseURL}/dayscode/add_quiz_challenge`, quizChallenge)
    .then((res) => {
      if (res.status === 200) {
        dispatch({ type: SET_PROBLEM_LOADING });
        return {
          success: true,
          quizChallenge: res?.data?.quizChallenge,
        };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: SET_PROBLEM_LOADING });
      console.log("add quizChallenge Error", error);
      triggerNotifier({
        message: error?.response?.data?.message,
        type: "error",
        duration: 2000,
        icon: "⚠️",
      });
    });
};

export const editQuizChallenge = (quizChallenge) => (dispatch) => {
  dispatch({ type: SET_PROBLEM_LOADING });
  return axios
    .put(`${baseURL}/dayscode/edit_quiz_challenge`, quizChallenge)
    .then((res) => {
      if (res.status === 200) {
        dispatch({ type: SET_PROBLEM_LOADING });
        dispatch(getQuizChallengeDetails(quizChallenge.id));
        return {
          success: true,
          quizChallenge: res?.data?.quizChallenge,
        };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: SET_PROBLEM_LOADING });
      console.log("edit quiz challenge Error", error);
      triggerNotifier({
        message: error?.response?.data?.message,
        type: "error",
        duration: 2000,
        icon: "⚠️",
      });
    });
};

export const getQuizChallenges =
  ({ status, topic }) =>
  (dispatch) => {
    dispatch({ type: GET_PROBLEMS_LOADING });
    return axios
      .get(
        `${baseURL}/dayscode/fetch_quiz_challenges/${generateQueryParams({
          status: status ? status : "public",
          topic: topic,
        })}`,
      )
      .then((res) => {
        if (res.status === 200) {
          dispatch(
            actions.setQuizChallengesList(res?.data?.quizChallenges),
          );
          return { success: true, data: res?.data?.quizChallenges };
        } else {
          return { success: false };
        }
      })
      .catch((error) => {
        dispatch({ type: GET_PROBLEMS_LOADING });
        console.log("quiz Challenges List Error", error);
        triggerNotifier({
          message: error?.response?.data?.message,
          type: "error",
          duration: 2000,
          icon: "⚠️",
        });
      });
  };

export const getQuizChallengeDetails = (id) => (dispatch) => {
  dispatch({ type: GET_PROBLEM_DETAILS_LOADING });
  return axios
    .get(`${baseURL}/dayscode/get_quiz_challenge/${id}`)
    .then(({ data }) => {
      if (data?.success) {
        dispatch(actions.setQuizChallengeDetails(data.quizChallenge));
        return { success: true, data: data.quizChallenge };
      }
    })
    .catch((error) => {
      dispatch({ type: GET_PROBLEM_DETAILS_LOADING });
      console.log("get quiz_challenge error", error);
      triggerNotifier({
        message: error?.response?.data?.message,
        type: "error",
        duration: 2000,
        icon: "⚠️",
      });
    });
};

export const removeQuizChallenge = (id) => (dispatch) => {
  return axios
    .delete(`${baseURL}/dayscode/delete_quiz_challenge/${id}`)
    .then(({ data }) => {
      if (data?.success) {
        dispatch(getQuizChallenges());
        return { success: true };
      }
    })
    .catch((error) => {
      console.log("delete Quiz Challenge error", error);
      triggerNotifier({
        message: error?.response?.data?.message,
        type: "error",
        duration: 2000,
        icon: "⚠️",
      });
    });
};

export const submitQuizChallenge = (submitQuiz) => (dispatch) => {
  dispatch({ type: SET_SOLUTION_SUBMISSION_LOADING });
  return axios
    .put(`${baseURL}/dayscode/submit_quiz_challenge`, submitQuiz)
    .then((res) => {
      if (res.status === 200) {
        dispatch({ type: SET_SOLUTION_SUBMISSION_LOADING });
        dispatch({
          type: SET_UPDATED_QUIZ_CHALLENGES,
          payload: res?.data?.challenges,
        });

        return {
          success: true,
          result: res?.data?.result,
          completedBy: res?.data?.completedBy,
        };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: SET_SOLUTION_SUBMISSION_LOADING });
      console.log("check quiz challenge Error", error);
      triggerNotifier({
        message: error?.response?.data?.message,
        type: "error",
        duration: 2000,
        icon: "⚠️",
      });
    });
};

export const quizChallengeResult = (quiz) => (dispatch) => {
  dispatch({ type: GET_QUIZ_CHALLENGE_REPORT_LOADING });
  return axios
    .post(`${baseURL}/dayscode/quiz_challenge_result`, quiz)
    .then((res) => {
      if (res.status === 200) {
        dispatch({ type: GET_QUIZ_CHALLENGE_REPORT_LOADING });
        dispatch({ type: SET_CHALLENGES_COMPLETED_STATUS });
        return {
          success: true,
          result: res?.data?.result,
        };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: GET_QUIZ_CHALLENGE_REPORT_LOADING });
      console.log("quiz challenge result Error", error);
      triggerNotifier({
        message: error?.response?.data?.message,
        type: "error",
        duration: 2000,
        icon: "⚠️",
      });
    });
};

// --------All modules content
export const getContentByDays = (day) => (dispatch) => {
  dispatch({ type: GET_CONTENT_DETAILS_LOADING });
  return axios
    .get(`${baseURL}/dayscode/get_content/${day}`)
    .then(({ data }) => {
      if (data?.success) {
        dispatch({ type: GET_CONTENT_DETAILS_LOADING });
        dispatch(actions.setContentDetails(data.contentDetails));
        return { success: true, contentDetails: data.contentDetails };
      }
    })
    .catch((error) => {
      dispatch({ type: GET_CONTENT_DETAILS_LOADING });
      console.log("get content error", error);
      triggerNotifier({
        message: error?.response?.data?.message,
        type: "error",
        duration: 2000,
        icon: "⚠️",
      });
    });
};

export const getContentsList = (status) => (dispatch) => {
  dispatch({ type: GET_PROBLEMS_LOADING });
  return axios
    .get(`${baseURL}/dayscode/fetch_contents/${status}`)
    .then((res) => {
      if (res.status === 200) {
        dispatch(actions.setContentsList(res?.data?.contents));
        return { success: true, data: res?.data?.contents };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: GET_PROBLEMS_LOADING });
      console.log("contents List Error", error);
      triggerNotifier({
        message: error?.response?.data?.message,
        type: "error",
        duration: 2000,
        icon: "⚠️",
      });
    });
};

// --------Session Attendace-------------------

export const addUserAttendancePresent = (id, userAttendance) => (dispatch) => {
  dispatch({ type: SET_USER_ATTENDANCE_PRESENT_LOADING });
  return axios
    .put(`${baseURL}/dayscode/add_user_attendance/${id}`, userAttendance)
    .then((res) => {
      if (res.status === 200) {
        dispatch({ type: SET_USER_ATTENDANCE_PRESENT_LOADING });
        dispatch(
          getDaysUsersListOptions(
            userAttendance.mentor,
            userAttendance.isAdmin,
          ),
        );
        return { success: true };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: SET_USER_ATTENDANCE_PRESENT_LOADING });
      console.log("add user attendance present Error", error);
      triggerNotifier({
        message: error?.response?.data?.message,
        type: "error",
        duration: 2000,
        icon: "⚠️",
      });
    });
};

export const deleteUserAttendancePresent = (id, data) => (dispatch) => {
  dispatch({ type: SET_USER_ATTENDANCE_PRESENT_LOADING });
  return axios
    .put(`${baseURL}/dayscode/delete_user_attendance/${id}`, data)
    .then((res) => {
      if (res.status === 200) {
        dispatch({ type: SET_USER_ATTENDANCE_PRESENT_LOADING });
        dispatch(getDaysUsersListOptions(data.mentor, data.isAdmin));
        return { success: true };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: SET_USER_ATTENDANCE_PRESENT_LOADING });
      console.log("delete user attendance present Error", error);
      triggerNotifier({
        message: error?.response?.data?.message,
        type: "error",
        duration: 2000,
        icon: "⚠️",
      });
    });
};

//  Assign Mentor =======================
export const assignUserMentor = (id, mentor) => (dispatch) => {
  dispatch({ type: SET_USER_ATTENDANCE_PRESENT_LOADING });
  return axios
    .put(`${baseURL}/dayscode/assign_user_mentor/${id}`, mentor)
    .then((res) => {
      if (res.status === 200) {
        dispatch({ type: SET_USER_ATTENDANCE_PRESENT_LOADING });
        dispatch(getDaysUsers());
        return { success: true };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: SET_USER_ATTENDANCE_PRESENT_LOADING });
      console.log("assign mentor user Error", error);
      triggerNotifier({
        message: error?.response?.data?.message,
        type: "error",
        duration: 2000,
        icon: "⚠️",
      });
    });
};

export const deleteAssignedUserMentor = (id, mentor) => (dispatch) => {
  dispatch({ type: SET_USER_ATTENDANCE_PRESENT_LOADING });
  return axios
    .put(`${baseURL}/dayscode/delete_user_mentor/${id}`, mentor)
    .then((res) => {
      if (res.status === 200) {
        dispatch({ type: SET_USER_ATTENDANCE_PRESENT_LOADING });
        dispatch(getDaysUsers());
        return { success: true };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: SET_USER_ATTENDANCE_PRESENT_LOADING });
      console.log("delete assigned mentor user Error", error);
      triggerNotifier({
        message: error?.response?.data?.message,
        type: "error",
        duration: 2000,
        icon: "⚠️",
      });
    });
};

//  Add User Feedback =======================
export const addUserFeedback = (id, feedback) => (dispatch) => {
  dispatch({ type: SET_USER_FEEDBACK_LOADING });
  return axios
    .put(`${baseURL}/dayscode/add_user_feedback/${id}`, feedback)
    .then((res) => {
      if (res.status === 200) {
        dispatch({ type: SET_USER_FEEDBACK_LOADING });
        return { success: true };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: SET_USER_FEEDBACK_LOADING });
      console.log("add user feedback error", error);
      triggerNotifier({
        message: error?.response?.data?.message,
        type: "error",
        duration: 2000,
        icon: "⚠️",
      });
    });
};

export const deleteUserFeedback = (id, feedback) => (dispatch) => {
  dispatch({ type: SET_USER_FEEDBACK_LOADING });
  return axios
    .put(`${baseURL}/dayscode/delete_user_feedback/${id}`, feedback)
    .then((res) => {
      if (res.status === 200) {
        dispatch({ type: SET_USER_FEEDBACK_LOADING });
        dispatch(getAllUsersFeedbacks(feedback.batchId));
        return { success: true };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: SET_USER_FEEDBACK_LOADING });
      console.log("delete user feedback Error", error);
      triggerNotifier({
        message: error?.response?.data?.message,
        type: "error",
        duration: 2000,
        icon: "⚠️",
      });
    });
};

export const getAllUsersFeedbacks = (batchId) => (dispatch) => {
  dispatch({ type: GET_ALL_USERS_FEEDBACKS_LOADING });
  return axios
    .get(`${baseURL}/dayscode/get_all_feedbacks/${batchId}`)
    .then((res) => {
      if (res.status === 200) {
        dispatch(actions.setAllUsersFeedbacks(res?.data?.feedbacks));
        return { success: true, feedbacks: res?.data?.feedbacks };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: GET_ALL_USERS_FEEDBACKS_LOADING });
      console.log("get all feedbacks Error", error);
      triggerNotifier({
        message: error?.response?.data?.message,
        type: "error",
        duration: 2000,
        icon: "⚠️",
      });
    });
};

export const getUserFeedbacks = (batchId, data) => (dispatch) => {
  dispatch({ type: GET_ALL_USERS_FEEDBACKS_LOADING });
  return axios
    .get(`${baseURL}/dayscode/get_user_feedbacks/${batchId}`, data)
    .then((res) => {
      if (res.status === 200) {
        dispatch({ type: GET_ALL_USERS_FEEDBACKS_LOADING });
        return { success: true, feedbacks: res?.data?.feedbacks };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: GET_ALL_USERS_FEEDBACKS_LOADING });
      console.log("get user feedbacks Error", error);
      triggerNotifier({
        message: error?.response?.data?.message,
        type: "error",
        duration: 2000,
        icon: "⚠️",
      });
    });
};

// All users progress by mentor

export const getAllUsersProgressByMentor =
  (batchId, mentorId, admin) => (dispatch) => {
    dispatch({ type: GET_ALL_USERS_PROGRESS_LOADING });
    return axios
      .get(
        `${baseURL}/dayscode/all_users_progress/${batchId}/${mentorId}/${admin}`,
      )
      .then((res) => {
        if (res.status === 200) {
          dispatch({
            type: GET_ALL_USERS_PROGRESS,
            payload: res?.data?.allSubmissionsByUser,
          });
          return {
            success: true,
            allUsersProgressByMentor: res?.data?.allSubmissionsByUser,
          };
        } else {
          return { success: false };
        }
      })
      .catch((error) => {
        dispatch({ type: GET_ALL_USERS_PROGRESS_LOADING });
        console.log("get user feedbacks Error", error);
        triggerNotifier({
          message: error?.response?.data?.message,
          type: "error",
          duration: 2000,
          icon: "⚠️",
        });
      });
  };

// Download File/Image -------------
export const getFileDownloadDetails = (url, fileName) => (dispatch) => {
  return axios
    .get(`${url}`, { responseType: "blob" })
    .then((res) => {
      if (res.status === 200) {
        return { success: true, data: res?.data };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      console.log("get file data Error", error);
      triggerNotifier({
        message: error?.response?.data?.message,
        type: "error",
        duration: 2000,
        icon: "⚠️",
      });
    });
};

// Code Batches

export const createCodeBatch = (batchData) => (dispatch) => {
  dispatch({ type: GET_ALL_CODE_BATCHES_LOADING });
  return axios
    .post(`${baseURL}/dayscode/code_batch/create`, batchData)
    .then((res) => {
      if (res.status === 200) {
        dispatch({ type: GET_ALL_CODE_BATCHES_LOADING });
        return { success: true, batch: res?.data?.batch };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: GET_ALL_CODE_BATCHES_LOADING });
      console.log("add batch Error", error);
      triggerNotifier({
        message: error?.response?.data?.message,
        type: "error",
        duration: 2000,
        icon: "⚠️",
      });
    });
};

export const editCodeBatch = (batchData) => (dispatch) => {
  dispatch({ type: GET_ALL_CODE_BATCHES_LOADING });
  return axios
    .patch(`${baseURL}/dayscode/code_batch/${batchData._id}`, {
      data: batchData,
    })
    .then((res) => {
      if (res.status === 200) {
        dispatch({ type: GET_ALL_CODE_BATCHES_LOADING });
        return { success: true, batch: res?.data?.batch };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: GET_ALL_CODE_BATCHES_LOADING });
      console.log("edit batch Error", error);
      triggerNotifier({
        message: error?.response?.data?.message,
        type: "error",
        duration: 2000,
        icon: "⚠️",
      });
    });
};

export const getCodeBatches =
  ({
    schedule,
    registrationStatus,
    limit,
    status,
    search,
    type,
    category,
    page,
  }) =>
  (dispatch) => {
    // If there's an ongoing request, abort it
    if (abortController) {
      abortController.abort();
    }
    // Create a new AbortController for the current request
    abortController = new AbortController();
    const signal = abortController.signal;

    dispatch({ type: GET_ALL_CODE_BATCHES_LOADING });
    return axios
      .get(
        `${baseURL}/dayscode/code_batch${generateQueryParams({
          schedule,
          registrationStatus,
          limit,
          status,
          search,
          type,
          category,
          page,
        })}`,
        { signal }, //Pass the signal to axios
      )
      .then((res) => {
        if (res.status === 200) {
          dispatch({ type: GET_ALL_CODE_BATCHES, payload: res.data });
          return { success: true, batches: res?.data?.batches };
        } else {
          return { success: false };
        }
      })
      .catch((error) => {
        dispatch({ type: GET_ALL_CODE_BATCHES_LOADING });
        console.log("get all code batches Error", error);
        triggerNotifier({
          message: error?.response?.data?.message,
          type: "error",
          duration: 2000,
          icon: "⚠️",
        });
      })
      .finally(() => {
        // Clear the abort controller after the request completes or fails
        abortController = null;
      });
  };

export const getMoreCodeBatches =
  ({
    schedule,
    registrationStatus,
    limit,
    status,
    search,
    type,
    category,
    page,
  }) =>
  (dispatch) => {
    // If there's an ongoing request, abort it
    if (abortController) {
      abortController.abort();
    }
    // Create a new AbortController for the current request
    abortController = new AbortController();
    const signal = abortController.signal;

    dispatch({ type: GET_MORE_CODE_BATCHES_LOADING });
    return axios
      .get(
        `${baseURL}/dayscode/code_batch${generateQueryParams({
          schedule,
          registrationStatus,
          limit,
          status,
          search,
          type,
          category,
          page,
        })}`,
        { signal },
      )
      .then((res) => {
        if (res.status === 200) {
          dispatch({ type: GET_MORE_CODE_BATCHES, payload: res?.data.batches });
          return { success: true, batches: res?.data?.batches };
        } else {
          return { success: false };
        }
      })
      .catch((error) => {
        dispatch({ type: GET_MORE_CODE_BATCHES_LOADING });
        console.log("get MORE code batches Error", error);
        triggerNotifier({
          message: error?.response?.data?.message,
          type: "error",
          duration: 2000,
          icon: "⚠️",
        });
      })
      .finally(() => {
        // Clear the abort controller after the request completes or fails
        abortController = null;
      });
  };

export const getCodeBatchDetails = (batch) => (dispatch) => {
  dispatch({ type: GET_ALL_CODE_BATCHES_LOADING });
  return axios
    .get(`${baseURL}/dayscode/code_batch/${batch}`)
    .then((res) => {
      if (res.status === 200) {
        dispatch({ type: GET_CODE_BATCH, payload: res.data });
        return { success: true, batch: res?.data?.batch };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: GET_ALL_CODE_BATCHES_LOADING });
      console.log("get all code batches Error", error);
      triggerNotifier({
        message: error?.response?.data?.message,
        type: "error",
        duration: 2000,
        icon: "⚠️",
      });
    });
};

export const deleteCodeBatch = (id) => (dispatch) => {
  return axios
    .delete(`${baseURL}/dayscode/code_batch/${id}`)
    .then(({ data }) => {
      if (data.success) {
        dispatch(getCodeBatches());
        return { success: true };
      }
    })
    .catch((error) => {
      console.log("delete batch error", error);
      triggerNotifier({
        message: error?.response?.data?.message,
        type: "error",
        duration: 2000,
        icon: "⚠️",
      });
    });
};

export const addProblemIntoBatch = (batchData) => (dispatch) => {
  return axios
    .post(`${baseURL}/dayscode/code_batch/content/problem`, batchData)
    .then((res) => {
      if (res.status === 200) {
        dispatch(getCodeBatchDetails(batchData.batch));
        toast.success("Problem added into batch", {
          duration: 2000,
        });
        return { success: true, batch: res?.data?.batch };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      // toast.success(res.message.data.message, {
      //   duration: 2000,
      // })
      console.log("edit batch Error", error);
      triggerNotifier({
        message: error?.response?.data?.message,
        type: "error",
        duration: 2000,
        icon: "⚠️",
      });
    });
};

// getBatchProblems

export const getBatchProblems =
  ({ id, status, problem_type }) =>
  (dispatch) => {
    dispatch({ type: GET_PROBLEMS_LOADING });
    return axios
      .get(
        `${baseURL}/dayscode/code_batch/content/problem${generateQueryParams({
          batch: id,
          status: status ? status : null,
          problem_type: problem_type ? problem_type : null,
        })}`,
      )
      .then((res) => {
        if (res.status === 200) {
          dispatch(
            actions.setBatchProblemsList(res?.data?.batchProblems),
          );
          return { success: true, data: res?.data?.batchProblems };
        } else {
          return { success: false };
        }
      })
      .catch((error) => {
        dispatch({ type: GET_PROBLEMS_LOADING });
        console.log("batch problem List Error", error);
        triggerNotifier({
          message: error?.response?.data?.message,
          type: "error",
          duration: 2000,
          icon: "⚠️",
        });
      });
  };

export const editProblemIntoBatch = (batchData) => (dispatch) => {
  return axios
    .patch(`${baseURL}/dayscode/code_batch/content/problem/${batchData.id}`, {
      data: batchData,
    })
    .then((res) => {
      if (res.status === 200) {
        dispatch(getBatchProblems({ id: batchData.batch }));
        toast.success("Problem edited into batch", {
          duration: 2000,
        });
        return { success: true, batch: res?.data?.batch };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      // toast.success(res.message.data.message, {
      //   duration: 2000,
      // })
      console.log("edit batch problem Error", error);
      triggerNotifier({
        message: error?.response?.data?.message,
        type: "error",
        duration: 2000,
        icon: "⚠️",
      });
    });
};

export const deleteProblemFromBatch = (batchData) => (dispatch) => {
  return axios
    .delete(
      `${baseURL}/dayscode/code_batch/problem/${batchData.batch}/${batchData.problem}`,
    )
    .then((res) => {
      if (res.status === 200) {
        dispatch(getBatchProblems({ id: batchData.batch }));
        dispatch(getCodeBatchDetails(batchData.batch));
        toast.success("Problem removed from batch", {
          duration: 2000,
        });
        return { success: true, batch: res?.data?.batch };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      console.log("edit batch Error", error);
      triggerNotifier({
        message: error?.response?.data?.message,
        type: "error",
        duration: 2000,
        icon: "⚠️",
      });
    });
};

export const addLessonIntoBatch = (batchData) => (dispatch) => {
  return axios
    .post(`${baseURL}/dayscode/code_batch/content/lesson`, batchData)
    .then((res) => {
      if (res.status === 200) {
        dispatch(getCodeBatchDetails(batchData.batch));
        toast.success("Lesson added into batch", {
          duration: 2000,
        });
        return { success: true, batch: res?.data?.batch };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      console.log("edit batch Error", error);
      triggerNotifier({
        message: error?.response?.data?.message,
        type: "error",
        duration: 2000,
        icon: "⚠️",
      });
    });
};

export const getBatchLessons =
  ({ id, status }) =>
  (dispatch) => {
    dispatch({ type: GET_PROBLEMS_LOADING });
    return axios
      .get(
        `${baseURL}/dayscode/code_batch/content/lesson${generateQueryParams({
          batch: id,
          status: status,
        })}`,
      )
      .then((res) => {
        if (res.status === 200) {
          dispatch(
            actions.setBatchLessonsList(res?.data?.batchLessons),
          );
          return { success: true, data: res?.data?.batchLessons };
        } else {
          return { success: false };
        }
      })
      .catch((error) => {
        dispatch({ type: GET_PROBLEMS_LOADING });
        console.log("batch lesson List Error", error);
        triggerNotifier({
          message: error?.response?.data?.message,
          type: "error",
          duration: 2000,
          icon: "⚠️",
        });
      });
  };

export const editLessonIntoBatch = (batchData) => (dispatch) => {
  return axios
    .patch(`${baseURL}/dayscode/code_batch/content/lesson/${batchData.id}`, {
      data: batchData,
    })
    .then((res) => {
      if (res.status === 200) {
        dispatch(getBatchLessons({ id: batchData.batch }));
        toast.success("Lesson edited into batch", {
          duration: 2000,
        });
        return { success: true, batch: res?.data?.batch };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      console.log("edit batch Error", error);
      triggerNotifier({
        message: error?.response?.data?.message,
        type: "error",
        duration: 2000,
        icon: "⚠️",
      });
    });
};

export const deleteLessonFromBatch = (batchData) => (dispatch) => {
  return axios
    .delete(
      `${baseURL}/dayscode/code_batch/lesson/${batchData.batch}/${batchData.lesson}`,
    )
    .then((res) => {
      if (res.status === 200) {
        dispatch(getBatchLessons({ id: batchData.batch }));
        toast.success("Lesson removed from batch", {
          duration: 2000,
        });
        return { success: true, batch: res?.data?.batch };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      console.log("edit batch Error", error);
      triggerNotifier({
        message: error?.response?.data?.message,
        type: "error",
        duration: 2000,
        icon: "⚠️",
      });
    });
};

export const addQuizIntoBatch = (batchData) => (dispatch) => {
  return axios
    .post(`${baseURL}/dayscode/code_batch/content/quiz`, batchData)
    .then((res) => {
      if (res.status === 200) {
        dispatch(getCodeBatchDetails(batchData.batch));
        toast.success("Quiz added into batch", {
          duration: 2000,
        });
        return { success: true, batch: res?.data?.batch };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      console.log("edit batch Error", error);
      triggerNotifier({
        message: error?.response?.data?.message,
        type: "error",
        duration: 2000,
        icon: "⚠️",
      });
    });
};

export const getBatchQuiz =
  ({ id, status }) =>
  (dispatch) => {
    dispatch({ type: GET_PROBLEMS_LOADING });
    return axios
      .get(
        `${baseURL}/dayscode/code_batch/content/quiz${generateQueryParams({
          batch: id,
          status: status ? status : null,
        })}`,
      )
      .then((res) => {
        if (res.status === 200) {
          dispatch(
            actions.setBatchQuizChallengesList(
              res?.data?.batchQuizes,
            ),
          );
          return { success: true, data: res?.data?.batchQuizes };
        } else {
          return { success: false };
        }
      })
      .catch((error) => {
        dispatch({ type: GET_PROBLEMS_LOADING });
        console.log("batch problem List Error", error);
        triggerNotifier({
          message: error?.response?.data?.message,
          type: "error",
          duration: 2000,
          icon: "⚠️",
        });
      });
  };

export const editQuizIntoBatch = (batchData) => (dispatch) => {
  return axios
    .patch(`${baseURL}/dayscode/code_batch/content/quiz/${batchData.id}`, {
      data: batchData,
    })
    .then((res) => {
      if (res.status === 200) {
        dispatch(getBatchQuiz({ id: batchData.batch }));
        toast.success("Quiz edited into batch", {
          duration: 2000,
        });
        return { success: true, batch: res?.data?.batch };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      console.log("edit batch Error", error);
      triggerNotifier({
        message: error?.response?.data?.message,
        type: "error",
        duration: 2000,
        icon: "⚠️",
      });
    });
};

export const deleteQuizFromBatch = (batchData) => (dispatch) => {
  return axios
    .delete(
      `${baseURL}/dayscode/code_batch/quiz/${batchData.batch}/${batchData.quiz}`,
    )
    .then((res) => {
      if (res.status === 200) {
        dispatch(getBatchQuiz({ id: batchData.batch }));
        toast.success("Quiz removed from batch", {
          duration: 2000,
        });
        return { success: true, batch: res?.data?.batch };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      console.log("edit batch Error", error);
      triggerNotifier({
        message: error?.response?.data?.message,
        type: "error",
        duration: 2000,
        icon: "⚠️",
      });
    });
};

export const addMentorIntoBatch = (batchData) => (dispatch) => {
  return axios
    .post(
      `${baseURL}/dayscode/code_batch/mentor/${batchData.batch}/${batchData.mentor}`,
    )
    .then((res) => {
      if (res.status === 200) {
        dispatch(getCodeBatchDetails(batchData.batch));
        toast.success("Mentor added into batch", {
          duration: 2000,
        });
        return { success: true, batch: res?.data?.batch };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      console.log("edit batch Error", error);
      triggerNotifier({
        message: error?.response?.data?.message,
        type: "error",
        duration: 2000,
        icon: "⚠️",
      });
    });
};

export const createMentorBatch = (batchData) => (dispatch) => {
  return axios
    .post(`${baseURL}/dayscode/mentor`, batchData)
    .then((res) => {
      if (res.status === 200) {
        dispatch(getCodeBatchDetails(batchData.batch));
        toast.success("Mentor Created in a batch ", {
          duration: 2000,
        });
        return { success: true, batch: res?.data?.batch };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      console.log("Error in create Batch Mentor", error);
      triggerNotifier({
        message: error?.response?.data?.message,
        type: "error",
        duration: 2000,
        icon: "⚠️",
      });
    });
};

export const editMentorIntoBatch = (batchData) => (dispatch) => {
  return axios
    .patch(`${baseURL}/dayscode/mentor/${batchData.id}`, {
      data: batchData,
    })
    .then((res) => {
      if (res.status === 200) {
        dispatch(getBatchMentors({ batch: batchData.id }));
        toast.success("Mentor edited into batch", {
          duration: 2000,
        });
        return { success: true, batch: res?.data?.batch };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      // toast.success(res.message.data.message, {
      //   duration: 2000,
      // })
      console.log("edit batch mentor Error", error);
      triggerNotifier({
        message: error?.response?.data?.message,
        type: "error",
        duration: 2000,
        icon: "⚠️",
      });
    });
};

export const deleteMentorFromBatch = (batchData) => (dispatch) => {
  return axios
    .delete(`${baseURL}/dayscode/mentor/${batchData.mentor}`)
    .then((res) => {
      if (res.status === 200) {
        dispatch(getBatchMentors({ batch: batchData.batch }));
        toast.success("Mentor removed from batch", {
          duration: 2000,
        });
        return { success: true, batch: res?.data?.batch };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      console.log("edit batch Error", error);
      triggerNotifier({
        message: error?.response?.data?.message,
        type: "error",
        duration: 2000,
        icon: "⚠️",
      });
    });
};

export const getBatchMentors =
  ({ batch, role }) =>
  (dispatch) => {
    dispatch({ type: GET_PROBLEMS_LOADING });
    return axios
      .get(
        `${baseURL}/dayscode/mentors/${generateQueryParams({
          batch: batch,
          role: role,
        })}`,
      )
      .then((res) => {
        if (res.status === 200) {
          dispatch(actions.setBatchMentorList(res?.data?.mentors));
          return { success: true, batch: res?.data?.mentors };
        } else {
          return { success: false };
        }
      })
      .catch((error) => {
        console.log("Error in getting Batch Mentor", error);
        triggerNotifier({
          message: error?.response?.data?.message,
          type: "error",
          duration: 2000,
          icon: "⚠️",
        });
      });
  };

export const getDaysCodeMentors = () => (dispatch) => {
  return axios
    .get(`${baseURL}/dayscode/mentors`)
    .then((res) => {
      if (res.status === 200) {
        // dispatch(getCodeBatchDetails(batchData.batch));
        dispatch({ type: SET_MENTOR_LIST, payload: res?.data });
        return { success: true, mentors: res?.data?.mentors };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      console.log("Error in getting Batch Mentor", error);
      triggerNotifier({
        message: error?.response?.data?.message,
        type: "error",
        duration: 2000,
        icon: "⚠️",
      });
    });
};

export const deleteDaysCodeMentor = (id) => (dispatch) => {
  return axios
    .delete(`${baseURL}/dayscode/mentor/${id}`)
    .then((res) => {
      if (res.status === 200) {
        dispatch({ type: SET_MENTOR_LIST, payload: res?.data });
        return { success: true, mentors: res?.data?.mentors };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      console.log("Error in getting Batch Mentor", error);
      triggerNotifier({
        message: error?.response?.data?.message,
        type: "error",
        duration: 2000,
        icon: "⚠️",
      });
    });
};

export const getSpeakers = (data) => (dispatch) => {
  dispatch({ type: GET_SPEAKERS_LIST_LOADING });
  return axios
    .get(
      `${baseURL}/user/speakers${generateQueryParams({
        limit: data.limit,
        page: data.page,
        search: data?.search,
        technology: data?.technology,
      })}`,
    )
    .then((res) => {
      if (res.status === 200) {
        dispatch(actions.setSpeakersList(res.data));
        dispatch({ type: GET_SPEAKERS_LIST_LOADING });
        return { success: true, speakers: res?.data?.Speakers };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: GET_SPEAKERS_LIST_LOADING });
      console.log("Get Speakers Error", error);
      triggerNotifier({
        message: error?.response?.data?.message,
        type: "error",
        duration: 2000,
        icon: "⚠️",
      });
    });
};

export const getParticipants = (data) => (dispatch) => {
  dispatch({ type: GET_SPEAKERS_LIST_LOADING });
  return axios
    .get(
      `${baseURL}/dayscode/fetch_user${generateQueryParams({
        search: data?.search,
      })}`,
    )
    .then(({ data }) => {
      if (data.success) {
        dispatch(actions.setDaysUsersList(data.participants));
        dispatch({ type: GET_PROBLEMS_LOADING });
        return { success: true, data: data.participants };
      }
    })
    .catch((error) => {
      dispatch({ type: GET_PROBLEMS_LOADING });
      console.log("get days users error", error);
      triggerNotifier({
        message: error?.response?.data?.message,
        type: "error",
        duration: 2000,
        icon: "⚠️",
      });
    });
};

/* export const deleteParticipantFromBatch = (batchData) => (dispatch) => {
  return axios
    .delete(
      `${baseURL}/dayscode/code_batch/participant/${batchData.batch}/${batchData.participant}`,
    )
    .then((res) => {
      if (res.status === 200) {
        dispatch(getCodeBatchDetails(batchData.batch));
        toast.success("Participant removed from batch", {
          duration: 2000,
        });
        return { success: true, batch: res?.data?.batch };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      console.log("Error in participant deletion", error);
      triggerNotifier({
        message: error?.response?.data?.message,
        type: "error",
        duration: 2000,
        icon: "⚠️",
      });
    });
}; */

export const createBatchRoadMap = (batchData) => (dispatch) => {
  // If there's an ongoing request, abort it
  if (abortController) {
    abortController.abort();
  }
  // Create a new AbortController for the current request
  abortController = new AbortController();
  const signal = abortController.signal;
  return axios
    .post(`${baseURL}/dayscode/roadmap/create`, batchData, { signal })
    .then((res) => {
      if (res.status === 200) {
        dispatch(getCodeBatchDetails(batchData.batch));
        console.log(batchData.batch);
        toast.success("RoadMap created", {
          duration: 2000,
        });
        return { success: true, batch: res?.data?.batch };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      console.log("Error in creating Roadmap", error);
      console.log("eror", error?.response?.data.message);
      dispatch(actions.setContentError(error?.response?.data.message));
      triggerNotifier({
        message: error?.response?.data.message,
        type: "error",
        duration: 2000,
        icon: "⚠️",
      });
    })
    .finally(() => {
      // Clear the abort controller after the request completes or fails
      abortController = null;
    });
};

export const getBatchRoadmap = (data) => (dispatch) => {
  dispatch({ type: GET_SPEAKERS_LIST_LOADING });
  return axios
    .get(
      `${baseURL}/dayscode/roadmap${generateQueryParams({
        batch: data,
      })}`,
    )
    .then((res) => {
      if (res.status === 200) {
        dispatch(actions.setRoadmapList(res.data));
        dispatch({ type: GET_SPEAKERS_LIST_LOADING });
        return { success: true, roadmaps: res?.data?.roadmaps };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: GET_SPEAKERS_LIST_LOADING });
      console.log("Get Roadmap Error", error);
      triggerNotifier({
        message: error?.response?.data?.message,
        type: "error",
        duration: 2000,
        icon: "⚠️",
      });
    });
};

export const deleteRoadmapfromBatch = (batchData) => (dispatch) => {
  return axios
    .delete(`${baseURL}/dayscode/roadmap/${batchData.roadmap}`)
    .then((res) => {
      if (res.status === 200) {
        dispatch(getCodeBatchDetails(batchData.batch));
        toast.success("Roadmap removed from batch", {
          duration: 2000,
        });
        return { success: true, batch: res?.data?.batch };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      console.log("Error in Roadmap deletion", error);
      triggerNotifier({
        message: error?.response?.data?.message,
        type: "error",
        duration: 2000,
        icon: "⚠️",
      });
    });
};

export const addParticipantToBatch = (batchData) => (dispatch) => {
  return axios
    .post(`${baseURL}/dayscode/code_batch/participant/create`, batchData)
    .then((res) => {
      if (res.status === 200) {
        dispatch(getBatchParticipants({ batch: batchData?.batch }));
        toast.success("Participant Added In Batch", {
          duration: 2000,
        });
        return { success: true, batch: res?.data?.batch };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      console.log("Error in adding Participant in Batch", error);
      triggerNotifier({
        message: error?.response?.data?.message,
        type: "error",
        duration: 2000,
        icon: "⚠️",
      });
    });
};

export const editBatchRoadMap = (roadmap) => (dispatch) => {
  return axios
    .patch(`${baseURL}/dayscode/roadmap/${roadmap._id}`, { data: roadmap })
    .then((res) => {
      if (res.status === 200) {
        dispatch(getCodeBatchDetails(roadmap.batch));
        toast.success("RoadMap updated", {
          duration: 2000,
        });
        return { success: true, batch: res?.data?.batch };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      console.log("Error in editing roadmap", error);
      dispatch({
        type: "SET_CONTENT_ERROR",
        payload: error?.response?.data?.message,
      });
      triggerNotifier({
        message: error?.response?.data?.message,
        type: "error",
        duration: 2000,
        icon: "⚠️",
      });
    });
};
//--------------Create Code Class ---------------------------------
export const createCodeClass = (codeClass) => (dispatch) => {
  console.log("codeClass oper", codeClass);
  dispatch({ type: GET_CREATE_CODE_CLASS_LOADING });
  return axios
    .post(`${baseURL}/dayscode/class/create`, codeClass)
    .then((res) => {
      if (res.status === 200) {
        triggerNotifier({
          message: "Create Code Class",
          type: "success",
          duration: 1000,
          icon: "👏",
        });
        dispatch({ type: GET_CREATE_CODE_CLASS_LOADING });
        return { success: true, coldeClass: res?.data?.coldeClass };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: GET_CREATE_CODE_CLASS_LOADING });
      console.log("Create Code Class Error", error);
      triggerNotifier({
        message: error?.response?.data?.message,
        type: "error",
        duration: 2000,
        icon: "⚠️",
      });
    });
};

export const getBatchParticipants =
  ({ batch, limit, status, search }) =>
  (dispatch) => {
    dispatch({ type: GET_PROBLEMS_LOADING });
    return axios
      .get(
        `${baseURL}/dayscode/code_batch/participants${generateQueryParams({
          batch,
          limit: limit ? limit : null,
          status: status ? status : null,
          search: search ? search : "",
        })}`,
      )
      .then((res) => {
        if (res.status === 200) {
          dispatch(
            actions.setBatchParticipantsList(res?.data?.participants),
          );
          return { success: true, data: res?.data?.participants };
        } else {
          return { success: false };
        }
      })
      .catch((error) => {
        //dispatch({ type: GET_PROBLEMS_LOADING });
        console.log("batch participantList Error", error);
        triggerNotifier({
          message: error?.response?.data?.message,
          type: "error",
          duration: 2000,
          icon: "⚠️",
        });
      });
  };

export const editParticipantIntoBatch = (batchData) => (dispatch) => {
  return axios
    .patch(`${baseURL}/dayscode/code_batch/participant/${batchData.id}`, {
      data: batchData.status,
    })
    .then((res) => {
      if (res.status === 200) {
        //console.log(batchData.batch)
        // dispatch(getBatchParticipants({ batch: batchData.batch }));
        toast.success("Participant Status edited into batch", {
          duration: 2000,
        });
        return { success: true, batch: res?.data?.batch };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      // toast.success(res.message.data.message, {
      //   duration: 2000,
      // })
      console.log("edit batch problem Error", error);
      triggerNotifier({
        message: error?.response?.data?.message,
        type: "error",
        duration: 2000,
        icon: "⚠️",
      });
    });
};

export const deleteParticipantFromBatch =
  ({ id, batch }) =>
  (dispatch) => {
    return axios
      .delete(`${baseURL}/dayscode/code_batch/participant/${id}`)
      .then((res) => {
        if (res.status === 200) {
          dispatch(getBatchParticipants({ batch }));
          toast.success("Participant removed from batch", {
            duration: 2000,
          });
          return { success: true, batch: res?.data?.batch };
        } else {
          return { success: false };
        }
      })
      .catch((error) => {
        console.log("Error in participant deletion", error);
      });
  };
//-----------------------Get Code Class Details-------------------
export const getCodeClassDetails = (codeClass) => (dispatch) => {
  dispatch({ type: GET_CODE_CLASS_DETAILS_LOADING });
  return axios
    .get(`${baseURL}/dayscode/class/${codeClass}`)
    .then(({ data }) => {
      if (data.success) {
        dispatch(actions.setCodeClassDetails(data.class));
        return { success: true, data: data.class };
      }
    })
    .catch((error) => {
      dispatch({ type: GET_CODE_CLASS_DETAILS_LOADING });
      console.log("get Code Class error", error);
      triggerNotifier({
        message: error?.response?.data?.message,
        type: "error",
        duration: 2000,
        icon: "⚠️",
      });
    });
};

export const getCodeClasses = () => (dispatch) => {
  dispatch({ type: GET_CODE_CLASSES_LOADING });
  return axios
    .get(`${baseURL}/dayscode/class`)
    .then(({ data }) => {
      if (data.success === true) {
        triggerNotifier({
          message: "Code Classes Loaded",
          type: "success",
          duration: 1000,
          icon: "👏",
        });
        dispatch(actions.setCodeClassess(data.classes));
        return { success: true, data: data.classes };
      }
    })
    .catch((error) => {
      dispatch({ type: GET_CODE_CLASSES_LOADING });
      console.log("get Code Classes error", error);
      triggerNotifier({
        message: error?.response?.data?.message,
        type: "error",
        duration: 2000,
        icon: "⚠️",
      });
    });
};

export const getBatchLeaderboard = (batch) => (dispatch) => {
  dispatch({ type: GET_BATCH_LEADERBOARD_LOADING });
  return axios
    .get(`${baseURL}/dayscode/code_batch/${batch}/leaderboard`)
    .then((res) => {
      if (res.status === 200) {
        // console.log("result", res);
        dispatch(actions.setBatchLeaderBoard(res.data));
        //dispatch({ type: GET_SPEAKERS_LIST_LOADING });
        return { success: true, leaderboard: res?.data?.leaderboard };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: GET_BATCH_LEADERBOARD_LOADING });
      console.log("Get batch leaderboard Error", error);
      triggerNotifier({
        message: error?.response?.data?.message,
        type: "error",
        duration: 2000,
        icon: "⚠️",
      });
    });
};

//------------Edit Code Class --------------------
export const editCodeClass = (codeClass) => (dispatch) => {
  dispatch({ type: GET_CREATE_CODE_CLASS_LOADING });
  return axios
    .patch(`${baseURL}/dayscode/class/${codeClass._id}`, { data: codeClass })
    .then((res) => {
      if (res.status === 200) {
        triggerNotifier({
          message: "Edit Code Class",
          type: "success",
          duration: 1000,
          icon: "👏",
        });
        dispatch({ type: GET_CREATE_CODE_CLASS_LOADING });
        dispatch(getCodeClassDetails(codeClass.id));
        return { success: true, codeClass: res?.data?.codeClass };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: GET_PROBLEMS_LOADING });
      console.log("batch participantList Error", error);
      dispatch({ type: GET_CREATE_CODE_CLASS_LOADING });
      console.log("edit Code Class Error", error);
      triggerNotifier({
        message: error?.response?.data?.message,
        type: "error",
        duration: 2000,
        icon: "⚠️",
      });
    });
};

//----------------------Add Attendance into Class--------------
export const addAttendanceInToClass =
  (codeClass, participants) => (dispatch) => {
    dispatch({ type: SET_USER_ATTENDANCE_PRESENT_LOADING });
    return axios
      .patch(`${baseURL}/dayscode/class/${codeClass}/add/attendance`, {
        data: participants,
      })
      .then((res) => {
        if (res.status === 200) {
          triggerNotifier({
            message: "Class Attendance Done",
            type: "success",
            duration: 1000,
            icon: "👏",
          });
          dispatch({ type: SET_USER_ATTENDANCE_PRESENT_LOADING });
          return { success: true };
        } else {
          return { success: false };
        }
      })
      .catch((error) => {
        dispatch({ type: SET_USER_ATTENDANCE_PRESENT_LOADING });
        console.log("add user attendance present Error", error);
        triggerNotifier({
          message: error?.response?.data?.message,
          type: "error",
          duration: 2000,
          icon: "⚠️",
        });
      });
  };
//----------------get Code Class Participants-------------
export const getCodeClassParticipants = (codeClass) => (dispatch) => {
  //dispatch({ type: GET_CODE_CLASS_PARTICIPANTS_LOADING });
  return axios
    .get(`${baseURL}/dayscode/class/${codeClass}/participants`)
    .then(({ data }) => {
      if (data.success === true) {
        triggerNotifier({
          message: "Code Class Participants Loaded",
          type: "success",
          duration: 1000,
          icon: "👏",
        });
        dispatch(actions.setCodeClassParticipants(data.allParticipants));
        return { success: true, data: data.allParticipants };
      }
    })
    .catch((error) => {
      // dispatch({ type: GET_CODE_CLASS_PARTICIPANTS_LOADING });
      console.log("get Code Class participants error", error);
      triggerNotifier({
        message: error?.response?.res?.message,
        type: "error",
        duration: 2000,
        icon: "⚠️",
      });
    });
};

export const deleteBatchFromClass =
  ({ batch, codeClass }) =>
  (dispatch) => {
    return axios
      .delete(`${baseURL}/dayscode/class/${codeClass}/delete/batch/${batch}`)
      .then(({ data }) => {
        if (data.success) {
          triggerNotifier({
            message: "Delete Batch",
            type: "success",
            duration: 1000,
            icon: "👏",
          });
          dispatch(getCodeClasses());
          return { success: true };
        }
      })
      .catch((error) => {
        console.log(" Delete Batch  error", error);
        triggerNotifier({
          message: error?.response?.data?.message,
          type: "error",
          duration: 2000,
          icon: "⚠️",
        });
      });
  };

export const deleteMentorFromClass =
  ({ mentor, codeClass }) =>
  (dispatch) => {
    return axios
      .delete(`${baseURL}/dayscode/class/${codeClass}/delete/mentor/${mentor}`)
      .then(({ data }) => {
        if (data.success) {
          triggerNotifier({
            message: "Delete Mentor",
            type: "success",
            duration: 1000,
            icon: "👏",
          });
          dispatch(getCodeClasses());
          return { success: true };
        }
      })
      .catch((error) => {
        console.log(" Delete mentor  error", error);
        triggerNotifier({
          message: error?.response?.data?.message,
          type: "error",
          duration: 2000,
          icon: "⚠️",
        });
      });
  };

export const deleteCodeClass = (id) => (dispatch) => {
  return axios
    .delete(`${baseURL}/dayscode/class/${id}`)
    .then(({ data }) => {
      if (data.success) {
        dispatch(getCodeClasses());
        return { success: true };
      }
    })
    .catch((error) => {
      console.log("get Delete Class error", error);
      triggerNotifier({
        message: error?.response?.data?.message,
        type: "error",
        duration: 2000,
        icon: "⚠️",
      });
    });
};

export const addBatchInToClass =
  ({ codeClass, batch }) =>
  (dispatch) => {
    dispatch({ type: GET_ADD_BATCH_IN_TO_CLASS_LOADING });
    return axios
      .patch(`${baseURL}/dayscode/class/${codeClass}/add/batch/${batch}`)
      .then((res) => {
        if (res.status === 200) {
          triggerNotifier({
            message: "Batch Add",
            type: "success",
            duration: 1000,
            icon: "👏",
          });
          dispatch({ type: GET_ADD_BATCH_IN_TO_CLASS_LOADING });
          return { success: true };
        } else {
          return { success: false };
        }
      })
      .catch((error) => {
        dispatch({ type: GET_ADD_BATCH_IN_TO_CLASS_LOADING });
        console.log("add Batch error", error);
        triggerNotifier({
          message: error?.response?.data?.message,
          type: "error",
          duration: 2000,
          icon: "⚠️",
        });
      });
  };

export const addMentorInToClass =
  ({ codeClass, mentor }) =>
  (dispatch) => {
    dispatch({ type: SET_USER_ATTENDANCE_PRESENT_LOADING });
    return axios
      .patch(`${baseURL}/dayscode/class/${codeClass}/add/mentor/${mentor}`)
      .then((res) => {
        if (res.status === 200) {
          triggerNotifier({
            message: "Mentor Added",
            type: "success",
            duration: 1000,
            icon: "👏",
          });
          dispatch({ type: SET_USER_ATTENDANCE_PRESENT_LOADING });
          return { success: true };
        } else {
          return { success: false };
        }
      })
      .catch((error) => {
        dispatch({ type: SET_USER_ATTENDANCE_PRESENT_LOADING });
        console.log("assign mentor  Error", error);
        triggerNotifier({
          message: error?.response?.data?.message,
          type: "error",
          duration: 2000,
          icon: "⚠️",
        });
      });
  };
//--------------------Delete Attendance -------------
export const deleteAttendanceFromClass =
  ({ codeClass, participant }) =>
  (dispatch) => {
    return axios
      .delete(
        `${baseURL}/dayscode/class/${codeClass}/delete/attendance/${participant}`,
      )
      .then(({ data }) => {
        debugger;
        if (data.success) {
          triggerNotifier({
            message: "Delete Attendance",
            type: "success",
            duration: 1000,
            icon: "👏",
          });
          dispatch(getCodeClasses());
          return { success: true };
        }
      })
      .catch((error) => {
        console.log(" Delete Attendance  error", error);
        triggerNotifier({
          message: error?.response?.data?.message,
          type: "error",
          duration: 2000,
          icon: "⚠️",
        });
      });
  };

//----------Participant Assign to  Mentor--------------------------------
export const addParticipantTomentor =
  ({ mentor, batchParticipant }) =>
  (dispatch) => {
    return axios
      .patch(`${baseURL}/dayscode/mentor/${mentor}/assign`, {
        data: { batchParticipant },
      })
      .then((res) => {
        if (res.status === 200) {
          triggerNotifier({
            message: "Assign Mentor to Participant",
            type: "success",
            duration: 1000,
            icon: "👏",
          });
          return { success: true };
        } else {
          return { success: false };
        }
      })
      .catch((error) => {
        console.log("assign mentor  Error", error);
        triggerNotifier({
          message: error?.response?.data?.message,
          type: "error",
          duration: 2000,
          icon: "⚠️",
        });
      });
  };
export const deleteParticipantTomentor =
  ({ mentor, batchParticipant }) =>
  (dispatch) => {
    return axios
      .patch(`${baseURL}/dayscode/mentor/${mentor}/deassign`, {
        data: { batchParticipant },
      })
      .then((res) => {
        if (res.status === 200) {
          triggerNotifier({
            message: " Delete Mentor to Participant",
            type: "success",
            duration: 1000,
            icon: "👏",
          });
          return { success: true };
        } else {
          return { success: false };
        }
      })
      .catch((error) => {
        console.log("Delete mentor  Error", error);
        triggerNotifier({
          message: error?.response?.data?.message,
          type: "error",
          duration: 2000,
          icon: "⚠️",
        });
      });
  };

export const getBatchContent =
  ({ batchId, day }) =>
  (dispatch) => {
    dispatch({ type: SET_BATCH_CONTENT_LOADING });
    return axios
      .get(`${baseURL}/dayscode/code_batch/${batchId}/content/${day}`)
      .then(({ data }) => {
        if (data.success === true) {
          triggerNotifier({
            message: "Batch Content Loaded",
            type: "success",
            duration: 1000,
            icon: "👏",
          });
          dispatch(actions.setBatchContent(data?.contentDetails));
          return { success: true, data: data?.contentDetails };
        }
      })
      .catch((error) => {
        dispatch({ type: SET_BATCH_CONTENT_LOADING });
        console.log("Batch Content error", error);
        triggerNotifier({
          message: error?.response?.res?.message,
          type: "error",
          duration: 2000,
          icon: "⚠️",
        });
      });
  };

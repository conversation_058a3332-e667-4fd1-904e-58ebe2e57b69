import React from "react";

import {
  DesktopWallpaper,
  DetailsPanel,
  FrameSection,
  HeroSection,
  MobileWallpaper,
  SocialStoryTheme,
} from "./SharedComponents";
// import desktopWallpaper1 from "../../assets/images/svg/wittyhacks/desktopWallpaper1.png";
import instaPostTheme from "../../assets/images/svg/wittyhacks/MobileSwags/6.png";
import mobileWallpaper2 from "../../assets/images/svg/wittyhacks/MobileSwags/7.png";
import mobileWallpaper3 from "../../assets/images/svg/wittyhacks/MobileSwags/8.png";
import mobileWallpaper4 from "../../assets/images/svg/wittyhacks/MobileSwags/9.png";
import desktopWallpaper1 from "../../assets/images/svg/wittyhacks/DesktopSwags/1.png";
import desktopWallpaper2 from "../../assets/images/svg/wittyhacks/DesktopSwags/2.png";
import desktopWallpaper3 from "../../assets/images/svg/wittyhacks/DesktopSwags/3.png";
import desktopWallpaper4 from "../../assets/images/svg/wittyhacks/DesktopSwags/4.png";
import desktopWallpaper5 from "../../assets/images/svg/wittyhacks/DesktopSwags/5.png";

const Wittyhacks = () => {
  const desktopWallpaper = [
    { imgUrlDesktop: desktopWallpaper1 },
    { imgUrlDesktop: desktopWallpaper2 },
    { imgUrlDesktop: desktopWallpaper3 },
    { imgUrlDesktop: desktopWallpaper4 },
    { imgUrlDesktop: desktopWallpaper5 },
  ];
  const mobileWallpaperUrl = [
    { mobileWallpaper: mobileWallpaper2 },
    { mobileWallpaper: mobileWallpaper3 },
    { mobileWallpaper: mobileWallpaper4 },
  ];

  return (
    <>
      <div className="wittyhacks">
        <HeroSection />
        <DetailsPanel />
        <FrameSection />

        {/*  Desktop Wallpaper */}
        <div className="wallpaper-layout ">
          <h1 className="padding-x my-4">Desktop Wallpaper</h1>
          <div className="d-flex flex-wrap padding-x desktop-wall-layout">
            {desktopWallpaper.map((item) => {
              return <DesktopWallpaper imgUrlDesktop={item.imgUrlDesktop} />;
            })}
          </div>
        </div>

        {/*  Zoom Background */}
        {/* <div className="wallpaper-layout ">
          <h1 className="padding-x my-4">Zoom Backgrounds</h1>
          <div className=" padding-x d-flex flex-wrap  desktop-wall-layout">
            {desktopWallpaper.map((item) => {
              return <ZoomBackground zoomBackground={item.zoomBackground} />;
            })}
          </div>
        </div> */}

        {/* Mobile Background */}
        <div className="wallpaper-layout ">
          <h1 className="padding-x my-4">Mobile Wallpaper</h1>
          <div className=" padding-x d-flex padding-x flex-wrap  desktop-wall-layout">
            {mobileWallpaperUrl.map((item) => {
              return <MobileWallpaper imgUrlMobile={item.mobileWallpaper} />;
            })}
          </div>
        </div>

        <div className="wallpaper-layout ">
          <h1 className="padding-x my-4">Instagram Dtory Theme</h1>
          <div className=" padding-x d-flex padding-x flex-wrap desktop-wall-layout">
            <SocialStoryTheme imgUrlSocial={instaPostTheme} />
          </div>
        </div>
      </div>
    </>
  );
};

export default Wittyhacks;

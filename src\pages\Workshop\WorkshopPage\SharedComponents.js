import React, { useState } from 'react';
import Carousel from "react-multi-carousel";
import {
  Accordion,
  AccordionBody,
  AccordionHeader,
  AccordionItem,
} from 'reactstrap';
import { IoIosArrowDropdownCircle } from "react-icons/io";

import CustomStarRating from '../../../components/sharedComponents/CustomStarRating'


// Hero Section
export const HeroSection = () => {
  return (
    <>
      <section className="row mx-0 gap-4 d-flex justify-content-center align-items-center padding-x padding-y hero-section">
        <div className="shapes">
          <div className="square1"></div>
          <div className="square2"></div>
          <div className="circle"></div>
          <div className="triangle"></div>
        </div>
        <div className="col-12 col-lg-6 col-md-8">
          <h1 className="text-center mx-auto">
            Learn <span className="gradient-text">Web Design</span> by <PERSON><PERSON><PERSON><PERSON>
          </h1>
          <p className="text-center mt-4">
            Learn the basics of web designing, learn tools like figma to design
            the screen of a web page, learn responsive designing and prototyping{" "}
          </p>
        </div>
      </section>
    </>
  );
};

export const WorkshopDetailsSection = () => {
  const workshopDetailsPoints = [
    {
      icon: "youtube.png",
      content: "Get 16 hours learning in 7 Days",
    },
    {
      icon: "youtube.png",
      content: "Daily guided exercises",
    },
    {
      icon: "youtube.png",
      content: "Access to 50k+ community",
    },
    {
      icon: "",
      content: "Regular expert feedback",
    },
  ];

  return (
    <>
      <section className="row mx-0 d-flex justify-content-between workshop-section padding-x">
        <div className="col-md-5 col-12 px-0">
          <div>
            {workshopDetailsPoints.map((item, key) => (
              <div key={key} className="detail-block d-flex my-3">
                {item.icon ? (
                  <img
                    height="20"
                    width="20"
                    loading="lazy"
                    src={require(`../../../assets/images/svg/${item.icon}`)}
                    alt="avatar"
                  />
                ) : (
                  <img
                    height="20"
                    width="20"
                    loading="lazy"
                    src="/"
                    alt="avatar"
                  />
                )}

                <span>{item.content}</span>
              </div>
            ))}
          </div>
          <div className="d-flex justify-content-center align-items-center text-white mx-auto by">
            by
          </div>
          <div className="speaker-block ">
            <div className="d-flex align-items-center">
              <img
                src={require(`../../../assets/images/svg/profile.jpg`)}
                alt=""
              />

              <div className="ml-3">
                {" "}
                <p className="my-0 gradient-text">Dhanshree Nirgude</p>{" "}
                <p className="my-0">Web Designer</p>
              </div>
            </div>
            <div className="d-flex align-items-center mt-3 participants-box">
              <img
                src={require(`../../../assets/images/svg/profile.jpg`)}
                alt=""
              />
              <img
                src={require(`../../../assets/images/svg/profile.jpg`)}
                alt=""
              />
              <img
                src={require(`../../../assets/images/svg/profile.jpg`)}
                alt=""
              />
              <p className="my-0">
                <span>993232</span> people have joined already
              </p>
            </div>
          </div>
        </div>
        <div className="col-md-6 col-12 px-0 text-center d-flex flex-column justify-content-lg-between mt-4 mt-md-0 right">
          <div className="workshop-img">
            <img
              height="auto"
              width="auto"
              src={`https://res.cloudinary.com/datacode/image/upload/v1650287597/h2y3w1ftk24ticvyawtu.png`}
              alt="avatar"
              loading="lazy"
              className="img-fluid mx-auto"
            />
          </div>
          <p className="mx-auto my-lg-2 my-md-4 my-3">
            get to Meet your web designer guru, Dhanshree Nirgude! Complete Web
            Design: from Figma to Webflow to Freelancing
          </p>
          <button className="btn-main">Login to Enroll Now</button>
        </div>
      </section>
    </>
  );
};

export const AboutSection = () => {
  return (
    <>
      <section className="text-center about-section padding-x padding-y">
        <h1 className="mx-auto heading gradient-text">About Workshop</h1>
        <p className="mx-auto mt-4">
          Full Stack Blockchain Development course is a live mentor-led
          certification program with by iNeuron. In this course you will learn
          the entire stack required to work in Permissionless Blockchain
          development. This course focuses on latest Blockchain industry
          standards like Ethereum Blockchain, Solidity, Decentralized Autonomous
          Organisations, Decentralized Finance, Non Fungible Tokens, Polygon
          Network, Polkadot Blockchain, Oracles along with complete development
          stack in Javascript and many more Blockchain concepts.
        </p>
      </section>
    </>
  );
};

export const LearnSection = () => {
  const LearnCardDetails = [
    {
      title: "Secrets of Good Design",
      description: "Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book."
    },
    {
      title: "Practice Design Like a Pro",
      description: "Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book."
    },
    {
      title: "Webflow Development",
      description: "Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book."
    }
  ]
  return (
    <>
      <section className="learn-section padding-x">
        <h1 className="mx-auto gradient-text heading">
          What will you learn?
        </h1>
        <div className="d-flex justify-content-around flex-wrap my-5 mt-md-5 learn-container">
          {LearnCardDetails.map((item, key) => (
            <div className="m-3 learn-card">
              <div className="left"></div>
              <div className="right"></div>
              <span>0{key + 1}</span>
              <div className="card-body">
                <h3>{item.title}</h3>
                <p className="mt-4">
                  {item.description}
                </p>
              </div>
            </div>
          ))
          }

        </div>
        <button className="mx-auto btn-main">Login to Enroll Now</button>
      </section>
    </>
  );
};

export const LearnPlanSection = () => {
  const dates = ['23 Dec 2024', '24 Dec 2024', '25 Dec 2024', '26 Dec 2024', '27 Dec 2024'];
  const details = [
    {
      id: 1,
      time: '10:00 - 10:15',
      title: 'Introductions',
      description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Donec malesuada tristique justo quis ultrices. Morbi gravida dignissim lectus'
    },
    {
      id: 2,
      time: '10:15 - 11:00',
      title: 'Design thinking for better frames',
      description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. '
    },
    {
      id: 3,
      time: '11:00 - 11:30',
      title: 'Short Break',
      description: ''
    },
    {
      id: 4,
      time: '11:30 - 12:00',
      title: 'Importance of user research in designing process',
      description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Donec malesuada tristique justo quis ultrices. Morbi gravida dignissim lectus'
    },
    {
      id: 5,
      time: '12:00 - 12:30',
      title: 'User data & Privacy',
      description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Donec malesuada tristique justo quis ultrices.'
    },
  ]
  return (
    <>
      <section className="learn-plan-section padding-x padding-y">
        <h1 className="gradient-text heading">Our Learning Plan</h1>
        <div className="my-4 text-center date-section">
          {
            dates.map((item) => (<span className="mx-2">{item}</span>)
            )
          }
        </div>
        <div className=" py-4 learn-plan-details-section padding-x">
          {
            details.map((item) => (
              item.description === '' ?
                (
                  <div className="mb-5 short-break">
                    <div className="break-line"></div>
                    <div className="mx-auto short-break-btn"> <img height='20' width='20' src={require(`../../../assets/images/svg/Cup.png`)} alt="" /> {item.title}</div>
                  </div>
                )
                :
                (
                  <div className="learn-plan-item" key={item.id} >
                    <div className="d-flex align-items-center">
                      <img height='20' width='20' src={require(`../../../assets/images/svg/Clock.png`)} alt="" />
                      <span className="ml-1">{item.time}</span>
                    </div>
                    <h5 className="my-2">{item.title}</h5>
                    <p>{item.description}</p>
                  </div>
                )

            )
            )
          }

        </div>
      </section>
    </>
  );
};

export const TakeWorkshopSection = () => {
  const roles = [
    {
      img: "student.png",
      title: "Students",
      detail: "With this course, you can start your career as a full-stack web developer. Ideal to start in 2nd or 3rd year of your Engineering course."
    },
    {
      img: "student.png",
      title: "Freshers",
      detail: "Fresh graduates with zero industry experience can take this course to become a full-stack web developer."
    },
    {
      img: "student.png",
      title: "Working Professionals",
      detail: "Early-career professionals (ideally from 1-3 years of experience) can take this course to become a full-stack web developer."
    },
  ]
  return (
    <>
      <section className="take-workshop-section padding-x">
        <h1 className="gradient-text heading">Who should take this Workshop?</h1>
        <div className="row mx-0 text-center justify-content-between padding-y">
          {
            roles && roles.map((item, key) => (
              <div key={key} className="col-md-4 col-lg-3 col-12 mb-3 mb-md-0">
                {
                  item.img ? <img src={require(`../../../assets/images/svg/${item.img}`)} width="100" height="100" alt="" />
                    :
                    <img src="/" width="100" height="100" alt="" />
                }

                <h4 className="my-3">{item.title}</h4>
                <p>{item.detail}</p>
              </div>
            ))
          }

        </div>
      </section>
    </>
  );
};

export const TestimonialSection = () => {

  const responsive = {
    desktop: {
      breakpoint: { max: 3000, min: 1024 },
      items: 3,
      slidesToSlide: 3 // optional, default to 1.
    },
    tablet: {
      breakpoint: { max: 1024, min: 680 },
      items: 2,
      slidesToSlide: 2 // optional, default to 1.
    },
    mobile: {
      breakpoint: { max: 680, min: 0 },
      items: 1,
      slidesToSlide: 1 // optional, default to 1.
    }
  };

  const clients = [
    {
      name: "Leo",
      role: "Lead Designer",
      rating: 4,
      img: "profile.png",
      title: "It was a very good experience",
      description: "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Cursus nibh mauris, nec turpis orci lectus maecenas. Suspendisse sed magna eget nibh in turpis. Consequat duis diam lacus arcu. Faucibus venenatis felis id augue sit cursus pellentesque enim arcu. Elementum felis magna pretium in tincidunt. Suspendisse sed magna eget nibh in turpis. Consequat duis diam lacus arcu."
    },
    {
      name: "Maria",
      role: "Lead Designer",
      rating: 2,
      img: "profile.png",
      title: "It was a very good experience",
      description: "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Cursus nibh mauris, nec turpis orci lectus maecenas. Suspendisse sed magna eget nibh in turpis. Consequat duis diam lacus arcu. Faucibus venenatis felis id augue sit cursus pellentesque enim arcu. Elementum felis magna pretium in tincidunt. Suspendisse sed magna eget nibh in turpis. Consequat duis diam lacus arcu."
    },
    {
      name: "Riya",
      role: "Lead Designer",
      rating: 3,
      img: "profile.png",
      title: "It was a very good experience",
      description: "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Cursus nibh mauris, nec turpis orci lectus maecenas. Suspendisse sed magna eget nibh in turpis. Consequat duis diam lacus arcu. Faucibus venenatis felis id augue sit cursus pellentesque enim arcu. Elementum felis magna pretium in tincidunt. Suspendisse sed magna eget nibh in turpis. Consequat duis diam lacus arcu."
    },
    {
      name: "Priyanshi",
      role: "Lead Designer",
      rating: 5,
      img: "profile.png",
      title: "It was a very good experience",
      description: "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Cursus nibh mauris, nec turpis orci lectus maecenas. Suspendisse sed magna eget nibh in turpis. Consequat duis diam lacus arcu. Faucibus venenatis felis id augue sit cursus pellentesque enim arcu. Elementum felis magna pretium in tincidunt. Suspendisse sed magna eget nibh in turpis. Consequat duis diam lacus arcu."
    },
  ]

  return (
    <>
      <section className="testimonial-section padding-x padding-y">
        <h4 className="text-uppercase text-center">Testimonials</h4>
        <h1 className="mx-auto text-center my-3 gradient-text heading">What our learners has to say about us</h1>
        <Carousel
          additionalTransfrom={0}
          arrows
          autoPlaySpeed={1000}
          centerMode={false}
          className=""
          containerClass="testimonial-container"
          dotListClass=""
          draggable
          focusOnSelect={false}
          infinite
          itemClass=""
          keyBoardControl
          minimumTouchDrag={80}
          pauseOnHover
          renderArrowsWhenDisabled={false}
          renderButtonGroupOutside={false}
          renderDotsOutside
          responsive={responsive}
          rewind={false}
          rewindWithAnimation={false}
          rtl={false}
          shouldResetAutoplay
          showDots
          sliderClass=""
          slidesToSlide={1}
          swipeable
        >
          {
            clients.map((item, key) => (
              <div key={key} className="text-center p-md-4 px-2 py-4 testimonial-card">
                <div className="text-md-left row mx-0 align-items-center justify-content-lg-start justify-content-md-between">
                  <div className="col-md-3 col-12 px-0 justify-content-center align-items-center mb-2 mb-md-0">
                    <img width="60" height="60" src={require(`../../../assets/images/svg/workshop-page-testimonial/${item.img}`)} alt="" />
                  </div>
                  <div className="col-md-9 col-12 px-0">
                    <h5>{item.name}</h5>
                    <div className="row mx-0 align-items-center">
                      <div className="col-md-7 col-12 px-0"> <p className="mb-0">{item.role}</p></div>
                      <div className="col-md-5 col-12 px-0 text-center text-md-right">
                        <CustomStarRating initialValue={item.rating} readonly={true} />
                      </div>
                    </div>
                  </div>
                </div>
                <h5 className="my-3">{item.title}</h5>
                <p>{item.description}</p>
              </div>
            ))
          }
        </Carousel>
      </section>
    </>
  );
};

export const FaqSection = () => {
  const [open, setOpen] = useState('1');
  const toggle = (id) => {
    if (open === id) {
      setOpen();
    } else {
      setOpen(id);
    }
  };

  const faq = [
    {
      ques: "A digital agency is a business",
      ans: "Digital marketing efforts, instead of handling in-house. They can provide your business with a variety of digital solutions to promote your product or service online and help you hit your marketing goals and grow your business."
    },
    {
      ques: "Hire to outsource your digital",
      ans: "Digital marketing efforts, instead of handling in-house. They can provide your business with a variety of digital solutions to promote your product or service online and help you hit your."
    },
    {
      ques: "Marketing efforts",
      ans: "Digital marketing efforts, instead of handling in-house. They can provide your business with a variety of digital solutions to promote your product or service online and help you hit your marketing goals and grow your business."
    },
    {
      ques: "Can provide your business",
      ans: "Digital marketing efforts, instead of handling in-house. They can provide your business with a variety of digital solutions to promote your product or service online and help you hit your marketing goals and grow your business."
    },
  ]
  return (
    <>
      <section className="faq-section padding-x padding-y">
        <div className="row mx-0 justify-content-between">
          <div className="col-md-4 col-12 px-0 mb-3 mb-md-0">
            <h1 className="mb-3 gradient-text heading">Frequently Asked Questions</h1>
            <h5>A digital agency is a business you hire to outsource your digital marketing efforts, instead of handling in-house.</h5>
          </div>
          <div className="col-md-6 col-12 px-0">
            <Accordion open={open} toggle={toggle}>
              {
                faq.map((item, key) => (
                  <AccordionItem>
                    <AccordionHeader targetId={(key +1).toString()}>
                      {item.ques}
                      <span><IoIosArrowDropdownCircle /></span> </AccordionHeader>
                    <AccordionBody accordionId={(key+1).toString()}>
                      {item.ans}
                    </AccordionBody>
                  </AccordionItem>

                ))
              }

            </Accordion>
          </div>
        </div>
      </section>
    </>
  );
};

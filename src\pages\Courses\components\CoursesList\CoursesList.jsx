import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Link } from "react-router-dom";

import { Pagination } from "@akshayDatacode/datacode-ui";
import { getCourses, deleteCourse } from "../../actions";
import CustomLoader from "../../../../components/sharedComponents/CustomLoader";
import CourseLayoutContainer from "../CourseLayoutContainer";
import CourseListCard from "./CourseListCard";
import DeleteModal from "../../../../components/sharedComponents/DeleteModal";
import ToolTip from "../../../../components/sharedComponents/ToolTip";

const CoursesList = () => {
  const dispatch = useDispatch();
  
  const coursesList = useSelector((state) => state.course.coursesList);
  const coursesCount = useSelector((state) => state.course.coursesCount);
  const coursesListLoading = useSelector(
    (state) => state.course.coursesListLoading
  );

  const [selected, setSelected] = useState(0);
  const [openModal, setOpenModal] = useState(false);
  const [tempDeleteId, setTempDeleteId] = useState();

  useEffect(() => {
    dispatch(getCourses(selected, coursesPerPage));
    window.scrollTo(0, 0);
  }, [dispatch]);

  // Pagination
  const coursesPerPage = 2;
  const pageCount = Math.ceil(coursesCount / coursesPerPage);
  const changePage = ({ selected }) => {
    setSelected(selected);
    dispatch(getCourses(selected, coursesPerPage));
  };

  const toggleModal = () => {
    setOpenModal(!openModal);
  };

  const handleCourseDeleteModal = () => {
    dispatch(deleteCourse(tempDeleteId)).then((res) => {
      if (res) {
        dispatch(getCourses(selected, coursesPerPage));
        setOpenModal(!openModal);
      }
    });
  };

  const handleDelete = (id) => {
    setTempDeleteId(id);
    setOpenModal(!openModal);
  };

  return (
    <>
      <CourseLayoutContainer>
        <div className="course-list">
          <h3>Courses List </h3>
          <div className="row mx-0">
            <div className="col-12 course-list-header">
              <h4>My Courses {coursesCount}</h4>
              <Link to="/course/new">
                <ToolTip title="Create course" placement="bottom">
                  <button>Create Course</button>
                </ToolTip>
              </Link>
            </div>
          </div>
          {coursesListLoading ? (
            <div className="d-flex justify-content-center">
              <CustomLoader />
            </div>
          ) : (
            <div className="row mx-0">
              <div className="col-12">
                {coursesList && coursesList.length ? (
                  coursesList.map((course) => (
                    <CourseListCard
                      key={course._id}
                      course={course}
                      handleDelete={handleDelete}
                    />
                  ))
                ) : (
                  <p>No Course available in list</p>
                )}
              </div>
            </div>
          )}
          {coursesCount > 0 && (
            <div className="d-flex justify-content-center">
              <Pagination pageCount={pageCount} changePage={changePage} />
            </div>
          )}
        </div>
      </CourseLayoutContainer>
      <DeleteModal
        open={openModal}
        toggle={toggleModal}
        onSubmit={handleCourseDeleteModal}
        submitButtonName={"Delete Problem"}
        message={" Are you sure want to delete this course"}
        title={"Delete Course"}
      />
    </>
  );
};
export default CoursesList;

import React, { useState, useEffect } from "react";
import { useDispatch } from "react-redux";
import { Combobox } from "react-widgets";

import {
  editParticipantIntoBatch,
  getBatchParticipants,
} from "../../pages/DaysCode/actions";
import { problemTypeOptions } from "../../pages/DaysCode/components/Settings/Problems/AddProblem/helper";
import {
  batchContentRoleOptions,
  batchContentStatusOptions,
  batchParticipantStatusOptions,
} from "../../pages/DaysCode/components/Settings/helpers";
import { getOptions } from "../utils";
import CustomFormModal from "./CustomFormModal";

const AddContentToBatchModal = ({
  setshowModal,
  showModal,
  handleAddIntoBatch,
  codeBatchDetails,
  batch,
  item,
  type,
  batchContent,
  isEdit,
  filterLimit,
  filterStatus,
  tempDay
}) => {

  const dispatch = useDispatch();
  
  const [status, setBatchStatus] = useState();
  const [participantStatus, setParticipantStatus] = useState(
    batchContent?.status,
  );
  const [problemType, setproblemType] = useState();
  const [day, setDay] = useState("");
  const [role, setRole] = useState();

  useEffect(() => {
    if (batchContent) {
      setBatchStatus(batchContent.status);
      setproblemType(batchContent.problem_type);
      setDay(batchContent.day);
      setRole(batchContent.role);
    }
  }, [batchContent]);

  useEffect(()=>{
    if(tempDay){
      setDay(tempDay)
    }
  },[tempDay])

  const onclick = () => {
    console.log(batchContent)
    if (
      type === "mentor" ||
      type === "problem" ||
      type === "quiz" ||
      type === "lesson"
    ) {
      handleAddIntoBatch({
        batch,
        problem_type: problemType?.value,
        day,
        id:
          type === "mentor" && isEdit === false
            ? batchContent
            : (isEdit ? batchContent : item)._id,
        status: status?.value,
        role: role ? role.value : 0,
      });
    } else if (type === "participant") {
      dispatch(
        editParticipantIntoBatch({
          id: batchContent._id,
          status: participantStatus,
          batch: batch,
        }),
      ).then(() => {
        dispatch(
          getBatchParticipants({
            batch: batch,
            status: filterStatus,
            limit: filterLimit,
          }),
        );
      });
    }
    setshowModal(false);
  };

  return (
    <CustomFormModal
      isOpen={showModal}
      toggleModal={() => setshowModal(false)}
      title={`Edit ${type}`}
      titleIcon={""}
      onSubmitTitle={isEdit ? "SAVE" : "ADD"}
      onSubmit={onclick}
    >
      {type === "mentor" ? (
        <div>
          <label className='mr-4 form-label'>Role:</label>
          <div className=''>
            <Combobox
              data={getOptions(batchContentRoleOptions, "role")}
              dataKey={"value"}
              textField='role'
              placeholder={"role"}
              value={role}
              onChange={(value) => setRole(value)}
            />
          </div>
        </div>
      ) : type === "participant" ? (
        <div>
          <label className='mr-4 form-label'>Status:</label>
          <div className=''>
            <Combobox
              data={getOptions(batchParticipantStatusOptions, "status")}
              dataKey={"value"}
              textField='status'
              placeholder={"status"}
              value={participantStatus}
              onChange={(value) => setParticipantStatus(value)}
            />
          </div>
        </div>
      ) : (
        <>
          <div>
            <label className='mr-4 form-label d-block'>Batch Days:</label>
            <input
              type='number'
              className='form-control'
              value={day}
              onChange={(e) => {
                setDay(e.target.value);
              }}
            />
          </div>
          <div className='my-3'>
            <label className='mr-4form-label'>Status:</label>
            <div className=''>
              <Combobox
                data={getOptions(batchContentStatusOptions, "status")}
                dataKey={"value"}
                textField='status'
                placeholder={"Batch Status"}
                value={status}
                onChange={(value) => setBatchStatus(value)}
              />
            </div>
          </div>
          {type === "lesson" || type === "quiz" ? (
            ""
          ) : (
            <div>
              <label className='mr-4 form-label'>Problem Type:</label>
              <div className=''>
                <Combobox
                  data={getOptions(problemTypeOptions, "problem_type")}
                  dataKey={"value"}
                  textField='problem_type'
                  placeholder={"Problem Type"}
                  value={problemType}
                  onChange={(value) => setproblemType(value)}
                />
              </div>
            </div>
          )}
        </>
      )}
    </CustomFormModal>
  );
};

export default AddContentToBatchModal;

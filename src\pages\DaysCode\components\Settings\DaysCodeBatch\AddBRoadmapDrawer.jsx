import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { <PERSON><PERSON>, <PERSON><PERSON>, Drawer, Space } from "antd";
import moment from "moment";
import { Field, getFormValues, reduxForm } from "redux-form";
import { Combobox } from "react-widgets";

import { required } from "../../../../../components/utils/validators";
import { renderInputField } from "../../../../../components/sharedComponents/ReduxFormFields";
import { getTopicOptions } from "../../utils";
import { DSAContent } from "../../Constants/helper";
import {
  createBatchRoadMap,
  editBatchRoadMap,
  editLessonIntoBatch,
  editProblemIntoBatch,
  editQuizIntoBatch,
  getBatchContent,
  getCodeBatchDetails,
} from "../../../actions/operations";
import { removeContentError } from "../../../actions";
import ProblemListCard from "../Problems/ProblemsList/ProblemListCard";
import NoContentCard from "./NoContentCard";
import CustomLoader from "../../../../../components/sharedComponents/CustomLoader";

const AddBRoadmapDrawer = ({
  batch,
  handleSubmit,
  submitting,
  initialize,
  updateMode,
  mode,
  showDrawer,
  onClose,
  open,
  tempRoadmap,
  tempDay,
  error,
  selectedComponent,
  setSelectedComponent,
  setOpenContent,
  setTempDay
}) => {
  /* const [open, setOpen] = useState(false); */
  const dispatch = useDispatch();

  const formStates =
    useSelector((state) => getFormValues("create-roadmap")(state)) || {};
    const {
    codeBatchDetails,
    contentError,
    batchContentDetails,
    batchContentLoading,
  } = useSelector((state) => state.dayscode) || {};

  const [placement, setPlacement] = useState("right");
  const [topic, setTopic] = useState();

  const ContentTypes = ["lesson", "problem"];

  

  useEffect(() => {
    if (tempRoadmap) {
      /* initialize(tempRoadmap) */
      setTopic(tempRoadmap.topic);
      initialize({
        ...tempRoadmap,
        date: calculateDayDate(tempRoadmap.day, codeBatchDetails.start_date),
      });
      dispatch(getBatchContent({ batchId: batch, day: tempRoadmap.day }));
    }
  }, [tempRoadmap, dispatch]);

  useEffect(() => {
    if (tempDay) {
      initialize({
        day: tempDay,
        date: calculateDayDate(tempDay, codeBatchDetails.start_date),
        title: "",
        topic: "",
      });
      dispatch(getBatchContent({ batchId: batch, day: tempDay }));
    }
  }, [tempDay, dispatch]);

  useEffect(() => {
    if (mode === "add" && !tempDay) {
      initialize(null);
    }
  }, [mode]);

  useEffect(() => {
    return () => {
      dispatch(removeContentError());
    };
  }, [dispatch]);

  const onSubmit = (values) => {
    if (mode === "add") {
      dispatch(
        createBatchRoadMap({
          topic: topic.name,
          batch: batch,
          day: values.day,
          title: values.title,
        }),
      );
    } else {
      dispatch(
        editBatchRoadMap({
          _id: tempRoadmap._id,
          topic: topic.name,
          batch: batch,
          day: values.day,
          title: values.title,
        }),
      );
    }
    // dispatch(reset("create-roadmap"));
  };

  const calculateDayDate = (day, startDate) => {
    if (!startDate) {
      console.error("Invalid start date");
      return null;
    }
    // Calculate the end date
    const date = new Date(startDate);
    date.setDate(date.getDate() + Number(day));
    const formatted = moment(date).format("YYYY-MM-DD");
    // Return the end date in a readable format\
    initialize({ ...formStates, date: formatted });
  };

  const onDrawerClose = () => {
    onClose();
    initialize(null);
  };

  const handleEditQuizIntoBatch = (contentData) => {
    dispatch(editQuizIntoBatch(contentData)).then(()=>{
      dispatch(getCodeBatchDetails(codeBatchDetails._id))
    });
  };
  const handleEditLessonIntoBatch = (contentData) => {
    dispatch(editLessonIntoBatch(contentData)).then(()=>{
      dispatch(getCodeBatchDetails(codeBatchDetails._id))
    });
  };
  const handleEditProblemIntoBatch = (contentData) => {
    dispatch(editProblemIntoBatch(contentData)).then(()=>{
      dispatch(getCodeBatchDetails(codeBatchDetails._id))
    });
  };

  const ReturnEditContentFunction = (type) => {
    const EditContent = {
      lesson : handleEditLessonIntoBatch,
      quiz: handleEditQuizIntoBatch,
      problem: handleEditProblemIntoBatch,
    }
    return EditContent[type] 
  }
  return (
    <>
      <Space>
        <Button
          type='primary'
          onClick={() => {
            showDrawer();
            updateMode("add");
          }}
        >
          Create Roadmap
        </Button>
      </Space>

      <Drawer
        title={
          mode === "add" ? "Create Roadmap for batch" : "Edit Roadmap for batch"
        }
        placement={placement}
        closable={false}
        setPlacement={setPlacement}
        onClose={onDrawerClose}
        open={open}
        key={placement}
        rootClassName='custom-drawer'
      >
        {contentError && (
          <Alert
            message='Error'
            description={contentError}
            type='error'
            showIcon
          />
        )}
        <form onSubmit={handleSubmit(onSubmit)}>
          <div className='row '>
            <div className='col-5 mx-3'>
              <Field
                type='text'
                name='title'
                label='Roadmap Title'
                placeholder=''
                component={renderInputField}
                validate={[required]}
              />
            </div>
            <div className='col-5 mx-3'>
              <Field
                type='number'
                name='day'
                label='Day'
                placeholder='Select Roadmap Day'
                component={renderInputField}
                validate={[required]}
                min={0}
                onChange={(e) => {      
                  setTempDay(e.target.value)           
                  dispatch(
                    getBatchContent({ batchId: batch, day: e.target.value }),
                  );
                }}
                onBlur={()=>{
                  calculateDayDate(
                    formStates.day,
                    codeBatchDetails?.start_date,
                  );
                }
                }
              />
            </div>
            <div className='col-5 mt-3 mb-4 mx-3'>
              <label htmlFor='topic'>Select Topic</label>
              <Combobox
                data={getTopicOptions(DSAContent)}
                dataKey={"value"}
                textField='name'
                label='Select TOpic'
                placeholder={"Select Topic Name"}
                value={topic}
                validate={[required]}
                onChange={(value) => setTopic(value)}
              />
            </div>
            <div className='col-5 mb-4 mx-3'>
              <Field
                type='date'
                name='date'
                label='Roadmap Date'
                placeholder=''
                component={renderInputField}
                validate={[required]}
                isDisabled={true}
              />
            </div>
            <hr />
            <div className='col-11 d-flex justify-content-center my-3 title-heading'>
              Batch Content
            </div>
            {formStates.day ? (
              <>
                {!batchContentLoading ? (
                  <>
                    <div className='row mx-4 mt-3 '>
                      {batchContentDetails &&
                        ContentTypes.map((type) => {
                          const content = batchContentDetails[type];
                          const details = content
                            ? content[`${type}Details`]
                            : null;

                          return content ? (
                            <div key={type} className='col-md-11 col-12 p-2'>
                              <div className='sub-title-heading'>{type}</div>
                              <ProblemListCard
                                batch={batch}
                                day={content.day}
                                batchContent={content}
                                item={details}
                                type={type}
                                handleAddIntoBatch={ReturnEditContentFunction(type)}
                              />
                            </div>
                          ) : (
                            <div key={type} className='col-md-11 col-12 p-2'>
                              <div className='sub-title-heading'>{type}</div>
                              <NoContentCard
                                type={type}
                                setSelectedComponent={setSelectedComponent}
                                setOpenContent={setOpenContent}
                              />
                            </div>
                          );
                        })}
                      <div className='sub-title-heading'>Quizes</div>
                      {batchContentDetails.challenges?.length ? (
                        batchContentDetails.challenges.map((challenge) => {


                          return (
                            <div
                              className='col-md-11 col-12 p-2'
                              key={challenge._id}
                            >
                              <ProblemListCard
                                batch={batch}
                                day={challenge?.day}
                                batchContent={challenge}
                                item={challenge?.quizDetails}
                                type='quiz'
                                handleAddIntoBatch={ReturnEditContentFunction('quiz')}
                              />
                            </div>
                          );
                        })
                      ) : (
                        <div className='col-md-11 col-12 p-2'>
                          <NoContentCard
                            type='quiz'
                            setSelectedComponent={setSelectedComponent}
                            setOpenContent={setOpenContent}
                          />
                        </div>
                      )}
                    </div>
                  </>
                ) : (
                  <div className='col-11 d-flex justify-content-center'>
                    <CustomLoader />
                  </div>
                )}
              </>
            ) : (
              <div className='col-11 d-flex justify-content-center'>
                Select Day to display content
              </div>
            )}
            <div className='col-11 d-flex justify-content-center'>
              <button
                type='submit'
                className='btn custom-button'
                // disabled={submitting}
              >
                {mode === "add" ? <p>Add</p> : <p>Save</p>}
              </button>
            </div>
          </div>
        </form>
      </Drawer>
    </>
  );
};

export default reduxForm({
  form: "create-roadmap",
  fields: ["day"],
})(AddBRoadmapDrawer);

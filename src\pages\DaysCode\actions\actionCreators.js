import {
  SET_PROBLEMS_LIST,
  SET_PROBLEM_DETAILS,
  SET_PROBLEM_DETAILS_BLANK,
  SET_USER_SUBMISSIONS_LIST,
  SET_SUBMISSIONS_LEADERBOARD,
  SET_SETTING_ACTIVE_TAB,
  SET_ALL_SUBMISSIONS,
  SET_DAYS_USERS_LIST,
  SET_DAYS_USER_DETAILS,
  SET_PROGRESS_COUNT,
  SET_LESSONS_LIST,
  SET_LESSON_DETAILS,
  SET_QUIZ_CHALLENGES_LIST,
  SET_QUIZ_CHALLENGE_DETAILS,
  SET_CONTENT_DETAILS,
  SET_CONTENTS_LIST,
  SET_TEST_CASES_RESPONCES_SUCCESS,
  SET_TEST_CASES_RESPONCES,
  SET_TEST_CASES_RESPONCES_FAILURE,
  SET_SOLUTION_SUBMISSION_LOADING,
  GET_CONTENT_COMPLETED_STATUS,
  SET_ALL_USERS_FEEDBACKS,
  SET_PRACTICE_PROBLEMS_LIST,
  SET_SPEAKERS_LIST,
  SET_ROADMAP_LIST,
  SET_MENTOR_LIST,
  SET_BATCH_PROBLEMS_LIST,
  SET_BATCH_LESSONS_LIST,
  SET_BATCH_QUIZ_CHALLENGES_LIST,
  SET_BATCH_MENTOR_LIST,
  SET_BATCH_PARTICIPANTS_LIST,
  SET_BATCH_LEADERBOARD,
  SET_CODE_CLASS_DETAILS,
  SET_CODE_CLASSES,
  SET_CODE_CLASS_PARTICIPANTS,
  SET_CONTENT_ERROR,
  REMOVE_CONTENT_ERROR,
  RESET_CODE_BATCH,
  RESET_CODE_BATCH_SUBMISSIONS,
  SET_BATCH_CONTENT,
} from "../constants";

export const setMentorList = (data) => ({
  type: SET_MENTOR_LIST,
  payload: data,
});

export const setBatchMentorList = (data) => ({
  type: SET_BATCH_MENTOR_LIST,
  payload: data,
});

export const setProblemsList = (data) => ({
  type: SET_PROBLEMS_LIST,
  payload: data,
});

export const setBatchProblemsList = (data) => ({
  type: SET_BATCH_PROBLEMS_LIST,
  payload: data,
});

export const setPracticeProblemsList = (data) => ({
  type: SET_PRACTICE_PROBLEMS_LIST,
  payload: data,
});

export const setProblemDetails = (data) => ({
  type: SET_PROBLEM_DETAILS,
  payload: data,
});

export const setProblemDetailsBlank = (data) => ({
  type: SET_PROBLEM_DETAILS_BLANK,
  payload: data,
});

export const setUserSubmissionsList = (data) => ({
  type: SET_USER_SUBMISSIONS_LIST,
  payload: data,
});

export const setSubmissionsLeaderboard = (data) => ({
  type: SET_SUBMISSIONS_LEADERBOARD,
  payload: data,
});

export const setSettingActiveTab = (data) => ({
  type: SET_SETTING_ACTIVE_TAB,
  payload: data,
});

export const setAllSubmissions = (data) => ({
  type: SET_ALL_SUBMISSIONS,
  payload: data,
});

export const setDaysUsersList = (data) => ({
  type: SET_DAYS_USERS_LIST,
  payload: data,
});

export const setDaysUserDetails = (data) => ({
  type: SET_DAYS_USER_DETAILS,
  payload: data,
});

export const setProgressCount = (data) => ({
  type: SET_PROGRESS_COUNT,
  payload: data,
});

export const setLessonsList = (data) => ({
  type: SET_LESSONS_LIST,
  payload: data,
});

export const setBatchLessonsList = (data) => ({
  type: SET_BATCH_LESSONS_LIST,
  payload: data,
});

export const setLessonDetails = (data) => ({
  type: SET_LESSON_DETAILS,
  payload: data,
});

export const setQuizChallengesList = (data) => ({
  type: SET_QUIZ_CHALLENGES_LIST,
  payload: data,
});

export const setBatchQuizChallengesList = (data) => ({
  type: SET_BATCH_QUIZ_CHALLENGES_LIST,
  payload: data,
});

export const setQuizChallengeDetails = (data) => ({
  type: SET_QUIZ_CHALLENGE_DETAILS,
  payload: data,
});

export const setContentDetails = (data) => ({
  type: SET_CONTENT_DETAILS,
  payload: data,
});

export const setContentsList = (data) => ({
  type: SET_CONTENTS_LIST,
  payload: data,
});

export const setTestCasesResponcesSuccess = (data) => ({
  type: SET_TEST_CASES_RESPONCES_SUCCESS,
  payload: data,
});

export const setTestCasesResponces = () => ({
  type: SET_TEST_CASES_RESPONCES,
});

export const setTestCasesResponcesFailure = (data) => ({
  type: SET_TEST_CASES_RESPONCES_FAILURE,
  payload: data,
});

export const setSolutionSubmissionLoading = (data) => ({
  type: SET_SOLUTION_SUBMISSION_LOADING,
});

export const getContentCompletedStatus = (data) => ({
  type: GET_CONTENT_COMPLETED_STATUS,
  payload: data,
});

export const setAllUsersFeedbacks = (data) => ({
  type: SET_ALL_USERS_FEEDBACKS,
  payload: data,
});

export const setSpeakersList = (data) => ({
  type: SET_SPEAKERS_LIST,
  payload: data,
});

export const setRoadmapList = (data) => ({
  type: SET_ROADMAP_LIST,
  payload: data,
});

export const setBatchParticipantsList = (data) => ({
  type: SET_BATCH_PARTICIPANTS_LIST,
  payload: data,
});

export const setBatchLeaderBoard = (data) => ({
  type: SET_BATCH_LEADERBOARD,
  payload: data,
});
export const setCodeClassDetails = (data) => ({
  type: SET_CODE_CLASS_DETAILS,
  payload: data,
});
export const setCodeClassess = (data) => ({
  type: SET_CODE_CLASSES,
  payload: data,
});
export const setCodeClassParticipants = (data) => ({
  type: SET_CODE_CLASS_PARTICIPANTS,
  payload: data,
});

export const setContentError = (data) => ({
  type: SET_CONTENT_ERROR,
  payload: data,
});

export const resetCodeBatches = (data) => ({
  type: RESET_CODE_BATCH,
  payload: data,
});

export const resetCodeBatchSubmission = (data) => ({
  type: RESET_CODE_BATCH_SUBMISSIONS,
  payload: data,
});

export const removeContentError = () => ({
  type: REMOVE_CONTENT_ERROR,
});

export const setBatchContent = (data) => ({
  type: SET_BATCH_CONTENT,
  payload: data,
});

import React from 'react'
import { useSelector } from 'react-redux'

import AddProblem from '../AddProblem'
import SubmissionsList from '../SubmissionsList'
import LayoutContainer from '../LayoutContainer';
import ProblemsList from '../ProblemsList';
import AddUserAttendance from '../UserAttendance/AddUserAttendance';
import UserAttendanceList from '../UserAttendance/UserAttendanceList';

const CodeAdminSettings = () => {
  const activeTab = useSelector(({ dayscode }) => dayscode.activeTab)

  const renderActiveComponent = (activeTab) => {
    const componentDictionary = {
      addProblems: <AddProblem />,
      submissionsList: <SubmissionsList />,
      problemsList: <ProblemsList />,
      sessionAttendance: <AddUserAttendance />,
      sessionAttendanceList: <UserAttendanceList />
    };

    return componentDictionary[activeTab];
  };

  return (
    <>
      <LayoutContainer>
        {renderActiveComponent(activeTab)}
      </LayoutContainer>
    </>
  )
}

export default CodeAdminSettings
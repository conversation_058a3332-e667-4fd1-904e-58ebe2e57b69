import React from "react";
import {
  UncontrolledDropdown,
  DropdownToggle,
  DropdownMenu,
  DropdownItem,
} from "reactstrap";
import { FaEllipsisH } from "react-icons/fa";
import { FaArrowCircleUp } from "react-icons/fa";
import { FaArrowCircleDown } from "react-icons/fa";
import { RiDeleteBin6Line } from "react-icons/ri";
import { RiEditLine } from "react-icons/ri";

import ToolTip from "../../../../../../components/sharedComponents/ToolTip";

const EventRegFormPreviewLayout = ({
  eventFormFields,
  formField,
  label,
  children,
  handelFormFieldOrder,
  handelDeleteFormField,
  handleFieldEdit,
  showDrawer
}) => {
  const formFieldsCount = eventFormFields.length;
  const index = eventFormFields.indexOf(formField);

  return (
    <>
      <div className="">
        <div className="input-field">
          <label className="label">{label}</label>
          <UncontrolledDropdown setActiveFromChild>
            <DropdownToggle tag="a">
              <ToolTip title="Edit" placement="bottom">
                <FaEllipsisH className="dot-icon" />
              </ToolTip>
            </DropdownToggle>
            <DropdownMenu>
              {formFieldsCount > 1 &&
                (index > 0 && index < formFieldsCount - 1 ? (
                  <>
                    <DropdownItem
                      className="move-btn"
                      onClick={() => handelFormFieldOrder(index, 1)}
                    >
                      <FaArrowCircleUp className="icon" />
                      Move up
                    </DropdownItem>
                    <DropdownItem
                      className="move-btn"
                      onClick={() => handelFormFieldOrder(index, -1)}
                    >
                      <FaArrowCircleDown className="icon" />
                      Move down
                    </DropdownItem>
                  </>
                ) : index === 0 ? (
                  <DropdownItem
                    className="move-btn"
                    onClick={() => handelFormFieldOrder(index, -1)}
                  >
                    <FaArrowCircleDown className="icon" />
                    Move down
                  </DropdownItem>
                ) : (
                  <DropdownItem
                    className="move-btn"
                    onClick={() => handelFormFieldOrder(index, 1)}
                  >
                    <FaArrowCircleUp className="icon" />
                    Move up
                  </DropdownItem>
                ))}
              <DropdownItem
                className="dlt-btn"
                onClick={() => handelDeleteFormField(formField)}
              >
                <RiDeleteBin6Line className="icon" />
                Delete
              </DropdownItem>
              <DropdownItem
                className="edit-btn"
                onClick={() => handleFieldEdit(formField)}
              >
                <RiEditLine className="icon" />
                Edit
              </DropdownItem>
            </DropdownMenu>
          </UncontrolledDropdown>
        </div>
        <div className="">{children}</div>
      </div>
    </>
  );
};

export default EventRegFormPreviewLayout;

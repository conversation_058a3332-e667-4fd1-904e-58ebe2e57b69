import React, { useState } from "react";

import EventImage from "./EventImage/EventImage";
import EventBasicDetails from "./EventBasicDetails";
import RegisterList from "../RegisterList";
import EventSpeakers from "./EventSpeakers";
import EventSponsors from "./EventSponsors";
import EventQuestions from "../EventQuestions";
import EventFeedbacks from "../EventFeedbacks";
import EventLayoutDetails from "./EventLayoutDetails";
import LayoutContainer from "./LayoutContainer";
import EventTimeline from "./EventTimeline";
import EventFormsLayout from "./EventFormsLayout";
import EventGeneral from "./EventGeneral";
import EventTicket from "./EventTicket";
import EventResource from "./EventResource/EventResource";
import EventCMS from "./EventCMS";

const EventOverview = () => {
  
  const [activeTab, setActiveTab] = useState("general");

  const renderActiveComponent = (activeTab) => {
    const componentDictionary = {
      general: (
        <EventGeneral
          setActiveTab={setActiveTab}
          // data={profileObject(userProfile, "profile")}
          // setProfileDetails={setProfileDetails}
        />
      ),
      settings: (
        <EventBasicDetails
          setActiveTab={setActiveTab}
          // data={profileObject(userProfile, "profile")}
          // setProfileDetails={setProfileDetails}
        />
      ),
      speaker: <EventSpeakers setActiveTab={setActiveTab} />,
      ticket: <EventTicket setActiveTab={setActiveTab} />,
      images: <EventImage setActiveTab={setActiveTab} />,
      resource: <EventResource setActiveTab={setActiveTab} />,
      sponsor: <EventSponsors setActiveTab={setActiveTab} />,
      question: <EventQuestions setActiveTab={setActiveTab} />,
      registerList: <RegisterList setActiveTab={setActiveTab} />,
      feedback: <EventFeedbacks setActiveTab={setActiveTab} />,
      layout: <EventLayoutDetails setActiveTab={setActiveTab} />,
      formLayout: <EventFormsLayout setActiveTab={setActiveTab} />,
      content: <EventCMS setActiveTab={setActiveTab} />,
      timeline: <EventTimeline />,
    };

    return componentDictionary[activeTab];
  };

  return (
    <>
      <div className='row mx-0'>
        <div className='col-12 px-0'>
          <LayoutContainer setActiveTab={setActiveTab} activeTab={activeTab}>
            {renderActiveComponent(activeTab)}
          </LayoutContainer>
        </div>
      </div>
    </>
  );
};

export default EventOverview;

import React, { useEffect, useState } from 'react';
import { Field, getFormValues, reduxForm } from 'redux-form';
import PropTypes from 'prop-types';
import { useNavigate, useParams } from 'react-router';

import { connect, useDispatch, useSelector } from 'react-redux';
import { renderInputField, renderSelectField } from '../../../../../components/sharedComponents/ReduxFormFields';
import { required } from '../../../../../components/utils/validators';
import { addReview, editReview, getUserProfile } from '../../../actions/operations';
import { getOptions } from '../../../../../components/utils';
import { userCategoryOptions } from './helpers';
import CustomStarRating from '../../../../../components/sharedComponents/CustomStarRating';
import { clearEditReview } from '../../../actions/actionCreators';
import CustomLoader from '../../../../../components/sharedComponents/CustomLoader';


const CreateReviewForm = ({ handleSubmit, submitting, reset, initialize, id, isEdit, setIsEdit }) => {

  const dispatch = useDispatch();
  const navigate = useNavigate();

  const formStates =
  useSelector((state) => getFormValues("create-review")(state)) || {};
  const { userProfile, currentUser, reviewToBeEdited, userProfileLoading } = useSelector((state) => state.auth)
  
  const [tempReview, setTempReview] = useState()
  const [rating, setRating] = useState(0)
  const isEmptyObject = (obj) => {
    return Object.keys(obj).length === 0 && obj.constructor === Object;
  }

  useEffect(() => {
    if (isEmptyObject(reviewToBeEdited)) {
      initialize({ ...tempReview, username: userProfile?.firstName + " " + userProfile?.lastName, authorname: currentUser?.userName })
    }
    else {
      initialize({ ...reviewToBeEdited, username: userProfile?.firstName + " " + userProfile?.lastName, authorname: currentUser?.userName })
      setRating(reviewToBeEdited.rating)
    }
  }, [userProfile, reviewToBeEdited])

  useEffect(() => {
    return () => {
      dispatch(clearEditReview());
    }

  }, [])

  useEffect(() => {
    console.log(reviewToBeEdited, "re")

  }, [reviewToBeEdited])

  const onSubmit = (values) => {
    const data = {
      ...values,
      user: id,
      author: currentUser?._id,
      rating: rating,
    }

    if (!isEdit) {
      dispatch(addReview(data))
    }
    else {
      dispatch(editReview(data))
    }
  }

  return (
    <>{/* <div className='row mx-0 solution-nav'>
          <div className='col-md-8 col-6 mx-0 d-flex p-2 align-items-center'>
            <h4 className='py-md-3 py-2 mb-0'>
              User Review
            </h4>
          </div>
          <div className='col-md-4 col-6 d-flex justify-content-end align-items-center'>
            <button
              onClick={() =>
                navigate("/users/review")
              }
              type='button'
              className='btn enroll-small-btn'
            >
              <small>Reviews List</small>
            </button>
          </div>
        </div> */}
      {false ?
        <div className='d-flex justify-content-center'><CustomLoader /></div>
        :
        <div className='row mx-0 d-flex justify-content-center'>
          <div className='col-7 border rounded-lg card-schadow p-3 my-4 p-4'>
            <div className='d-flex justify-content-center'><h2>User Review Form</h2></div>
            <form onSubmit={handleSubmit(onSubmit)}>
              <Field
                type='text'
                name='username'
                label='User '
                placeholder=''
                component={renderInputField}
                validate={[required]}
              />
              <Field
                name='category'
                label='Category'
                options={getOptions(userCategoryOptions, "category")}
                textField='category'
                placeholder={"Category"}
                component={renderSelectField}
                validate={required}
              />
              <Field
                name="review"
                component={renderInputField}
                validate={[required]}
                label="Review"
                type='textarea'
              />

              <div className='my-3' >
                <label>Rating</label>
                <CustomStarRating initialValue={rating} readonly={false} onRatingChange={setRating} />
              </div>

              <div className="form-group">
                <button type="submit" className="btn btn-primary" disabled={submitting}>
                  {isEdit ? "Save" : "Submit"}
                </button>
              </div>
            </form>
          </div>
        </div>
      }
    </>
  )
};

export default connect((state) => ({
  formValues: getFormValues("create-review")(state),
}))(
  reduxForm({
    form: "create-review",
  })(CreateReviewForm),
);


import React from 'react'
import { <PERSON> } from 'react-router-dom';
import { RiCheckboxCircleLine } from "react-icons/ri";

import ToolTip from '../../../../../components/sharedComponents/ToolTip';

const CourseCompleteDashboard = () => {
  
  return (
    <div className='text-center mt-5 complete-course-window'>
      <h4> Congratulation!</h4> 
      <p> <RiCheckboxCircleLine className="tickMark" />You have successfully completed the course </p>   
      <div className='row'>
        <div className="col-12 col-md-12 mt-5 ">
        <Link  to="/courses">
        <ToolTip title="Join For Courses" placement="top">
        <button className="btn-courses  col-md-6">
            <span>Go To Next Course</span>
        </button>
        </ToolTip>
        </Link>
        </div>
      </div>         
    </div>
  )
}

export default CourseCompleteDashboard